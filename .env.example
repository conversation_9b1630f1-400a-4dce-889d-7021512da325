# 量化套利系统环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# ================================
# 系统基础配置
# ================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
TIMEZONE=Asia/Shanghai

# ================================
# 数据库配置
# ================================
# SQLite数据库 (默认)
DATABASE_URL=sqlite:///quantitative_arbitrage.db

# PostgreSQL数据库 (可选)
# DATABASE_URL=postgresql://username:password@localhost:5432/arbitrage_db

# MongoDB数据库 (可选)
# MONGODB_URL=mongodb://localhost:27017/arbitrage_db

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# ================================
# 交易系统配置
# ================================
# 初始资金 (人民币)
INITIAL_CAPITAL=200000

# 仓位限制
MAX_POSITION_RATIO=0.7
EMERGENCY_POSITION_RATIO=0.85
MIN_POSITION_SIZE=1000
MAX_POSITION_SIZE=50000

# 风险管理
RISK_FREE_RATE=0.03
MAX_DRAWDOWN=0.1
VAR_CONFIDENCE=0.95
STOP_LOSS_RATIO=0.03

# ================================
# 策略配置
# ================================
# 配对交易策略
PAIRS_TRADING_ENABLED=true
PAIRS_LOOKBACK_PERIOD=60
PAIRS_ENTRY_THRESHOLD=2.0
PAIRS_EXIT_THRESHOLD=0.5

# 统计套利策略
STAT_ARB_ENABLED=true
STAT_ARB_WINDOW_SIZE=20
STAT_ARB_CONFIDENCE=0.95

# ML增强策略
ML_ENHANCED_ENABLED=true
ML_RETRAIN_FREQUENCY=30
ML_FEATURE_THRESHOLD=0.1

# 波动率套利策略
VOL_ARB_ENABLED=true
VOL_WINDOW=30
VOL_THRESHOLD=0.2

# 临时套利策略
EMERGENCY_ARB_ENABLED=true
EMERGENCY_TRIGGER_THRESHOLD=0.05

# ================================
# 数据源配置
# ================================
# 主要数据源: mock, tushare, yfinance, akshare
PRIMARY_DATA_SOURCE=mock
BACKUP_DATA_SOURCE=yfinance

# 数据更新频率 (秒)
DATA_UPDATE_FREQUENCY=2
MARKET_DATA_TIMEOUT=10

# Tushare配置 (需要注册获取token)
TUSHARE_TOKEN=your_tushare_token_here
TUSHARE_RETRY_COUNT=3

# Alpha Vantage配置 (可选)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Yahoo Finance配置
YFINANCE_TIMEOUT=10
YFINANCE_RETRY_COUNT=3

# ================================
# 监控和界面配置
# ================================
# 启用监控
ENABLE_MONITORING=true

# 界面端口配置
MAIN_APP_PORT=8503
ENHANCED_APP_PORT=8504
DYNAMIC_APP_PORT=8505

# 自动刷新配置
AUTO_REFRESH=true
REFRESH_INTERVAL=3
UI_UPDATE_FREQUENCY=2

# 界面主题
UI_THEME=light
UI_SIDEBAR_STATE=expanded

# ================================
# 日志配置
# ================================
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
LOG_FORMAT=detailed

# 日志文件配置
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/system.log
LOG_FILE_MAX_SIZE=10MB
LOG_FILE_BACKUP_COUNT=5
LOG_FILE_ROTATION=daily

# 控制台日志
CONSOLE_LOG_ENABLED=true
CONSOLE_LOG_LEVEL=INFO

# ================================
# 性能配置
# ================================
# 线程池配置
THREAD_POOL_SIZE=4
MAX_WORKERS=8

# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# 异步处理
ENABLE_ASYNC=true
ASYNC_TIMEOUT=30

# ================================
# 安全配置
# ================================
# API密钥加密
ENCRYPT_API_KEYS=false
ENCRYPTION_KEY=your_encryption_key_here

# 访问控制
ENABLE_AUTH=false
AUTH_USERNAME=admin
AUTH_PASSWORD=your_secure_password

# HTTPS配置 (生产环境)
ENABLE_HTTPS=false
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# ================================
# 通知配置
# ================================
# 邮件通知
EMAIL_ENABLED=false
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_TO=<EMAIL>

# 微信通知 (可选)
WECHAT_ENABLED=false
WECHAT_WEBHOOK_URL=your_wechat_webhook_url

# 钉钉通知 (可选)
DINGTALK_ENABLED=false
DINGTALK_WEBHOOK_URL=your_dingtalk_webhook_url

# ================================
# 云服务配置 (可选)
# ================================
# AWS配置
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_s3_bucket

# 阿里云配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_secret_key
ALIYUN_REGION=cn-hangzhou

# ================================
# 开发和调试配置
# ================================
# 开发模式
DEV_MODE=true
DEBUG_STRATEGIES=false
DEBUG_RISK_MANAGER=false
DEBUG_DATABASE=false

# 测试配置
ENABLE_TESTING=false
TEST_DATA_PATH=data/test
MOCK_DATA_ENABLED=true

# 性能分析
ENABLE_PROFILING=false
PROFILING_OUTPUT_PATH=logs/profiling

# ================================
# 备份和恢复配置
# ================================
# 自动备份
AUTO_BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=backups

# 数据导出
EXPORT_FORMAT=csv
EXPORT_PATH=exports

# ================================
# 监控和告警配置
# ================================
# 系统监控
SYSTEM_MONITORING_ENABLED=true
MONITORING_INTERVAL=60

# 性能告警阈值
CPU_ALERT_THRESHOLD=80
MEMORY_ALERT_THRESHOLD=85
DISK_ALERT_THRESHOLD=90

# 交易告警
LOSS_ALERT_THRESHOLD=0.05
DRAWDOWN_ALERT_THRESHOLD=0.08
POSITION_ALERT_THRESHOLD=0.8

# ================================
# 其他配置
# ================================
# 时区设置
TIMEZONE=Asia/Shanghai
DATE_FORMAT=%Y-%m-%d
TIME_FORMAT=%H:%M:%S
DATETIME_FORMAT=%Y-%m-%d %H:%M:%S

# 货币设置
BASE_CURRENCY=CNY
CURRENCY_PRECISION=2

# 语言设置
LANGUAGE=zh_CN
LOCALE=zh_CN.UTF-8

# 版本信息
SYSTEM_VERSION=4.0.0
API_VERSION=v1
BUILD_NUMBER=20240101
