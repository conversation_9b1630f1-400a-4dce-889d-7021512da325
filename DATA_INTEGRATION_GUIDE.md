# 📊 数据接入模块使用指南

## 🎯 模块概述

数据接入模块为量化套利系统提供了统一的数据接口，支持多种数据源类型，包括API接口和CSV文件数据，为后续接入实时行情数据预留了完整的接口。

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────┐
│                   数据接入模块架构                        │
├─────────────────────────────────────────────────────────┤
│  📊 增强数据管理器 (EnhancedDataManager)                 │
│  ├── 数据源管理器 (DataSourceManager)                    │
│  ├── 数据质量验证器 (DataValidator)                      │
│  ├── 数据缓存系统                                        │
│  └── 实时数据流                                          │
├─────────────────────────────────────────────────────────┤
│  🔌 数据源接口层                                         │
│  ├── 模拟数据源 (MockDataSource)                        │
│  ├── CSV数据源 (CSVDataSource)                          │
│  ├── API数据源 (TushareDataSource, YahooFinanceDataSource) │
│  └── 自定义数据源接口                                    │
├─────────────────────────────────────────────────────────┤
│  📈 数据标准化层                                         │
│  ├── MarketDataPoint (统一数据格式)                      │
│  ├── 数据转换器 (DataConverter)                          │
│  └── 数据验证器 (DataValidator)                          │
└─────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 基本使用

```python
from src.data.enhanced_data_manager import EnhancedDataManager

# 创建数据管理器
data_manager = EnhancedDataManager("config/data_sources.json")

# 获取实时数据
symbols = ['000001.SZ', '000002.SZ', '600000.SH']
realtime_data = data_manager.get_realtime_data(symbols)

for symbol, data in realtime_data.items():
    print(f"{symbol}: 价格={data.close:.2f}, 涨跌幅={data.change_pct:.2%}")
```

### 2. 配置文件设置

编辑 `config/data_sources.json`：

```json
{
  "cache_ttl": 60,
  "quality_threshold": 0.7,
  "stream": {
    "enabled": true,
    "update_interval": 2,
    "symbols": ["000001.SZ", "000002.SZ"]
  },
  "sources": {
    "mock": {"enabled": true, "priority": 3},
    "csv": {"enabled": false, "files": []},
    "api": {"enabled": false}
  }
}
```

## 📊 数据源类型

### 1. 模拟数据源 (MockDataSource)

**用途**: 测试和演示
**特点**: 
- ✅ 无需外部依赖
- ✅ 支持市场事件模拟
- ✅ 可配置价格趋势和波动率

```python
from src.data.mock_data_source import MockDataSource

# 创建模拟数据源
mock_source = MockDataSource()
mock_source.connect()

# 获取实时数据
data = mock_source.get_realtime_data(['000001.SZ'])

# 模拟市场事件
mock_source.simulate_market_event("crash", ['000001.SZ'])
mock_source.set_market_state("volatile", factor=2.0)
```

### 2. CSV数据源 (CSVDataSource)

**用途**: 历史数据回测和模拟
**特点**:
- ✅ 支持标准CSV格式
- ✅ 可配置数据模拟播放
- ✅ 支持多种时间频率

```python
from src.data.csv_data_source import CSVDataSourceFactory

# 从文件创建CSV数据源
csv_source = CSVDataSourceFactory.create_from_file(
    file_path="data/sample_stock_data.csv",
    symbols=['000001.SZ', '000002.SZ'],
    name="历史数据"
)

csv_source.connect()

# 启动数据模拟
csv_source.start_simulation(speed=2.0)  # 2倍速播放

# 获取历史数据
from src.data.data_source_interface import DataFrequency
hist_data = csv_source.get_historical_data(
    symbol='000001.SZ',
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 2),
    frequency=DataFrequency.MINUTE
)
```

**CSV文件格式要求**:
```csv
timestamp,symbol,open,high,low,close,volume,amount,change,change_pct
2024-01-01 09:30:00,000001.SZ,10.50,10.65,10.45,10.60,2500000,26500000,0.10,0.0095
```

### 3. API数据源

#### Tushare数据源

**用途**: A股实时和历史数据
**配置**:

```python
from src.data.api_data_source import APIDataSourceFactory

# 创建Tushare数据源
tushare_source = APIDataSourceFactory.create_tushare_source(
    api_key="your_tushare_token"
)

if tushare_source.connect():
    # 获取A股数据
    data = tushare_source.get_realtime_data(['000001.SZ', '600000.SH'])
```

**获取Token**: https://tushare.pro/register

#### Yahoo Finance数据源

**用途**: 全球股票数据
**配置**:

```python
# 创建Yahoo Finance数据源
yahoo_source = APIDataSourceFactory.create_yahoo_source()

if yahoo_source.connect():
    # 获取美股数据
    us_data = yahoo_source.get_realtime_data(['AAPL', 'GOOGL'])
    
    # 获取A股数据（需要正确的代码格式）
    a_data = yahoo_source.get_realtime_data(['000001.SZ'])
```

## 🔧 高级功能

### 1. 数据流订阅

```python
# 创建数据管理器
data_manager = EnhancedDataManager()

# 定义数据回调函数
def on_data_update(data):
    print(f"收到数据更新: {len(data)} 只股票")
    for symbol, point in data.items():
        print(f"  {symbol}: {point.close:.2f}")

# 订阅数据更新
data_manager.subscribe_data_updates(on_data_update)

# 启动数据流
symbols = ['000001.SZ', '000002.SZ']
data_manager.start_data_stream(symbols)

# 运行一段时间后停止
time.sleep(30)
data_manager.stop_data_stream()
```

### 2. 数据质量监控

```python
# 获取数据质量报告
quality_report = data_manager.get_quality_report()
print(f"平均数据质量: {quality_report['average_quality']:.2f}")
print(f"高质量数据占比: {quality_report['high_quality_count']}/{quality_report['total_symbols']}")

# 获取统计信息
stats = data_manager.get_statistics()
print(f"缓存命中率: {stats['cache_hit_rate']}")
print(f"API调用次数: {stats['api_calls']}")
```

### 3. 动态添加数据源

```python
# 添加CSV数据源
source_name = data_manager.add_csv_data_source(
    file_path="data/new_data.csv",
    symbols=['300750.SZ'],
    name="新数据源",
    simulate=True,
    speed=1.5
)

# 添加API数据源
data_manager.add_api_data_source(
    source_type="tushare",
    api_key="your_token"
)
```

### 4. 数据缓存控制

```python
# 使用缓存（默认）
cached_data = data_manager.get_realtime_data(symbols, use_cache=True)

# 强制刷新
fresh_data = data_manager.get_realtime_data(symbols, use_cache=False)

# 清空缓存
data_manager.clear_cache()
```

## 📋 配置详解

### 完整配置文件示例

```json
{
  "cache_ttl": 60,
  "quality_threshold": 0.7,
  "stream": {
    "enabled": true,
    "update_interval": 2,
    "auto_start": false,
    "symbols": ["000001.SZ", "000002.SZ"]
  },
  "sources": {
    "mock": {
      "enabled": true,
      "priority": 3,
      "auto_generation": true,
      "generation_interval": 2.0
    },
    "csv": {
      "enabled": true,
      "files": [
        {
          "name": "历史数据1",
          "path": "data/sample_stock_data.csv",
          "symbols": ["000001.SZ", "000002.SZ"],
          "simulate": true,
          "speed": 1.0
        }
      ]
    },
    "api": {
      "enabled": true,
      "tushare": {
        "enabled": true,
        "token": "your_tushare_token",
        "rate_limit": 0.2,
        "retry_count": 3
      },
      "yahoo": {
        "enabled": true,
        "timeout": 30,
        "retry_count": 3
      }
    }
  },
  "data_validation": {
    "enabled": true,
    "thresholds": {
      "min_price": 0.01,
      "max_price": 10000,
      "max_delay_seconds": 300
    }
  },
  "fallback": {
    "enabled": true,
    "strategy": "cascade",
    "sources_order": ["api", "csv", "mock"]
  }
}
```

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `cache_ttl` | 缓存生存时间(秒) | 60 |
| `quality_threshold` | 数据质量阈值 | 0.7 |
| `stream.update_interval` | 数据流更新间隔(秒) | 2 |
| `sources.*.priority` | 数据源优先级(数字越小优先级越高) | - |
| `fallback.strategy` | 故障转移策略 | "cascade" |

## 🔌 扩展接口

### 自定义数据源

```python
from src.data.data_source_interface import DataSourceInterface, DataSourceConfig

class CustomDataSource(DataSourceInterface):
    def connect(self) -> bool:
        # 实现连接逻辑
        pass
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        # 实现实时数据获取
        pass
    
    def get_historical_data(self, symbol: str, start_date: datetime, 
                          end_date: datetime, frequency: DataFrequency) -> pd.DataFrame:
        # 实现历史数据获取
        pass

# 注册自定义数据源
custom_source = CustomDataSource(config)
data_manager.add_data_source("custom", custom_source)
```

### WebSocket数据源接口

```python
# 预留的WebSocket接口
class WebSocketDataSource(DataSourceInterface):
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.ws_url = config.extra_params.get('ws_url')
        self.auth_token = config.extra_params.get('auth_token')
    
    def connect(self) -> bool:
        # WebSocket连接实现
        pass
    
    def _on_message(self, message):
        # 处理WebSocket消息
        pass
```

## 📊 性能优化

### 1. 缓存策略

- **L1缓存**: 内存缓存，TTL=60秒
- **数据预取**: 批量获取相关股票数据
- **智能刷新**: 基于数据变化频率调整刷新策略

### 2. 并发处理

```python
# 异步数据获取
import asyncio

async def get_multiple_sources_data(symbols):
    tasks = []
    for source in data_manager.source_manager.data_sources.values():
        if source.is_available():
            task = asyncio.create_task(source.get_realtime_data(symbols))
            tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

### 3. 数据压缩

```python
# 数据压缩存储
import pickle
import gzip

def compress_data(data):
    return gzip.compress(pickle.dumps(data))

def decompress_data(compressed_data):
    return pickle.loads(gzip.decompress(compressed_data))
```

## 🚨 错误处理

### 常见错误及解决方案

| 错误类型 | 原因 | 解决方案 |
|----------|------|----------|
| `ConnectionError` | 网络连接失败 | 检查网络连接，重试机制 |
| `AuthenticationError` | API认证失败 | 检查API密钥配置 |
| `RateLimitError` | API调用频率超限 | 调整调用频率，使用缓存 |
| `DataQualityError` | 数据质量不达标 | 启用数据验证，使用备用数据源 |

### 错误监控

```python
# 获取错误统计
error_stats = data_manager.get_statistics()
if error_stats['errors'] > 10:
    print("⚠️ 错误率过高，建议检查数据源状态")

# 数据源状态检查
sources_status = data_manager.get_data_sources_status()
for name, status in sources_status.items():
    if not status['available']:
        print(f"❌ 数据源 {name} 不可用")
```

## 🧪 测试和验证

### 运行测试

```bash
# 运行完整测试
python test_data_sources.py

# 测试特定数据源
python -c "
from src.data.enhanced_data_manager import EnhancedDataManager
dm = EnhancedDataManager()
data = dm.get_realtime_data(['000001.SZ'])
print(data)
"
```

### 性能测试

```python
import time

# 测试数据获取性能
start_time = time.time()
data = data_manager.get_realtime_data(['000001.SZ'] * 100)
end_time = time.time()

print(f"获取100只股票数据耗时: {end_time - start_time:.3f}秒")
```

## 📚 最佳实践

### 1. 数据源配置

- **优先级设置**: API数据源 > CSV数据源 > 模拟数据源
- **故障转移**: 配置多个备用数据源
- **缓存策略**: 根据数据更新频率调整缓存TTL

### 2. 性能优化

- **批量获取**: 一次性获取多只股票数据
- **异步处理**: 使用异步方式处理多个数据源
- **数据预热**: 系统启动时预加载常用数据

### 3. 监控告警

- **数据质量监控**: 设置质量阈值告警
- **性能监控**: 监控API响应时间
- **错误率监控**: 设置错误率告警阈值

## 🔮 未来扩展

### 计划支持的数据源

- **Wind API**: 万得金融数据
- **Bloomberg API**: 彭博数据终端
- **东方财富API**: 东方财富数据接口
- **同花顺API**: 同花顺数据接口
- **自建数据库**: PostgreSQL/MongoDB数据源

### 功能增强

- **实时推送**: WebSocket实时数据推送
- **数据融合**: 多数据源数据融合算法
- **智能路由**: 基于延迟和质量的智能数据源路由
- **数据湖**: 大数据存储和分析能力

## 📞 技术支持

如有问题，请参考：
- 📖 详细文档: `DEPLOYMENT_GUIDE.md`
- 🧪 测试脚本: `test_data_sources.py`
- ⚙️ 配置示例: `config/data_sources.json`
- 📄 示例数据: `data/sample_stock_data.csv`

---

**数据接入模块为量化套利系统提供了强大而灵活的数据基础设施，支持多种数据源和实时数据流，为策略开发和实盘交易提供了可靠的数据保障。** 🚀
