# 📦 量化套利系统部署指南

## 🎯 系统概述

本项目是一个专业级的量化套利交易系统，支持多种套利策略、实时风险管理、智能机会发现和可视化监控。

## 📋 系统要求

### 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 8GB以上，推荐16GB
- **存储**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: macOS 10.15+, Ubuntu 18.04+, Windows 10+
- **Python**: 3.8 - 3.11 (推荐3.9)
- **数据库**: SQLite (内置) 或 PostgreSQL (可选)

## 🔧 依赖管理

### 核心依赖 (requirements.txt)

```txt
# 数据处理和科学计算
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 机器学习
scikit-learn>=1.0.0
joblib>=1.1.0

# 数据可视化
matplotlib>=3.5.0
plotly>=5.0.0
seaborn>=0.11.0

# Web界面
streamlit>=1.28.0
dash>=2.10.0

# 数据库
sqlite3  # Python内置
sqlalchemy>=1.4.0

# 日志和配置
loguru>=0.6.0
pyyaml>=6.0
python-dotenv>=0.19.0

# 网络和API
requests>=2.28.0
websocket-client>=1.3.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2022.1

# 数学和统计
statsmodels>=0.13.0
arch>=5.3.0

# 并发处理
asyncio  # Python内置
threading  # Python内置
multiprocessing  # Python内置

# 开发和测试
pytest>=7.0.0
pytest-cov>=3.0.0
black>=22.0.0
flake8>=4.0.0
```

### 可选依赖

```txt
# 高级数据源 (可选)
yfinance>=0.1.70
tushare>=1.2.89
akshare>=1.8.0

# 数据库扩展 (可选)
psycopg2-binary>=2.9.0  # PostgreSQL
pymongo>=4.0.0          # MongoDB

# 性能优化 (可选)
numba>=0.56.0
cython>=0.29.0

# 云服务 (可选)
boto3>=1.24.0           # AWS
azure-storage-blob>=12.0.0  # Azure
```

## ⚙️ 配置管理

### 1. 环境配置文件 (.env)

```bash
# 创建环境配置文件
cp .env.example .env
```

**.env 文件内容：**
```bash
# 系统配置
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///quantitative_arbitrage.db
# DATABASE_URL=postgresql://user:password@localhost:5432/arbitrage_db

# 交易配置
INITIAL_CAPITAL=200000
MAX_POSITION_RATIO=0.7
EMERGENCY_POSITION_RATIO=0.85
RISK_FREE_RATE=0.03

# API配置 (可选)
TUSHARE_TOKEN=your_tushare_token
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# 监控配置
ENABLE_MONITORING=true
MONITORING_PORT=8503
UPDATE_INTERVAL=3

# 邮件通知 (可选)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
```

### 2. 系统配置文件 (config/config.yaml)

```yaml
# 系统配置
system:
  name: "量化套利系统"
  version: "4.0.0"
  timezone: "Asia/Shanghai"
  
# 交易配置
trading:
  initial_capital: 200000
  max_position_ratio: 0.7
  emergency_position_ratio: 0.85
  min_trade_amount: 1000
  max_trade_amount: 50000
  
# 策略配置
strategies:
  pairs_trading:
    enabled: true
    lookback_period: 60
    entry_threshold: 2.0
    exit_threshold: 0.5
    
  statistical_arbitrage:
    enabled: true
    window_size: 20
    confidence_level: 0.95
    
  ml_enhanced:
    enabled: true
    model_retrain_frequency: 30
    feature_importance_threshold: 0.1
    
  volatility_arbitrage:
    enabled: true
    vol_window: 30
    vol_threshold: 0.2

# 风险管理
risk_management:
  max_drawdown: 0.1
  var_confidence: 0.95
  stress_test_enabled: true
  
# 数据源配置
data_sources:
  primary: "mock"  # mock, tushare, yfinance
  backup: "yfinance"
  update_frequency: 2  # 秒
  
# 监控配置
monitoring:
  enabled: true
  port: 8503
  auto_refresh: true
  refresh_interval: 3
```

### 3. 日志配置 (config/logging.yaml)

```yaml
version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} - {message}"
  
  detailed:
    format: "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {process}:{thread} | {name}:{function}:{line} - {message}"

handlers:
  console:
    class: loguru._logger.Logger
    level: INFO
    
  file:
    class: loguru._logger.Logger
    level: DEBUG
    rotation: "10 MB"
    retention: "30 days"

loggers:
  "":
    level: INFO
    handlers: [console, file]
    propagate: false
    
  strategies:
    level: DEBUG
    handlers: [file]
    
  risk:
    level: WARNING
    handlers: [console, file]
```

## 🚀 安装和部署

### 1. 克隆项目

```bash
# 克隆项目
git clone https://github.com/your-repo/quantitative_arbitrage.git
cd quantitative_arbitrage

# 或者下载ZIP包并解压
```

### 2. 创建虚拟环境

```bash
# 使用venv创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
# macOS/Linux:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

### 3. 安装依赖

```bash
# 升级pip
pip install --upgrade pip

# 安装核心依赖
pip install -r requirements.txt

# 安装可选依赖 (根据需要)
pip install -r requirements-optional.txt
```

### 4. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器

# 创建必要目录
mkdir -p logs data config backups
```

### 5. 数据库初始化

```bash
# 运行数据库迁移脚本
python scripts/init_database.py

# 或者手动初始化
python -c "
from src.database.signal_database import SignalDatabase
db = SignalDatabase('quantitative_arbitrage.db')
print('数据库初始化完成')
"
```

## 🔄 数据库迁移

### 迁移脚本 (scripts/migrate_database.py)

```python
#!/usr/bin/env python3
"""
数据库迁移脚本
"""

import os
import sys
import sqlite3
from datetime import datetime

def migrate_v1_to_v2():
    """从v1.0迁移到v2.0"""
    print("执行v1.0到v2.0迁移...")
    # 迁移逻辑
    pass

def migrate_v2_to_v3():
    """从v2.0迁移到v3.0"""
    print("执行v2.0到v3.0迁移...")
    # 迁移逻辑
    pass

def backup_database(db_path):
    """备份数据库"""
    backup_path = f"{db_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    import shutil
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")
    return backup_path

if __name__ == "__main__":
    # 执行迁移
    pass
```

### 迁移命令

```bash
# 备份当前数据库
python scripts/backup_database.py

# 执行迁移
python scripts/migrate_database.py --from-version 1.0 --to-version 2.0

# 验证迁移
python scripts/verify_migration.py
```

## 🎮 启动和停止

### 1. 系统启动脚本 (scripts/start_system.sh)

```bash
#!/bin/bash

# 量化套利系统启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 启动量化套利系统${NC}"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安装${NC}"
    exit 1
fi

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo -e "${YELLOW}⚠️  建议在虚拟环境中运行${NC}"
fi

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import streamlit, pandas, numpy, plotly" || {
    echo -e "${RED}❌ 依赖检查失败，请运行: pip install -r requirements.txt${NC}"
    exit 1
}

# 检查配置文件
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env文件不存在，使用默认配置${NC}"
    cp .env.example .env
fi

# 检查数据库
echo "🗄️  检查数据库..."
python3 -c "
from src.database.signal_database import SignalDatabase
try:
    db = SignalDatabase('quantitative_arbitrage.db')
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"

# 启动系统
echo -e "${GREEN}🎯 启动系统组件...${NC}"

# 启动主界面
echo "📊 启动主界面..."
streamlit run realtime_integrated_app.py --server.port 8503 &
MAIN_PID=$!

# 启动增强界面 (可选)
echo "🔧 启动增强界面..."
streamlit run enhanced_visual_app.py --server.port 8504 &
ENHANCED_PID=$!

# 启动动态界面 (可选)
echo "⚡ 启动动态界面..."
streamlit run dynamic_visual_app.py --server.port 8505 &
DYNAMIC_PID=$!

# 保存PID
echo $MAIN_PID > .main.pid
echo $ENHANCED_PID > .enhanced.pid
echo $DYNAMIC_PID > .dynamic.pid

echo -e "${GREEN}✅ 系统启动完成${NC}"
echo ""
echo "📱 访问地址:"
echo "  主界面:   http://localhost:8503"
echo "  增强界面: http://localhost:8504"
echo "  动态界面: http://localhost:8505"
echo ""
echo "🛑 停止系统: ./scripts/stop_system.sh"
```

### 2. 系统停止脚本 (scripts/stop_system.sh)

```bash
#!/bin/bash

# 量化套利系统停止脚本

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}🛑 停止量化套利系统${NC}"

# 停止主界面
if [ -f ".main.pid" ]; then
    MAIN_PID=$(cat .main.pid)
    if kill -0 $MAIN_PID 2>/dev/null; then
        echo "🔴 停止主界面 (PID: $MAIN_PID)"
        kill $MAIN_PID
    fi
    rm -f .main.pid
fi

# 停止增强界面
if [ -f ".enhanced.pid" ]; then
    ENHANCED_PID=$(cat .enhanced.pid)
    if kill -0 $ENHANCED_PID 2>/dev/null; then
        echo "🔴 停止增强界面 (PID: $ENHANCED_PID)"
        kill $ENHANCED_PID
    fi
    rm -f .enhanced.pid
fi

# 停止动态界面
if [ -f ".dynamic.pid" ]; then
    DYNAMIC_PID=$(cat .dynamic.pid)
    if kill -0 $DYNAMIC_PID 2>/dev/null; then
        echo "🔴 停止动态界面 (PID: $DYNAMIC_PID)"
        kill $DYNAMIC_PID
    fi
    rm -f .dynamic.pid
fi

# 清理Streamlit进程
echo "🧹 清理残留进程..."
pkill -f "streamlit run" 2>/dev/null || true

echo -e "${GREEN}✅ 系统已停止${NC}"
```

### 3. 系统状态检查 (scripts/check_status.sh)

```bash
#!/bin/bash

# 系统状态检查脚本

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}📊 量化套利系统状态检查${NC}"
echo ""

# 检查进程状态
echo "🔍 进程状态:"
if pgrep -f "streamlit run realtime_integrated_app.py" > /dev/null; then
    echo -e "  主界面:   ${GREEN}✅ 运行中${NC}"
else
    echo -e "  主界面:   ${RED}❌ 已停止${NC}"
fi

if pgrep -f "streamlit run enhanced_visual_app.py" > /dev/null; then
    echo -e "  增强界面: ${GREEN}✅ 运行中${NC}"
else
    echo -e "  增强界面: ${RED}❌ 已停止${NC}"
fi

if pgrep -f "streamlit run dynamic_visual_app.py" > /dev/null; then
    echo -e "  动态界面: ${GREEN}✅ 运行中${NC}"
else
    echo -e "  动态界面: ${RED}❌ 已停止${NC}"
fi

echo ""

# 检查端口占用
echo "🌐 端口状态:"
for port in 8503 8504 8505; do
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "  端口 $port: ${GREEN}✅ 已占用${NC}"
    else
        echo -e "  端口 $port: ${RED}❌ 空闲${NC}"
    fi
done

echo ""

# 检查数据库
echo "🗄️  数据库状态:"
python3 -c "
from src.database.signal_database import SignalDatabase
try:
    db = SignalDatabase('quantitative_arbitrage.db')
    info = db.get_database_info()
    print(f'  ✅ 连接正常 - {info[\"total_records\"]} 条记录')
except Exception as e:
    print(f'  ❌ 连接失败: {e}')
"

echo ""

# 检查日志
echo "📝 最近日志:"
if [ -f "logs/system.log" ]; then
    tail -5 logs/system.log
else
    echo "  ⚠️  日志文件不存在"
fi
```

### 4. 快速启动命令

```bash
# 给脚本添加执行权限
chmod +x scripts/*.sh

# 启动系统
./scripts/start_system.sh

# 检查状态
./scripts/check_status.sh

# 停止系统
./scripts/stop_system.sh
```

## 🔧 开发模式

### 开发环境启动

```bash
# 开发模式启动 (单个界面)
streamlit run realtime_integrated_app.py --server.port 8503 --server.runOnSave true

# 或使用Python直接运行
python -m streamlit run realtime_integrated_app.py
```

### 调试模式

```bash
# 设置调试环境变量
export DEBUG=true
export LOG_LEVEL=DEBUG

# 启动调试模式
python debug_system.py
```

## 📊 监控和维护

### 系统监控

```bash
# 查看系统资源使用
htop

# 查看端口占用
netstat -tulpn | grep :850

# 查看日志
tail -f logs/system.log
```

### 定期维护

```bash
# 数据库备份 (建议每日执行)
python scripts/backup_database.py

# 日志清理 (建议每周执行)
python scripts/cleanup_logs.py

# 性能报告 (建议每月执行)
python scripts/generate_performance_report.py
```

这个部署指南提供了完整的项目依赖、配置、迁移、启动和停止说明。需要我继续添加更多详细内容吗？
