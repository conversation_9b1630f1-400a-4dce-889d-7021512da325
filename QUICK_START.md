# 🚀 量化套利系统快速启动指南

## 📋 系统要求

- **Python**: 3.8 - 3.11 (推荐 3.9)
- **操作系统**: macOS 10.15+, Ubuntu 18.04+, Windows 10+
- **内存**: 8GB+ (推荐 16GB)
- **存储**: 20GB 可用空间

## ⚡ 5分钟快速启动

### 1. 下载项目
```bash
# 克隆项目 (如果使用Git)
git clone https://github.com/your-repo/quantitative_arbitrage.git
cd quantitative_arbitrage

# 或者下载ZIP包并解压到项目目录
```

### 2. 创建虚拟环境 (推荐)
```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
# macOS/Linux:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

### 3. 安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

### 4. 初始化系统
```bash
# 运行初始化脚本
python scripts/init_database.py

# 给脚本添加执行权限 (macOS/Linux)
chmod +x scripts/*.sh
```

### 5. 启动系统
```bash
# 启动系统
./scripts/start_system.sh

# Windows用户可以直接运行:
# streamlit run realtime_integrated_app.py --server.port 8503
```

### 6. 访问界面
- **主界面**: http://localhost:8503
- **增强界面**: http://localhost:8504 (可选)
- **动态界面**: http://localhost:8505 (可选)

## 🎮 基本操作

### 启动系统
```bash
./scripts/start_system.sh
```

### 检查状态
```bash
./scripts/check_status.sh
```

### 停止系统
```bash
./scripts/stop_system.sh
```

## 🔧 常见问题

### Q1: 依赖安装失败
```bash
# 尝试使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者分别安装核心依赖
pip install streamlit pandas numpy plotly loguru
```

### Q2: 端口被占用
```bash
# 查看端口占用
lsof -i :8503

# 终止占用进程
kill -9 <PID>

# 或者使用不同端口启动
streamlit run realtime_integrated_app.py --server.port 8506
```

### Q3: 权限问题 (macOS/Linux)
```bash
# 给脚本添加执行权限
chmod +x scripts/*.sh

# 如果仍有问题，直接运行Python
python -m streamlit run realtime_integrated_app.py
```

### Q4: 数据库问题
```bash
# 重新初始化数据库
python scripts/init_database.py

# 或者删除数据库文件重新创建
rm quantitative_arbitrage.db
python scripts/init_database.py
```

## 📊 界面功能

### 主界面 (端口 8503)
- ✅ 实时市场监控
- ✅ 机会警报系统
- ✅ 策略性能分析
- ✅ 信号时间线
- ✅ 系统控制面板

### 操作步骤
1. 点击侧边栏的 "🚀 启动" 按钮
2. 观察实时数据更新
3. 查看机会警报
4. 分析策略性能
5. 使用 "🔍 强制扫描" 手动触发分析

## 🛠️ 配置说明

### 环境配置 (.env)
```bash
# 复制示例配置
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

### 主要配置项
```bash
# 初始资金
INITIAL_CAPITAL=200000

# 仓位限制
MAX_POSITION_RATIO=0.7

# 数据更新频率
DATA_UPDATE_FREQUENCY=2

# 界面刷新间隔
REFRESH_INTERVAL=3
```

## 📈 系统特性

### 🤖 自动化功能
- **实时数据处理**: 2秒间隔自动获取市场数据
- **多策略并行**: 同时运行5种不同的套利策略
- **智能机会发现**: AI增强的套利机会识别
- **动态风险管理**: 自适应仓位控制和风险监控
- **实时界面更新**: 3秒自动刷新的可视化界面

### 📊 策略类型
1. **配对交易**: 基于协整关系的配对套利
2. **统计套利**: 基于统计模型的套利策略
3. **ML增强**: 机器学习增强的预测模型
4. **波动率套利**: 基于隐含波动率的套利
5. **临时套利**: 市场异常情况下的紧急套利

### ⚠️ 风险管理
- **动态仓位控制**: 正常70%，紧急85%
- **实时风险监控**: VaR、最大回撤、夏普比率
- **自动止损**: 3%止损，智能止盈
- **市场状况检测**: 爆跌、爆涨、高波动检测

## 📚 进阶使用

### 自定义配置
```bash
# 编辑系统配置
nano config/config.yaml

# 编辑日志配置
nano config/logging.yaml
```

### 数据备份
```bash
# 手动备份数据库
cp quantitative_arbitrage.db backups/backup_$(date +%Y%m%d).db

# 自动备份 (可设置定时任务)
python scripts/backup_database.py
```

### 性能监控
```bash
# 查看系统资源
htop  # Linux
top   # macOS

# 查看日志
tail -f logs/main_app.log
```

## 🔄 更新和维护

### 系统更新
```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade

# 重新初始化 (如有必要)
python scripts/init_database.py
```

### 定期维护
```bash
# 清理日志 (建议每周)
python scripts/cleanup_logs.py

# 数据库优化 (建议每月)
python scripts/optimize_database.py

# 性能报告 (建议每月)
python scripts/generate_performance_report.py
```

## 🆘 获取帮助

### 日志查看
```bash
# 查看主应用日志
tail -f logs/main_app.log

# 查看系统事件日志
tail -f logs/system_events.log

# 查看错误日志
grep ERROR logs/*.log
```

### 调试模式
```bash
# 设置调试环境
export DEBUG=true
export LOG_LEVEL=DEBUG

# 启动调试模式
python debug_system.py
```

### 联系支持
- 📧 邮箱: <EMAIL>
- 📱 微信: quantitative_support
- 🌐 文档: https://docs.quantitative-arbitrage.com

## ⚠️ 重要提示

1. **投资风险**: 本系统仅供学习研究使用，实际投资有风险，请谨慎操作
2. **数据准确性**: 系统使用模拟数据，实际使用需要接入真实数据源
3. **系统稳定性**: 建议在稳定的网络环境下运行
4. **资源消耗**: 系统会占用一定的CPU和内存资源
5. **定期备份**: 建议定期备份重要数据

## 🎉 开始使用

现在您已经了解了快速启动的所有步骤，让我们开始体验专业级的量化套利系统吧！

```bash
# 一键启动
./scripts/start_system.sh

# 访问主界面
open http://localhost:8503
```

祝您使用愉快！ 🚀
