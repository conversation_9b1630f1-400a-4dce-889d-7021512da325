# 🚀 量化套利系统 v4.0

## 📊 项目概述

专业级量化套利系统，集成实时数据接入、多策略套利、智能风险管理和可视化监控。

## ✨ 核心功能

- **🔄 实时数据接入**: 支持API和CSV文件数据源
- **🤖 多策略套利**: 配对交易、统计套利、ML增强等
- **🛡️ 智能风险管理**: 动态仓位控制和风险监控
- **📊 可视化监控**: 实时界面和数据源管理
- **⚡ 自动化执行**: 全自动策略发现和机会识别

## 🚀 快速启动

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python scripts/init_database.py
```

### 2. 启动系统
```bash
# 启动完整系统
./scripts/start_system.sh

# 或手动启动主界面
streamlit run realtime_integrated_app.py --server.port 8503
```

### 3. 访问界面
- **🚀 统一界面**: http://localhost:8506 (全功能集成界面 - 推荐)
- **📊 原主界面**: http://localhost:8503 (实时集成套利系统)
- **🔧 增强界面**: http://localhost:8504 (风险控制和策略比较)
- **⚡ 动态界面**: http://localhost:8505 (动态资金池和临时套利)

## 📁 项目结构

```
quantitative_arbitrage/
├── 🚀 unified_arbitrage_app.py      # 统一界面 - 全功能集成 (推荐)
├── 📱 realtime_integrated_app.py    # 原主应用 - 实时集成套利系统
├── 🔧 enhanced_visual_app.py        # 增强界面 - 风险控制
├── ⚡ dynamic_visual_app.py         # 动态界面 - 临时套利
├── 📊 src/                          # 核心源码
│   ├── core/                        # 核心协调器
│   ├── data/                        # 数据接入模块
│   ├── strategies/                  # 策略模块
│   ├── risk/                        # 风险管理
│   └── database/                    # 数据库模块
├── 🔧 scripts/                      # 管理脚本
├── ⚙️ config/                       # 配置文件
├── 📚 docs/                         # 项目文档
├── 🧪 tests/                        # 测试文件
└── 📄 data/                         # 数据文件
```

## 📚 文档

- [部署指南](DEPLOYMENT_GUIDE.md)
- [数据接入指南](DATA_INTEGRATION_GUIDE.md)
- [快速开始](QUICK_START.md)

## 🧪 测试

```bash
# 运行数据源测试
python tests/test_data_sources.py

# 运行系统测试
python tests/test_system.py
```

## 🛠️ 管理命令

```bash
# 检查系统状态
./scripts/check_status.sh

# 停止系统
./scripts/stop_system.sh

# 查看日志
tail -f logs/main_app.log
```

## ⚠️ 免责声明

本系统仅供学习研究使用，投资有风险，入市需谨慎。

## 📞 技术支持

如有问题，请查看docs目录下的详细文档。
