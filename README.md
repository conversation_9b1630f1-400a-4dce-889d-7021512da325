# 🚀 量化套利系统

基于随机微积分方程的沪深股市量化套利系统，实现实时行业数据输入和交易信号输出。

## ✨ 核心特性

### 📊 随机微积分模型
- **Black-Scholes模型**: 经典几何布朗运动股价建模
- **Heston模型**: 随机波动率模型，更真实的市场建模
- **CIR模型**: Cox-Ingersoll-Ross利率模型
- **跳跃扩散模型**: Merton跳跃扩散过程

### 🏭 实时数据处理
- **多数据源支持**: Tushare、AKShare、YFinance
- **实时行业数据**: 自动获取行业板块数据和个股表现
- **数据缓存机制**: 高效的数据存储和检索
- **WebSocket支持**: 实时数据流处理

### 💰 套利策略
- **配对交易**: 基于协整关系的统计套利
- **统计套利**: 行业内个股分化套利
- **波动率套利**: 隐含波动率与历史波动率差异套利
- **期现套利**: 期货与现货价差套利

### 🛡️ 风险管理
- **仓位控制**: 单只股票最大仓位限制
- **止损机制**: 动态止损和固定止损
- **资金管理**: 凯利公式优化仓位分配
- **实时监控**: 实时PnL和风险指标监控

### 📋 沪深交易规则
- **T+1交易制度**: 符合A股交易规则
- **涨跌停限制**: 主板±10%，创业板/科创板±20%
- **交易时间**: 9:30-11:30, 13:00-15:00
- **最小变动单位**: 0.01元
- **交易费用计算**: 佣金、印花税、过户费

## 🏗️ 项目结构

```
quantitative_arbitrage/
├── src/
│   ├── models/
│   │   └── stochastic_models.py    # 随机微积分模型
│   ├── strategies/
│   │   └── arbitrage_strategies.py # 套利策略
│   ├── data/
│   │   └── data_provider.py        # 数据提供者
│   ├── trading/
│   │   └── market_rules.py         # 市场交易规则
│   ├── utils/
│   │   └── risk_management.py      # 风险管理
│   └── arbitrage_engine.py         # 核心套利引擎
├── tests/                          # 单元测试
├── config/                         # 配置文件
├── data/                          # 数据存储
├── logs/                          # 日志文件
├── app.py                         # Streamlit Web界面
├── demo.py                        # 命令行演示
├── requirements.txt               # 依赖包
└── README.md                      # 项目文档
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd quantitative_arbitrage

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

```bash
# 复制配置文件
cp config/config.example.yaml config/config.yaml

# 编辑配置文件，添加API密钥
vim config/config.yaml
```

### 3. 运行演示

```bash
# 命令行演示
python demo.py

# Web界面
streamlit run app.py
```

## 📈 使用示例

### 基础使用

```python
from src.arbitrage_engine import ArbitrageEngine

# 创建套利引擎
engine = ArbitrageEngine(initial_capital=1000000)

# 添加信号回调
def signal_handler(signal):
    print(f"收到信号: {signal.strategy_name}")
    print(f"交易对: {signal.symbol1} / {signal.symbol2}")
    print(f"信号类型: {signal.signal_type}")

engine.add_signal_callback(signal_handler)

# 启动引擎
engine.start()

# 运行一段时间后停止
import time
time.sleep(60)
engine.stop()
```

### 随机微积分模型

```python
from src.models.stochastic_models import BlackScholesModel, BSParameters

# 创建Black-Scholes模型
params = BSParameters(S0=100, mu=0.05, sigma=0.2, r=0.03, T=1.0)
model = BlackScholesModel(params)

# 模拟股价路径
paths = model.simulate(n_paths=1000, n_steps=252)

# 期权定价
call_price = model.option_price(K=105, option_type="call")
print(f"看涨期权价格: {call_price:.4f}")

# 计算希腊字母
greeks = model.greeks(K=105, option_type="call")
print(f"Delta: {greeks['delta']:.4f}")
```

### 配对交易策略

```python
from src.arbitrage_engine import PairTradingStrategy

# 创建配对交易策略
strategy = PairTradingStrategy(
    lookback_period=60,
    entry_threshold=2.0,
    exit_threshold=0.5
)

# 生成交易信号
signal = strategy.generate_signal("000001.SZ", "000002.SZ", 10.5, 12.3)
if signal:
    print(f"信号类型: {signal.signal_type}")
    print(f"置信度: {signal.confidence:.2f}")
```

## 🔧 配置说明

### API配置

```yaml
# config/config.yaml
data_sources:
  tushare:
    token: "your_tushare_token"
    enabled: true
  
  akshare:
    enabled: true
  
  yfinance:
    enabled: false

trading:
  initial_capital: 1000000
  max_position_ratio: 0.1
  max_daily_loss: 0.05
  
strategies:
  pair_trading:
    lookback_period: 60
    entry_threshold: 2.0
    exit_threshold: 0.5
  
  statistical_arbitrage:
    industry_threshold: 0.05
    individual_threshold: 0.03
```

## 📊 性能指标

系统实时计算并显示以下性能指标：

- **总收益率**: 累计收益率
- **夏普比率**: 风险调整后收益
- **最大回撤**: 最大亏损幅度
- **胜率**: 盈利交易占比
- **平均持仓时间**: 交易持续时间
- **日均交易次数**: 交易频率

## 🛡️ 风险提示

1. **市场风险**: 股票价格波动可能导致损失
2. **模型风险**: 数学模型可能不完全反映市场现实
3. **技术风险**: 系统故障可能影响交易执行
4. **流动性风险**: 部分股票可能存在流动性不足
5. **监管风险**: 交易规则变化可能影响策略有效性

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## �� 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>

---

⚠️ **免责声明**: 本系统仅供学习和研究使用，不构成投资建议。实际交易请谨慎评估风险。
