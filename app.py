"""
量化套利系统 Web 界面
实时显示行业数据输入和交易信号输出
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import threading
from src.arbitrage_engine import ArbitrageEngine, ArbitrageSignal

# 页面配置
st.set_page_config(
    page_title="量化套利系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化会话状态
if 'engine' not in st.session_state:
    st.session_state.engine = ArbitrageEngine(initial_capital=1000000)
    st.session_state.signals = []
    st.session_state.running = False

def signal_callback(signal: ArbitrageSignal):
    """信号回调函数"""
    st.session_state.signals.append(signal)
    # 保持最近100个信号
    if len(st.session_state.signals) > 100:
        st.session_state.signals = st.session_state.signals[-100:]

# 添加回调
st.session_state.engine.add_signal_callback(signal_callback)

# 主标题
st.title("🚀 量化套利系统")
st.markdown("---")

# 侧边栏控制
with st.sidebar:
    st.header("系统控制")
    
    if st.button("启动系统", type="primary"):
        if not st.session_state.running:
            st.session_state.engine.start()
            st.session_state.running = True
            st.success("系统已启动")
    
    if st.button("停止系统"):
        if st.session_state.running:
            st.session_state.engine.stop()
            st.session_state.running = False
            st.success("系统已停止")
    
    st.markdown("---")
    st.header("系统状态")
    status = "🟢 运行中" if st.session_state.running else "🔴 已停止"
    st.markdown(f"**状态**: {status}")
    st.markdown(f"**信号数量**: {len(st.session_state.signals)}")
    
    st.markdown("---")
    st.header("策略参数")
    entry_threshold = st.slider("入场阈值", 1.0, 3.0, 2.0, 0.1)
    confidence_filter = st.slider("置信度过滤", 0.0, 1.0, 0.6, 0.1)

# 主界面
col1, col2 = st.columns([2, 1])

with col1:
    st.header("📊 实时交易信号")
    
    # 信号表格
    if st.session_state.signals:
        # 过滤信号
        filtered_signals = [s for s in st.session_state.signals if s.confidence >= confidence_filter]
        
        if filtered_signals:
            # 转换为DataFrame
            signal_data = []
            for signal in filtered_signals[-20:]:  # 显示最近20个信号
                signal_data.append({
                    "时间": signal.timestamp.strftime("%H:%M:%S"),
                    "策略": signal.strategy_name,
                    "股票1": signal.symbol1,
                    "股票2": signal.symbol2,
                    "信号类型": signal.signal_type,
                    "置信度": f"{signal.confidence:.2f}",
                    "预期收益": f"{signal.expected_return:.2%}",
                    "风险等级": signal.risk_level,
                    "价格1": f"{signal.entry_price1:.2f}",
                    "价格2": f"{signal.entry_price2:.2f}"
                })
            
            df = pd.DataFrame(signal_data)
            
            # 根据信号类型着色
            def color_signal_type(val):
                if val == "LONG_SHORT":
                    return "background-color: #d4edda"
                elif val == "SHORT_LONG":
                    return "background-color: #f8d7da"
                else:
                    return "background-color: #fff3cd"
            
            styled_df = df.style.applymap(color_signal_type, subset=['信号类型'])
            st.dataframe(styled_df, use_container_width=True)
            
            # 信号统计
            st.subheader("📈 信号统计")
            col_stat1, col_stat2, col_stat3, col_stat4 = st.columns(4)
            
            with col_stat1:
                st.metric("总信号数", len(filtered_signals))
            
            with col_stat2:
                long_short_count = len([s for s in filtered_signals if s.signal_type == "LONG_SHORT"])
                st.metric("做多信号", long_short_count)
            
            with col_stat3:
                short_long_count = len([s for s in filtered_signals if s.signal_type == "SHORT_LONG"])
                st.metric("做空信号", short_long_count)
            
            with col_stat4:
                avg_confidence = np.mean([s.confidence for s in filtered_signals])
                st.metric("平均置信度", f"{avg_confidence:.2f}")
            
        else:
            st.info("暂无符合条件的交易信号")
    else:
        st.info("等待交易信号生成...")

with col2:
    st.header("🏭 行业数据输入")
    
    # 模拟行业数据
    industries = [
        {"name": "银行", "change": np.random.normal(0, 2), "volume": np.random.randint(1000, 5000)},
        {"name": "证券", "change": np.random.normal(0, 3), "volume": np.random.randint(800, 4000)},
        {"name": "保险", "change": np.random.normal(0, 2.5), "volume": np.random.randint(600, 3000)},
        {"name": "地产", "change": np.random.normal(0, 4), "volume": np.random.randint(1200, 6000)},
        {"name": "科技", "change": np.random.normal(0, 5), "volume": np.random.randint(2000, 8000)},
    ]
    
    # 行业表现图表
    industry_df = pd.DataFrame(industries)
    
    fig = px.bar(
        industry_df, 
        x="name", 
        y="change",
        color="change",
        color_continuous_scale="RdYlGn",
        title="行业涨跌幅 (%)"
    )
    fig.update_layout(height=300)
    st.plotly_chart(fig, use_container_width=True)
    
    # 行业成交量
    fig2 = px.pie(
        industry_df,
        values="volume",
        names="name",
        title="行业成交量分布"
    )
    fig2.update_layout(height=300)
    st.plotly_chart(fig2, use_container_width=True)

# 底部信息
st.markdown("---")
col_info1, col_info2, col_info3 = st.columns(3)

with col_info1:
    st.info("💡 **配对交易**: 基于股票价格比率的均值回归策略")

with col_info2:
    st.info("📊 **统计套利**: 利用行业内个股分化的套利机会")

with col_info3:
    st.info("⚡ **实时监控**: 5秒更新频率，实时捕捉套利机会")

# 自动刷新
if st.session_state.running:
    time.sleep(1)
    st.rerun()
