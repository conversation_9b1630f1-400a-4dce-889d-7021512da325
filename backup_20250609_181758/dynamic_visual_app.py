"""
动态量化套利系统可视化应用
支持动态资金池管理和临时套利策略
初始20万，正常70%，临时套利85%
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import time
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits, TradingMode, MarketCondition
    from strategies.emergency_arbitrage import EmergencyArbitrageEngine, EmergencySignal
    from database.signal_database import SignalDatabase, TradingSignal
except ImportError as e:
    st.error(f"模块导入失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="动态量化套利系统",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 100%);
        padding: 1.5rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .emergency-alert {
        background: #ff4757;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        animation: pulse 2s infinite;
    }
    .normal-mode {
        background: #2ed573;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        display: inline-block;
    }
    .emergency-mode {
        background: #ff4757;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        display: inline-block;
        animation: blink 1s infinite;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    @keyframes blink {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'dynamic_risk_manager' not in st.session_state:
    limits = DynamicPositionLimits(
        initial_capital=200000,  # 20万初始资金
        normal_position_ratio=0.70,  # 正常70%
        emergency_position_ratio=0.85,  # 临时套利85%
        max_stock_count=10
    )
    st.session_state.dynamic_risk_manager = DynamicRiskManager(limits)

if 'emergency_engine' not in st.session_state:
    st.session_state.emergency_engine = EmergencyArbitrageEngine()

if 'signal_database' not in st.session_state:
    st.session_state.signal_database = SignalDatabase()

if 'running' not in st.session_state:
    st.session_state.running = False

if 'market_data' not in st.session_state:
    st.session_state.market_data = {}

def create_main_header():
    """创建主标题"""
    risk_manager = st.session_state.dynamic_risk_manager
    mode_class = "emergency-mode" if risk_manager.current_mode == TradingMode.EMERGENCY_ARBITRAGE else "normal-mode"
    mode_text = "🚨 临时套利模式" if risk_manager.current_mode == TradingMode.EMERGENCY_ARBITRAGE else "✅ 正常交易模式"
    
    st.markdown(f"""
    <div class="main-header">
        <h1>⚡ 动态量化套利系统</h1>
        <p>20万动态资金池 | 正常70% | 临时套利85% | 爆跌爆涨快速响应</p>
        <div class="{mode_class}">{mode_text}</div>
    </div>
    """, unsafe_allow_html=True)

def create_dynamic_overview():
    """创建动态概览"""
    st.header("💰 动态资金池概览")
    
    risk_manager = st.session_state.dynamic_risk_manager
    portfolio_summary = risk_manager.get_portfolio_summary()
    
    # 关键指标
    col1, col2, col3, col4, col5, col6 = st.columns(6)
    
    with col1:
        st.metric(
            "初始资金", 
            f"¥{portfolio_summary['initial_capital']:,.0f}",
            delta="基准"
        )
    
    with col2:
        capital_change = portfolio_summary['total_capital'] - portfolio_summary['initial_capital']
        st.metric(
            "当前资金池", 
            f"¥{portfolio_summary['total_capital']:,.0f}",
            delta=f"{capital_change:+,.0f}"
        )
    
    with col3:
        current_limit = portfolio_summary['current_position_limit']
        max_position_value = portfolio_summary['total_capital'] * current_limit
        st.metric(
            "仓位限制", 
            f"{current_limit:.0%}",
            delta=f"最大¥{max_position_value:,.0f}"
        )
    
    with col4:
        st.metric(
            "当前仓位", 
            f"{portfolio_summary['total_position_ratio']:.1%}",
            delta=f"¥{portfolio_summary['total_market_value']:,.0f}"
        )
    
    with col5:
        normal_count = portfolio_summary['normal_position_count']
        emergency_count = portfolio_summary['emergency_position_count']
        st.metric(
            "持仓股票", 
            f"{portfolio_summary['position_count']}/10",
            delta=f"正常{normal_count}|临时{emergency_count}"
        )
    
    with col6:
        st.metric(
            "总收益率", 
            f"{portfolio_summary['total_return']:.2%}",
            delta=f"¥{portfolio_summary['total_pnl']:,.0f}"
        )
    
    # 资金池变化图表
    col1, col2 = st.columns(2)
    
    with col1:
        # 资金池组成饼图
        labels = ['可用现金', '正常仓位', '临时套利仓位']
        values = [
            portfolio_summary['available_cash'],
            portfolio_summary['normal_position_value'],
            portfolio_summary['emergency_position_value']
        ]
        colors = ['#74b9ff', '#00b894', '#e17055']
        
        fig = go.Figure(data=[go.Pie(
            labels=labels, 
            values=values,
            hole=.3,
            marker_colors=colors
        )])
        
        fig.update_layout(
            title="资金池组成",
            height=300,
            annotations=[dict(text=f'总计<br>¥{portfolio_summary["total_capital"]:,.0f}', 
                            x=0.5, y=0.5, font_size=14, showarrow=False)]
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 仓位利用率仪表盘
        current_ratio = portfolio_summary['total_position_ratio']
        max_ratio = portfolio_summary['current_position_limit']
        
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = current_ratio * 100,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "仓位利用率 (%)"},
            delta = {'reference': max_ratio * 100},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "#e17055" if risk_manager.current_mode == TradingMode.EMERGENCY_ARBITRAGE else "#00b894"},
                'steps': [
                    {'range': [0, 70], 'color': "lightgray"},
                    {'range': [70, 85], 'color': "yellow"},
                    {'range': [85, 100], 'color': "red"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': max_ratio * 100
                }
            }
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)

def create_emergency_monitoring():
    """创建临时套利监控"""
    st.header("🚨 临时套利监控")
    
    risk_manager = st.session_state.dynamic_risk_manager
    emergency_engine = st.session_state.emergency_engine
    
    # 生成模拟市场数据
    if st.button("🔄 更新市场数据") or not st.session_state.market_data:
        st.session_state.market_data = generate_market_data()
    
    market_data = st.session_state.market_data
    
    # 检测市场状况
    market_condition = risk_manager.detect_market_condition(market_data)
    should_trigger, emergency_signal = risk_manager.should_trigger_emergency_arbitrage(market_data)
    
    # 市场状况显示
    col1, col2, col3 = st.columns(3)
    
    with col1:
        condition_color = {
            MarketCondition.NORMAL: "🟢",
            MarketCondition.CRASH: "🔴", 
            MarketCondition.SURGE: "🟡",
            MarketCondition.VOLATILE: "🟠"
        }
        st.metric("市场状况", f"{condition_color[market_condition]} {market_condition.value}")
    
    with col2:
        mode_text = "临时套利" if risk_manager.current_mode == TradingMode.EMERGENCY_ARBITRAGE else "正常交易"
        st.metric("交易模式", mode_text)
    
    with col3:
        if risk_manager.emergency_start_time:
            duration = (datetime.now() - risk_manager.emergency_start_time).total_seconds() / 60
            st.metric("套利持续时间", f"{duration:.1f}分钟")
        else:
            st.metric("套利持续时间", "0分钟")
    
    # 临时套利触发
    if should_trigger and emergency_signal:
        st.markdown('<div class="emergency-alert">', unsafe_allow_html=True)
        st.warning(f"🚨 检测到{emergency_signal.market_condition.value}，建议进入临时套利模式！")
        st.write(f"触发股票: {emergency_signal.trigger_symbol}")
        st.write(f"变化幅度: {emergency_signal.trigger_change:.2%}")
        st.write(f"建议操作: {emergency_signal.suggested_action}")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🚨 进入临时套利模式", type="primary"):
                risk_manager.enter_emergency_mode(emergency_signal)
                st.success("已进入临时套利模式！")
                st.rerun()
        
        with col2:
            if st.button("❌ 忽略信号"):
                st.info("已忽略临时套利信号")
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # 退出临时套利模式
    if risk_manager.current_mode == TradingMode.EMERGENCY_ARBITRAGE:
        st.markdown('<div class="emergency-alert">', unsafe_allow_html=True)
        st.error("🚨 当前处于临时套利模式")
        
        if st.button("✅ 退出临时套利模式"):
            risk_manager.exit_emergency_mode()
            st.success("已退出临时套利模式，恢复正常交易")
            st.rerun()
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # 扫描临时套利机会
    emergency_signals = emergency_engine.scan_emergency_opportunities(market_data)
    
    if emergency_signals:
        st.subheader("⚡ 临时套利机会")
        
        signal_data = []
        for signal in emergency_signals[:10]:  # 显示前10个
            signal_data.append({
                '策略类型': signal.strategy_type.value,
                '目标股票': signal.trigger_symbol,
                '操作': signal.action,
                '紧急程度': f"{signal.urgency_level}/5",
                '置信度': f"{signal.confidence:.2f}",
                '预期收益': f"{signal.expected_return:.2%}",
                '最大持有': f"{signal.max_hold_time}分钟",
                '建议仓位': f"{signal.position_size_ratio:.1%}"
            })
        
        df = pd.DataFrame(signal_data)
        st.dataframe(df, use_container_width=True)
        
        # 选择最佳信号
        best_signal = emergency_engine.select_best_emergency_signal(emergency_signals)
        if best_signal:
            st.success(f"🎯 推荐信号: {best_signal.strategy_type.value} - {best_signal.trigger_symbol}")
    else:
        st.info("当前无临时套利机会")

def generate_market_data():
    """生成模拟市场数据"""
    symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    market_data = {}
    
    # 模拟不同的市场情况
    scenario = np.random.choice(['normal', 'crash', 'surge', 'volatile'], p=[0.6, 0.15, 0.15, 0.1])
    
    for symbol in symbols:
        base_price = np.random.uniform(8, 50)
        
        if scenario == 'crash':
            change = np.random.uniform(-0.12, -0.03)  # 3%-12%跌幅
        elif scenario == 'surge':
            change = np.random.uniform(0.03, 0.10)   # 3%-10%涨幅
        elif scenario == 'volatile':
            change = np.random.uniform(-0.08, 0.08)  # 高波动
        else:
            change = np.random.normal(0, 0.02)       # 正常波动
        
        market_data[symbol] = {
            'price': base_price * (1 + change),
            'change': change,
            'volume': np.random.randint(500000, 5000000)
        }
    
    return market_data

def create_market_data_display():
    """创建市场数据显示"""
    st.header("📊 实时市场数据")
    
    market_data = st.session_state.market_data
    
    if market_data:
        # 市场数据表格
        data_rows = []
        for symbol, data in market_data.items():
            data_rows.append({
                '股票代码': symbol,
                '当前价格': f"¥{data['price']:.2f}",
                '涨跌幅': f"{data['change']:+.2%}",
                '成交量': f"{data['volume']:,}",
                '状态': get_stock_status(data['change'])
            })
        
        df = pd.DataFrame(data_rows)
        
        # 根据涨跌幅着色
        def color_change(val):
            if '+' in val:
                return 'background-color: #d4edda'
            elif '-' in val:
                return 'background-color: #f8d7da'
            else:
                return 'background-color: #fff3cd'
        
        styled_df = df.style.applymap(color_change, subset=['涨跌幅'])
        st.dataframe(styled_df, use_container_width=True)
        
        # 市场热力图
        symbols = list(market_data.keys())
        changes = [market_data[symbol]['change'] for symbol in symbols]
        
        fig = go.Figure(data=go.Bar(
            x=symbols,
            y=changes,
            marker_color=['red' if x < 0 else 'green' for x in changes],
            text=[f"{x:+.1%}" for x in changes],
            textposition='auto'
        ))
        
        fig.update_layout(
            title="股票涨跌幅分布",
            xaxis_title="股票代码",
            yaxis_title="涨跌幅",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)

def get_stock_status(change):
    """获取股票状态"""
    if change <= -0.05:
        return "🔴 爆跌"
    elif change >= 0.05:
        return "🟢 爆涨"
    elif abs(change) >= 0.03:
        return "🟡 高波动"
    else:
        return "⚪ 正常"

def create_position_management():
    """创建仓位管理"""
    st.header("📈 仓位管理")
    
    risk_manager = st.session_state.dynamic_risk_manager
    
    # 仓位检查工具
    st.subheader("🔍 仓位检查工具")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        test_symbol = st.selectbox("选择股票", ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH'])
    
    with col2:
        test_price = st.number_input("股票价格", min_value=1.0, max_value=100.0, value=10.0, step=0.1)
    
    with col3:
        test_quantity = st.number_input("购买数量", min_value=100, max_value=10000, value=1000, step=100)
    
    with col4:
        is_emergency = st.checkbox("临时套利仓位")
    
    if st.button("🔍 检查仓位"):
        can_open, message, info = risk_manager.can_open_position(
            test_symbol, test_price, test_quantity, is_emergency
        )
        
        if can_open:
            st.success(f"✅ {message}")
            if info:
                st.write(f"建议数量: {info.get('suggested_quantity', 0)}股")
                st.write(f"建议金额: ¥{info.get('suggested_value', 0):,.0f}")
                st.write(f"建议仓位比例: {info.get('suggested_ratio', 0):.1%}")
                st.write(f"当前模式: {info.get('current_mode', 'unknown')}")
        else:
            st.error(f"❌ {message}")
    
    # 当前持仓显示
    if risk_manager.positions:
        st.subheader("�� 当前持仓")
        
        position_data = []
        for symbol, position in risk_manager.positions.items():
            position_data.append({
                '股票代码': symbol,
                '数量': f"{position.quantity}股",
                '成本价': f"¥{position.avg_cost:.2f}",
                '现价': f"¥{position.current_price:.2f}",
                '市值': f"¥{position.market_value:,.0f}",
                '盈亏': f"¥{position.unrealized_pnl:+,.0f}",
                '仓位比例': f"{position.position_ratio:.1%}",
                '类型': "临时套利" if position.is_emergency_position else "正常"
            })
        
        df = pd.DataFrame(position_data)
        st.dataframe(df, use_container_width=True)
    else:
        st.info("当前无持仓")

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.header("🎛️ 系统控制")
        
        # 系统状态
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🚀 启动", type="primary", use_container_width=True):
                st.session_state.running = True
                st.success("系统已启动")
        
        with col2:
            if st.button("⏹️ 停止", use_container_width=True):
                st.session_state.running = False
                st.success("系统已停止")
        
        st.markdown("---")
        
        # 动态参数设置
        st.subheader("⚙️ 动态参数")
        
        normal_ratio = st.slider(
            "正常仓位限制", 
            0.50, 0.80, 0.70, 0.05,
            format="%.0f%%"
        )
        
        emergency_ratio = st.slider(
            "临时套利限制", 
            0.75, 0.95, 0.85, 0.05,
            format="%.0f%%"
        )
        
        crash_threshold = st.slider(
            "爆跌触发阈值", 
            -0.10, -0.03, -0.05, 0.01,
            format="%.1f%%"
        )
        
        surge_threshold = st.slider(
            "爆涨触发阈值", 
            0.03, 0.10, 0.05, 0.01,
            format="%.1f%%"
        )
        
        # 更新参数
        if st.button("💾 更新参数"):
            risk_manager = st.session_state.dynamic_risk_manager
            risk_manager.limits.normal_position_ratio = normal_ratio
            risk_manager.limits.emergency_position_ratio = emergency_ratio
            risk_manager.limits.crash_threshold = crash_threshold
            risk_manager.limits.surge_threshold = surge_threshold
            st.success("参数已更新")
        
        st.markdown("---")
        
        # 系统状态显示
        st.subheader("📊 系统状态")
        
        risk_manager = st.session_state.dynamic_risk_manager
        
        if st.session_state.running:
            st.success("🟢 系统运行中")
        else:
            st.error("🔴 系统已停止")
        
        # 显示当前配置
        st.write(f"💰 当前资金池: ¥{risk_manager.total_capital:,.0f}")
        st.write(f"📊 正常仓位限制: {risk_manager.limits.normal_position_ratio:.0%}")
        st.write(f"🚨 临时套利限制: {risk_manager.limits.emergency_position_ratio:.0%}")
        st.write(f"📈 持仓股票: {len(risk_manager.positions)}/10")
        
        # 交易模式
        mode_color = "🚨" if risk_manager.current_mode == TradingMode.EMERGENCY_ARBITRAGE else "✅"
        st.write(f"{mode_color} 交易模式: {risk_manager.current_mode.value}")

def main():
    """主函数"""
    create_sidebar()
    create_main_header()
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "💰 动态资金池", 
        "🚨 临时套利", 
        "📊 市场监控", 
        "📈 仓位管理"
    ])
    
    with tab1:
        create_dynamic_overview()
    
    with tab2:
        create_emergency_monitoring()
    
    with tab3:
        create_market_data_display()
    
    with tab4:
        create_position_management()
    
    # 自动刷新
    if st.session_state.running:
        time.sleep(3)
        st.rerun()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>⚡ 动态量化套利系统 v3.0 | 20万动态资金池 | 临时套利85%</p>
        <p>🚨 爆跌爆涨快速响应 | 智能风险控制 | 实时模式切换</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
