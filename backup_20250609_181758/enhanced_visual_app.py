"""
增强版量化套利系统可视化应用
包含策略比较、仓位控制、数据库管理等功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import time
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from risk.enhanced_risk_manager import EnhancedRiskManager, PositionLimits
    from strategies.strategy_optimizer import StrategyOptimizer, TradingSignal as OptSignal
    from database.signal_database import SignalDatabase, TradingSignal
except ImportError as e:
    st.error(f"模块导入失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="增强版量化套利系统",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
    }
    .strategy-comparison {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
    .risk-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
    .success-box {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'risk_manager' not in st.session_state:
    limits = PositionLimits(
        initial_capital=200000,  # 20万初始资金
        max_total_position_ratio=0.70,  # 70%最大仓位
        max_stock_count=10,  # 最多10只股票
        max_single_position_ratio=0.15  # 单只股票最大15%
    )
    st.session_state.risk_manager = EnhancedRiskManager(limits)

if 'strategy_optimizer' not in st.session_state:
    st.session_state.strategy_optimizer = StrategyOptimizer()

if 'signal_database' not in st.session_state:
    st.session_state.signal_database = SignalDatabase()

if 'running' not in st.session_state:
    st.session_state.running = False

if 'signals_history' not in st.session_state:
    st.session_state.signals_history = []

def create_main_header():
    """创建主标题"""
    st.markdown("""
    <div class="main-header">
        <h1>🚀 增强版量化套利系统</h1>
        <p>20万资金 | 70%仓位控制 | 10只股票限制 | 策略智能优选</p>
    </div>
    """, unsafe_allow_html=True)

def create_risk_overview():
    """创建风险概览"""
    st.header("🛡️ 风险控制概览")
    
    risk_manager = st.session_state.risk_manager
    portfolio_summary = risk_manager.get_portfolio_summary()
    risk_metrics = risk_manager.get_risk_metrics()
    
    # 关键指标
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric(
            "总资金", 
            f"¥{portfolio_summary['total_capital']:,.0f}",
            delta=f"可用: ¥{portfolio_summary['available_cash']:,.0f}"
        )
    
    with col2:
        st.metric(
            "总仓位", 
            f"{portfolio_summary['total_position_ratio']:.1%}",
            delta=f"限制: 70%"
        )
    
    with col3:
        st.metric(
            "持仓数量", 
            f"{portfolio_summary['position_count']}/10",
            delta=f"剩余: {10 - portfolio_summary['position_count']}"
        )
    
    with col4:
        st.metric(
            "现金比例", 
            f"{portfolio_summary['cash_ratio']:.1%}",
            delta=f"储备: 30%"
        )
    
    with col5:
        risk_color = {"低风险": "🟢", "中风险": "🟡", "高风险": "🔴"}
        st.metric(
            "风险等级", 
            f"{risk_color.get(risk_metrics['risk_level'], '⚪')} {risk_metrics['risk_level']}"
        )
    
    # 仓位分配建议
    allocation_suggestion = risk_manager.get_position_allocation_suggestion()
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 仓位利用率图表
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = allocation_suggestion['position_utilization'] * 100,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "仓位利用率 (%)"},
            delta = {'reference': 70},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 70], 'color': "yellow"},
                    {'range': [70, 100], 'color': "red"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 70
                }
            }
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 风险指标雷达图
        categories = ['集中度风险', '单股风险', '仓位风险', '现金风险']
        values = [
            1 - risk_metrics['concentration_risk_hhi'],  # 越低越好
            1 - risk_metrics['max_single_position_ratio'],  # 越低越好
            1 - risk_metrics['total_position_ratio'],  # 适中最好
            risk_metrics['cash_ratio']  # 适中最好
        ]
        
        fig = go.Figure()
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name='当前风险水平'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )),
            showlegend=True,
            title="风险指标雷达图",
            height=300
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 风险提示
    rebalance_suggestions = risk_manager.get_rebalance_suggestions()
    if rebalance_suggestions:
        st.markdown('<div class="risk-warning">', unsafe_allow_html=True)
        st.warning("⚠️ 风险提示")
        for suggestion in rebalance_suggestions:
            st.write(f"• {suggestion['reason']}: {suggestion['suggested_action']}")
        st.markdown('</div>', unsafe_allow_html=True)

def create_strategy_comparison():
    """创建策略比较页面"""
    st.header("🧠 策略智能比较")
    
    # 模拟市场数据
    market_data = {
        '000001.SZ': {'price': 10.5 + np.random.normal(0, 0.2), 'change': np.random.normal(0, 0.02)},
        '000002.SZ': {'price': 12.3 + np.random.normal(0, 0.3), 'change': np.random.normal(0, 0.02)},
        '600000.SH': {'price': 8.7 + np.random.normal(0, 0.2), 'change': np.random.normal(0, 0.02)},
        '600036.SH': {'price': 45.2 + np.random.normal(0, 1.0), 'change': np.random.normal(0, 0.02)}
    }
    
    # 策略比较按钮
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("🔄 运行策略比较", type="primary"):
            with st.spinner("正在比较策略..."):
                best_signal, comparison_summary = st.session_state.strategy_optimizer.select_best_strategy(market_data)
                st.session_state.last_comparison = comparison_summary
                st.session_state.best_signal = best_signal
    
    with col2:
        auto_compare = st.checkbox("自动比较", value=False)
    
    # 显示比较结果
    if hasattr(st.session_state, 'last_comparison'):
        comparison = st.session_state.last_comparison
        
        st.subheader("📊 策略性能比较")
        
        # 策略性能表格
        performance_data = []
        for strategy_name, perf in comparison['strategy_performances'].items():
            performance_data.append({
                '策略名称': strategy_name,
                '总收益率': f"{perf['total_return']:.2%}",
                '夏普比率': f"{perf['sharpe_ratio']:.2f}",
                '最大回撤': f"{perf['max_drawdown']:.2%}",
                '胜率': f"{perf['win_rate']:.1%}",
                '综合评分': f"{perf['score']:.3f}",
                '有信号': "✅" if perf['has_signal'] else "❌"
            })
        
        df = pd.DataFrame(performance_data)
        st.dataframe(df, use_container_width=True)
        
        # 策略比较图表
        col1, col2 = st.columns(2)
        
        with col1:
            # 综合评分对比
            strategy_names = list(comparison['strategy_performances'].keys())
            scores = [perf['score'] for perf in comparison['strategy_performances'].values()]
            
            fig = go.Figure(data=[
                go.Bar(x=strategy_names, y=scores, 
                      marker_color=['gold' if name == comparison.get('selected_strategy') else 'lightblue' 
                                  for name in strategy_names])
            ])
            fig.update_layout(
                title="策略综合评分对比",
                xaxis_title="策略",
                yaxis_title="评分",
                height=400
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 多维度性能对比
            metrics = ['total_return', 'sharpe_ratio', 'win_rate']
            metric_names = ['总收益率', '夏普比率', '胜率']
            
            fig = go.Figure()
            
            for strategy_name, perf in comparison['strategy_performances'].items():
                values = [perf['total_return'], perf['sharpe_ratio'], perf['win_rate']]
                fig.add_trace(go.Scatterpolar(
                    r=values,
                    theta=metric_names,
                    fill='toself',
                    name=strategy_name
                ))
            
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, max(1, max([perf['sharpe_ratio'] for perf in comparison['strategy_performances'].values()]))]
                    )),
                showlegend=True,
                title="多维度性能对比",
                height=400
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # 最优策略选择结果
        if comparison.get('selected_strategy'):
            st.markdown('<div class="success-box">', unsafe_allow_html=True)
            st.success(f"🎯 最优策略: {comparison['selected_strategy']}")
            st.write(f"📊 选择评分: {comparison.get('selection_score', 0):.3f}")
            if hasattr(st.session_state, 'best_signal') and st.session_state.best_signal:
                signal = st.session_state.best_signal
                st.write(f"📈 交易信号: {signal.signal_type}")
                st.write(f"🎯 置信度: {signal.confidence:.2f}")
                st.write(f"💰 预期收益: {signal.expected_return:.2%}")
            st.markdown('</div>', unsafe_allow_html=True)
    
    # 自动比较
    if auto_compare:
        time.sleep(3)
        st.rerun()

def create_signal_management():
    """创建信号管理页面"""
    st.header("📡 交易信号管理")
    
    db = st.session_state.signal_database
    
    # 信号统计
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 生成新信号"):
            # 模拟生成信号
            market_data = {
                '000001.SZ': {'price': 10.5 + np.random.normal(0, 0.2), 'change': np.random.normal(0, 0.02)},
                '000002.SZ': {'price': 12.3 + np.random.normal(0, 0.3), 'change': np.random.normal(0, 0.02)}
            }
            
            best_signal, _ = st.session_state.strategy_optimizer.select_best_strategy(market_data)
            
            if best_signal:
                # 转换为数据库格式
                db_signal = TradingSignal(
                    timestamp=datetime.now(),
                    strategy_name=best_signal.strategy_name,
                    symbol1=best_signal.symbol1,
                    symbol2=best_signal.symbol2,
                    signal_type=best_signal.signal_type,
                    confidence=best_signal.confidence,
                    expected_return=best_signal.expected_return,
                    risk_level=best_signal.risk_level,
                    entry_price1=best_signal.entry_price1,
                    entry_price2=best_signal.entry_price2,
                    performance_score=best_signal.performance_score,
                    is_historical=False
                )
                
                signal_id = db.save_signal(db_signal)
                st.success(f"生成新信号 ID: {signal_id}")
            else:
                st.warning("未生成有效信号")
    
    with col2:
        if st.button("🔄 标记历史信号"):
            count = db.mark_signals_as_historical()
            st.info(f"标记了 {count} 个历史信号")
    
    with col3:
        if st.button("🧹 清理旧数据"):
            market_deleted, signals_deleted = db.cleanup_old_data(days=30)
            st.info(f"清理了 {market_deleted} 条市场数据, {signals_deleted} 条信号")
    
    # 信号列表
    tab1, tab2, tab3 = st.tabs(["🔴 实时信号", "📚 历史信号", "📈 统计分析"])
    
    with tab1:
        st.subheader("实时交易信号")
        realtime_signals = db.get_realtime_signals(hours=24)
        
        if realtime_signals:
            signal_data = []
            for signal in realtime_signals[-20:]:  # 最近20个
                signal_data.append({
                    'ID': signal.id,
                    '时间': signal.timestamp.strftime('%H:%M:%S'),
                    '策略': signal.strategy_name,
                    '交易对': f"{signal.symbol1}/{signal.symbol2}",
                    '信号': signal.signal_type,
                    '置信度': f"{signal.confidence:.2f}",
                    '预期收益': f"{signal.expected_return:.2%}",
                    '风险': signal.risk_level,
                    '已执行': "✅" if signal.is_executed else "❌"
                })
            
            df = pd.DataFrame(signal_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无实时信号")
    
    with tab2:
        st.subheader("历史交易信号")
        historical_signals = db.get_historical_signals(days=7)
        
        if historical_signals:
            signal_data = []
            for signal in historical_signals[-20:]:
                signal_data.append({
                    'ID': signal.id,
                    '日期': signal.timestamp.strftime('%m-%d'),
                    '策略': signal.strategy_name,
                    '信号': signal.signal_type,
                    '置信度': f"{signal.confidence:.2f}",
                    '预期收益': f"{signal.expected_return:.2%}",
                    '实际收益': f"{signal.actual_return:.2%}" if signal.actual_return else "N/A"
                })
            
            df = pd.DataFrame(signal_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无历史信号")
    
    with tab3:
        st.subheader("信号统计分析")
        stats = db.get_strategy_statistics(days=30)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总信号数", stats['total_signals'])
        with col2:
            st.metric("执行率", f"{stats['execution_rate']:.1%}")
        with col3:
            st.metric("平均置信度", f"{stats['avg_confidence']:.2f}")
        with col4:
            st.metric("平均收益", f"{stats['avg_actual_return']:.2%}")
        
        # 策略分布图
        if stats['strategy_breakdown']:
            strategy_names = [s['strategy_name'] for s in stats['strategy_breakdown']]
            signal_counts = [s['signal_count'] for s in stats['strategy_breakdown']]
            
            fig = px.pie(values=signal_counts, names=strategy_names, title="策略信号分布")
            st.plotly_chart(fig, use_container_width=True)

def create_database_info():
    """创建数据库信息页面"""
    st.header("💾 数据库管理")
    
    db = st.session_state.signal_database
    db_info = db.get_database_info()
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 数据库概览")
        st.metric("数据库大小", f"{db_info['database_size_mb']:.2f} MB")
        st.metric("总记录数", f"{db_info['total_records']:,}")
        
        # 表记录数
        for table, count in db_info['table_counts'].items():
            st.write(f"• {table}: {count:,} 条记录")
    
    with col2:
        st.subheader("🔧 数据库操作")
        
        if st.button("📤 导出信号数据"):
            try:
                filepath = f"signals_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                db.export_signals_to_csv(filepath)
                st.success(f"数据已导出到: {filepath}")
            except Exception as e:
                st.error(f"导出失败: {e}")
        
        if st.button("🗑️ 清理旧数据"):
            try:
                market_deleted, signals_deleted = db.cleanup_old_data(days=90)
                st.success(f"清理完成: {market_deleted} 条市场数据, {signals_deleted} 条信号")
            except Exception as e:
                st.error(f"清理失败: {e}")

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.header("🎛️ 系统控制")
        
        # 系统状态
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🚀 启动", type="primary", use_container_width=True):
                st.session_state.running = True
                st.success("系统已启动")
        
        with col2:
            if st.button("⏹️ 停止", use_container_width=True):
                st.session_state.running = False
                st.success("系统已停止")
        
        st.markdown("---")
        
        # 风险参数设置
        st.subheader("⚙️ 风险参数")
        
        initial_capital = st.number_input(
            "初始资金 (万元)", 
            min_value=10, max_value=100, value=20, step=5
        ) * 10000
        
        max_position_ratio = st.slider(
            "最大总仓位", 
            0.50, 0.90, 0.70, 0.05,
            format="%.0f%%"
        )
        
        max_stock_count = st.slider(
            "最大持仓股票数", 
            5, 20, 10, 1
        )
        
        max_single_ratio = st.slider(
            "单股最大仓位", 
            0.05, 0.25, 0.15, 0.01,
            format="%.0f%%"
        )
        
        # 更新风险管理器参数
        if st.button("💾 更新参数"):
            new_limits = PositionLimits(
                initial_capital=initial_capital,
                max_total_position_ratio=max_position_ratio,
                max_stock_count=max_stock_count,
                max_single_position_ratio=max_single_ratio
            )
            st.session_state.risk_manager = EnhancedRiskManager(new_limits)
            st.success("参数已更新")
        
        st.markdown("---")
        
        # 系统信息
        st.subheader("📊 系统状态")
        
        if st.session_state.running:
            st.success("🟢 系统运行中")
        else:
            st.error("🔴 系统已停止")
        
        # 显示当前配置
        risk_manager = st.session_state.risk_manager
        st.write(f"💰 初始资金: ¥{risk_manager.limits.initial_capital:,.0f}")
        st.write(f"📊 最大仓位: {risk_manager.limits.max_total_position_ratio:.0%}")
        st.write(f"📈 最大股票数: {risk_manager.limits.max_stock_count}")
        st.write(f"🎯 单股限制: {risk_manager.limits.max_single_position_ratio:.0%}")

def main():
    """主函数"""
    create_sidebar()
    create_main_header()
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "🛡️ 风险控制", 
        "🧠 策略比较", 
        "📡 信号管理", 
        "💾 数据库"
    ])
    
    with tab1:
        create_risk_overview()
    
    with tab2:
        create_strategy_comparison()
    
    with tab3:
        create_signal_management()
    
    with tab4:
        create_database_info()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>🚀 增强版量化套利系统 v2.0 | 20万资金专用版 | 智能策略优选</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
