#!/bin/bash

# 量化套利系统状态检查脚本
# 使用方法: ./scripts/check_status.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 获取当前时间
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')

echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                  量化套利系统状态检查                         ║"
echo "║                System Status Check                          ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${BLUE}📊 系统状态检查 - $CURRENT_TIME${NC}"
echo ""

# 检查进程状态
echo -e "${CYAN}🔍 进程状态检查:${NC}"
echo "----------------------------------------"

check_process() {
    local app_name=$1
    local process_pattern=$2
    local pid_file=$3
    local port=$4
    
    printf "%-15s: " "$app_name"
    
    # 检查PID文件
    if [ -f "$pid_file" ]; then
        PID=$(cat $pid_file)
        if kill -0 $PID 2>/dev/null; then
            # 进程存在，检查是否是正确的进程
            if pgrep -f "$process_pattern" | grep -q $PID; then
                echo -e "${GREEN}✅ 运行中 (PID: $PID)${NC}"
                
                # 检查端口
                if [ -n "$port" ] && lsof -i :$port > /dev/null 2>&1; then
                    echo "                端口 $port: ✅ 正常监听"
                elif [ -n "$port" ]; then
                    echo -e "                端口 $port: ${YELLOW}⚠️  未监听${NC}"
                fi
                
                # 显示进程信息
                PROCESS_INFO=$(ps -p $PID -o pid,ppid,pcpu,pmem,etime,cmd --no-headers 2>/dev/null)
                if [ -n "$PROCESS_INFO" ]; then
                    echo "                进程信息: $PROCESS_INFO"
                fi
                
                return 0
            else
                echo -e "${RED}❌ PID文件存在但进程不匹配${NC}"
                rm -f $pid_file
                return 1
            fi
        else
            echo -e "${RED}❌ PID文件存在但进程已停止${NC}"
            rm -f $pid_file
            return 1
        fi
    else
        # 检查是否有匹配的进程在运行
        if pgrep -f "$process_pattern" > /dev/null; then
            RUNNING_PID=$(pgrep -f "$process_pattern" | head -1)
            echo -e "${YELLOW}⚠️  运行中但无PID文件 (PID: $RUNNING_PID)${NC}"
            echo $RUNNING_PID > $pid_file
            return 2
        else
            echo -e "${RED}❌ 已停止${NC}"
            return 1
        fi
    fi
}

# 检查各个服务
check_process "主界面" "realtime_integrated_app.py" ".main.pid" "8503"
MAIN_STATUS=$?

check_process "增强界面" "enhanced_visual_app.py" ".enhanced.pid" "8504"
ENHANCED_STATUS=$?

check_process "动态界面" "dynamic_visual_app.py" ".dynamic.pid" "8505"
DYNAMIC_STATUS=$?

echo ""

# 检查端口占用详情
echo -e "${CYAN}🌐 端口状态详情:${NC}"
echo "----------------------------------------"
PORTS=(8503 8504 8505)
PORT_NAMES=("主界面" "增强界面" "动态界面")

for i in "${!PORTS[@]}"; do
    port=${PORTS[$i]}
    name=${PORT_NAMES[$i]}
    
    printf "%-15s (端口 %d): " "$name" "$port"
    
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 已占用${NC}"
        # 显示占用进程详情
        PROCESS_INFO=$(lsof -i :$port | tail -n +2)
        echo "                $PROCESS_INFO"
    else
        echo -e "${RED}❌ 空闲${NC}"
    fi
done

echo ""

# 检查网络连接
echo -e "${CYAN}🔗 网络连接测试:${NC}"
echo "----------------------------------------"
for port in "${PORTS[@]}"; do
    printf "端口 %d HTTP测试: " "$port"
    if curl -s --connect-timeout 3 http://localhost:$port > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 响应正常${NC}"
    else
        echo -e "${RED}❌ 无响应${NC}"
    fi
done

echo ""

# 检查数据库状态
echo -e "${CYAN}🗄️  数据库状态:${NC}"
echo "----------------------------------------"
python3 -c "
import sys
sys.path.append('.')
try:
    from src.database.signal_database import SignalDatabase
    db = SignalDatabase('quantitative_arbitrage.db')
    info = db.get_database_info()
    print(f'✅ 连接正常')
    print(f'   数据库大小: {info[\"database_size_mb\"]:.2f} MB')
    print(f'   总记录数: {info[\"total_records\"]}')
    print(f'   表数量: {len(info[\"tables\"])}')
    
    # 获取最近的信号
    from datetime import datetime, timedelta
    recent_signals = db.get_realtime_signals(hours=1)
    print(f'   最近1小时信号: {len(recent_signals)} 个')
    
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
"

echo ""

# 检查系统资源
echo -e "${CYAN}💻 系统资源状态:${NC}"
echo "----------------------------------------"

# CPU使用率
if command -v top &> /dev/null; then
    CPU_USAGE=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//' 2>/dev/null || echo "N/A")
    echo "CPU使用率: ${CPU_USAGE}%"
elif command -v vmstat &> /dev/null; then
    CPU_USAGE=$(vmstat 1 2 | tail -1 | awk '{print 100-$15}')
    echo "CPU使用率: ${CPU_USAGE}%"
fi

# 内存使用
if command -v free &> /dev/null; then
    MEMORY_INFO=$(free -h | grep "Mem:")
    echo "内存状态: $MEMORY_INFO"
elif command -v vm_stat &> /dev/null; then
    # macOS
    MEMORY_PRESSURE=$(memory_pressure 2>/dev/null | grep "System-wide memory free percentage" | awk '{print $5}' || echo "N/A")
    echo "内存压力: $MEMORY_PRESSURE"
fi

# 磁盘使用
if command -v df &> /dev/null; then
    DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}')
    echo "磁盘使用: $DISK_USAGE"
fi

echo ""

# 检查日志文件
echo -e "${CYAN}📝 日志文件状态:${NC}"
echo "----------------------------------------"
if [ -d "logs" ]; then
    LOG_FILES=("main_app.log" "enhanced_app.log" "dynamic_app.log" "system_events.log")
    
    for log_file in "${LOG_FILES[@]}"; do
        log_path="logs/$log_file"
        printf "%-20s: " "$log_file"
        
        if [ -f "$log_path" ]; then
            FILE_SIZE=$(du -h "$log_path" | cut -f1)
            LAST_MODIFIED=$(stat -c %y "$log_path" 2>/dev/null | cut -d'.' -f1 || stat -f %Sm "$log_path" 2>/dev/null)
            echo -e "${GREEN}✅ 存在 (${FILE_SIZE})${NC}"
            echo "                     最后修改: $LAST_MODIFIED"
            
            # 显示最后几行
            echo "                     最近日志:"
            tail -2 "$log_path" 2>/dev/null | sed 's/^/                       /'
        else
            echo -e "${YELLOW}⚠️  不存在${NC}"
        fi
    done
    
    # 总日志大小
    TOTAL_LOG_SIZE=$(du -sh logs 2>/dev/null | cut -f1)
    echo ""
    echo "日志目录总大小: $TOTAL_LOG_SIZE"
else
    echo -e "${YELLOW}⚠️  logs目录不存在${NC}"
fi

echo ""

# 检查配置文件
echo -e "${CYAN}⚙️  配置文件状态:${NC}"
echo "----------------------------------------"
CONFIG_FILES=(".env" "config/config.yaml" "requirements.txt")

for config_file in "${CONFIG_FILES[@]}"; do
    printf "%-20s: " "$config_file"
    if [ -f "$config_file" ]; then
        FILE_SIZE=$(du -h "$config_file" | cut -f1)
        echo -e "${GREEN}✅ 存在 (${FILE_SIZE})${NC}"
    else
        echo -e "${YELLOW}⚠️  不存在${NC}"
    fi
done

echo ""

# 系统健康评分
echo -e "${CYAN}🏥 系统健康评分:${NC}"
echo "----------------------------------------"
HEALTH_SCORE=0
TOTAL_CHECKS=5

# 主界面运行状态 (权重: 40%)
if [ $MAIN_STATUS -eq 0 ]; then
    HEALTH_SCORE=$((HEALTH_SCORE + 40))
    echo "主界面状态: ✅ (+40分)"
else
    echo "主界面状态: ❌ (+0分)"
fi

# 数据库连接 (权重: 30%)
if python3 -c "
import sys
sys.path.append('.')
from src.database.signal_database import SignalDatabase
try:
    db = SignalDatabase('quantitative_arbitrage.db')
    db.get_database_info()
    exit(0)
except:
    exit(1)
" 2>/dev/null; then
    HEALTH_SCORE=$((HEALTH_SCORE + 30))
    echo "数据库连接: ✅ (+30分)"
else
    echo "数据库连接: ❌ (+0分)"
fi

# 端口可访问性 (权重: 20%)
if curl -s --connect-timeout 3 http://localhost:8503 > /dev/null 2>&1; then
    HEALTH_SCORE=$((HEALTH_SCORE + 20))
    echo "网络访问: ✅ (+20分)"
else
    echo "网络访问: ❌ (+0分)"
fi

# 日志文件存在 (权重: 5%)
if [ -f "logs/main_app.log" ]; then
    HEALTH_SCORE=$((HEALTH_SCORE + 5))
    echo "日志记录: ✅ (+5分)"
else
    echo "日志记录: ❌ (+0分)"
fi

# 配置文件存在 (权重: 5%)
if [ -f ".env" ]; then
    HEALTH_SCORE=$((HEALTH_SCORE + 5))
    echo "配置文件: ✅ (+5分)"
else
    echo "配置文件: ❌ (+0分)"
fi

echo ""
echo "总体健康评分: $HEALTH_SCORE/100"

if [ $HEALTH_SCORE -ge 90 ]; then
    echo -e "${GREEN}🎉 系统状态: 优秀${NC}"
elif [ $HEALTH_SCORE -ge 70 ]; then
    echo -e "${YELLOW}⚠️  系统状态: 良好${NC}"
elif [ $HEALTH_SCORE -ge 50 ]; then
    echo -e "${YELLOW}⚠️  系统状态: 一般${NC}"
else
    echo -e "${RED}❌ 系统状态: 需要关注${NC}"
fi

echo ""

# 建议操作
echo -e "${CYAN}💡 建议操作:${NC}"
echo "----------------------------------------"
if [ $MAIN_STATUS -ne 0 ]; then
    echo "• 启动系统: ./scripts/start_system.sh"
fi

if [ $HEALTH_SCORE -lt 70 ]; then
    echo "• 检查日志: tail -f logs/main_app.log"
    echo "• 重启系统: ./scripts/stop_system.sh && ./scripts/start_system.sh"
fi

echo "• 查看详细日志: ls -la logs/"
echo "• 备份数据: ./scripts/backup_database.sh"
echo "• 清理日志: ./scripts/cleanup_logs.sh"

echo ""
echo -e "${BLUE}📊 状态检查完成 - $(date '+%H:%M:%S')${NC}"
