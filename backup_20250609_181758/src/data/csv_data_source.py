"""
CSV文件数据源实现
支持从CSV文件读取历史数据和模拟实时数据
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger
import threading
import time

from .data_source_interface import (
    DataSourceInterface, DataSourceConfig, MarketDataPoint, 
    DataFrequency, DataConverter, DataValidator
)


class CSVDataSource(DataSourceInterface):
    """CSV文件数据源"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.data_cache: Dict[str, pd.DataFrame] = {}
        self.current_index: Dict[str, int] = {}
        self.simulation_thread = None
        self.simulation_running = False
        self.simulation_speed = 1.0  # 模拟速度倍数
        self.last_data: Dict[str, MarketDataPoint] = {}
        
    def connect(self) -> bool:
        """连接CSV数据源"""
        try:
            if not self.config.file_path or not os.path.exists(self.config.file_path):
                logger.error(f"CSV文件不存在: {self.config.file_path}")
                return False
            
            # 加载CSV数据
            self._load_csv_data()
            self.is_connected = True
            logger.info(f"CSV数据源连接成功: {self.config.file_path}")
            return True
            
        except Exception as e:
            logger.error(f"CSV数据源连接失败: {e}")
            self._handle_error(e)
            return False
    
    def disconnect(self) -> bool:
        """断开连接"""
        try:
            self.simulation_running = False
            if self.simulation_thread and self.simulation_thread.is_alive():
                self.simulation_thread.join(timeout=5)
            
            self.data_cache.clear()
            self.current_index.clear()
            self.is_connected = False
            logger.info("CSV数据源已断开连接")
            return True
            
        except Exception as e:
            logger.error(f"断开CSV数据源失败: {e}")
            return False
    
    def _load_csv_data(self):
        """加载CSV数据"""
        try:
            # 读取CSV文件
            df = pd.read_csv(
                self.config.file_path,
                encoding=self.config.encoding,
                delimiter=self.config.delimiter
            )
            
            # 数据预处理
            df = self._preprocess_dataframe(df)
            
            # 按股票代码分组存储
            if 'symbol' in df.columns:
                for symbol in df['symbol'].unique():
                    symbol_data = df[df['symbol'] == symbol].copy()
                    symbol_data = symbol_data.sort_values('timestamp')
                    self.data_cache[symbol] = symbol_data
                    self.current_index[symbol] = 0
                    logger.info(f"加载股票 {symbol} 数据: {len(symbol_data)} 条记录")
            else:
                # 单个股票数据
                symbol = self.config.symbols[0] if self.config.symbols else "DEFAULT"
                df = df.sort_values('timestamp')
                self.data_cache[symbol] = df
                self.current_index[symbol] = 0
                logger.info(f"加载股票 {symbol} 数据: {len(df)} 条记录")
                
        except Exception as e:
            logger.error(f"加载CSV数据失败: {e}")
            raise
    
    def _preprocess_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理DataFrame"""
        # 标准化列名
        column_mapping = {
            'time': 'timestamp',
            'date': 'timestamp',
            'datetime': 'timestamp',
            'ts': 'timestamp',
            'code': 'symbol',
            'stock_code': 'symbol',
            'ticker': 'symbol',
            'vol': 'volume',
            'amount': 'amount',
            'turnover': 'amount',
            'pct_chg': 'change_pct',
            'pct_change': 'change_pct'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 处理时间戳
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        else:
            # 如果没有时间戳，生成模拟时间戳
            start_time = datetime.now() - timedelta(days=len(df))
            df['timestamp'] = pd.date_range(start=start_time, periods=len(df), freq='1min')
        
        # 确保必需的数值列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = np.random.randint(1000000, 10000000, len(df))
                else:
                    # 基于close价格生成其他价格
                    if 'close' in df.columns:
                        base_price = df['close']
                        if col == 'open':
                            df[col] = base_price * (1 + np.random.normal(0, 0.01, len(df)))
                        elif col == 'high':
                            df[col] = base_price * (1 + np.abs(np.random.normal(0, 0.02, len(df))))
                        elif col == 'low':
                            df[col] = base_price * (1 - np.abs(np.random.normal(0, 0.02, len(df))))
                    else:
                        df[col] = 10.0  # 默认价格
        
        # 计算衍生字段
        if 'change' not in df.columns:
            df['change'] = df['close'] - df['open']
        
        if 'change_pct' not in df.columns:
            df['change_pct'] = df['change'] / df['open']
        
        if 'amount' not in df.columns:
            df['amount'] = df['close'] * df['volume']
        
        # 数据清洗
        df = df.dropna(subset=['open', 'high', 'low', 'close'])
        df = df[df['volume'] >= 0]
        
        return df
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        """获取实时数据（模拟）"""
        result = {}
        
        for symbol in symbols:
            if symbol in self.data_cache:
                data_point = self._get_next_data_point(symbol)
                if data_point:
                    result[symbol] = data_point
                    self.last_data[symbol] = data_point
            elif symbol in self.last_data:
                # 如果没有新数据，返回最后的数据
                result[symbol] = self.last_data[symbol]
        
        self.last_update = datetime.now()
        return result
    
    def _get_next_data_point(self, symbol: str) -> Optional[MarketDataPoint]:
        """获取下一个数据点"""
        if symbol not in self.data_cache:
            return None
        
        df = self.data_cache[symbol]
        current_idx = self.current_index[symbol]
        
        if current_idx >= len(df):
            # 数据已结束，循环播放或生成新数据
            self.current_index[symbol] = 0
            current_idx = 0
        
        row = df.iloc[current_idx]
        self.current_index[symbol] += 1
        
        # 转换为MarketDataPoint
        data_point = MarketDataPoint(
            symbol=symbol,
            timestamp=datetime.now(),  # 使用当前时间模拟实时
            open=float(row['open']),
            high=float(row['high']),
            low=float(row['low']),
            close=float(row['close']),
            volume=int(row['volume']),
            amount=float(row.get('amount', 0)),
            change=float(row.get('change', 0)),
            change_pct=float(row.get('change_pct', 0)),
            source=self.config.name
        )
        
        return data_point
    
    def get_historical_data(self, symbol: str, start_date: datetime, 
                          end_date: datetime, frequency: DataFrequency) -> pd.DataFrame:
        """获取历史数据"""
        if symbol not in self.data_cache:
            return pd.DataFrame()
        
        df = self.data_cache[symbol].copy()
        
        # 时间过滤
        df = df[(df['timestamp'] >= start_date) & (df['timestamp'] <= end_date)]
        
        # 频率转换（简化实现）
        if frequency != DataFrequency.MINUTE:
            df = self._resample_data(df, frequency)
        
        return df
    
    def _resample_data(self, df: pd.DataFrame, frequency: DataFrequency) -> pd.DataFrame:
        """重采样数据"""
        if df.empty:
            return df
        
        # 设置时间戳为索引
        df_resampled = df.set_index('timestamp')
        
        # 频率映射
        freq_map = {
            DataFrequency.MINUTE_5: '5T',
            DataFrequency.MINUTE_15: '15T',
            DataFrequency.MINUTE_30: '30T',
            DataFrequency.HOUR: '1H',
            DataFrequency.DAILY: '1D'
        }
        
        freq = freq_map.get(frequency, '1T')
        
        # 重采样
        resampled = df_resampled.resample(freq).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()
        
        # 重新计算change和change_pct
        resampled['change'] = resampled['close'] - resampled['open']
        resampled['change_pct'] = resampled['change'] / resampled['open']
        
        return resampled.reset_index()
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        if symbol not in self.data_cache:
            return {}
        
        df = self.data_cache[symbol]
        
        return {
            "symbol": symbol,
            "name": f"股票{symbol}",
            "market": "A股",
            "data_count": len(df),
            "date_range": {
                "start": df['timestamp'].min().isoformat(),
                "end": df['timestamp'].max().isoformat()
            },
            "price_range": {
                "min": float(df['low'].min()),
                "max": float(df['high'].max())
            },
            "avg_volume": int(df['volume'].mean()),
            "source": self.config.name
        }
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码"""
        return symbol in self.data_cache
    
    def start_simulation(self, speed: float = 1.0):
        """启动数据模拟"""
        self.simulation_speed = speed
        self.simulation_running = True
        
        if not self.simulation_thread or not self.simulation_thread.is_alive():
            self.simulation_thread = threading.Thread(target=self._simulation_loop)
            self.simulation_thread.daemon = True
            self.simulation_thread.start()
            logger.info(f"启动CSV数据模拟，速度: {speed}x")
    
    def stop_simulation(self):
        """停止数据模拟"""
        self.simulation_running = False
        logger.info("停止CSV数据模拟")
    
    def _simulation_loop(self):
        """模拟循环"""
        while self.simulation_running and self.is_connected:
            try:
                # 模拟数据更新
                symbols = list(self.data_cache.keys())
                if symbols:
                    self.get_realtime_data(symbols)
                
                # 根据模拟速度调整睡眠时间
                sleep_time = 1.0 / self.simulation_speed
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"数据模拟循环错误: {e}")
                time.sleep(1)
    
    def reset_simulation(self):
        """重置模拟"""
        for symbol in self.current_index:
            self.current_index[symbol] = 0
        logger.info("重置CSV数据模拟")
    
    def get_simulation_progress(self) -> Dict[str, float]:
        """获取模拟进度"""
        progress = {}
        for symbol, df in self.data_cache.items():
            current_idx = self.current_index.get(symbol, 0)
            total_count = len(df)
            progress[symbol] = current_idx / total_count if total_count > 0 else 0
        return progress


class CSVDataSourceFactory:
    """CSV数据源工厂"""
    
    @staticmethod
    def create_from_file(file_path: str, symbols: List[str] = None, 
                        name: str = None) -> CSVDataSource:
        """从文件创建CSV数据源"""
        config = DataSourceConfig(
            source_type="csv_file",
            name=name or f"CSV_{os.path.basename(file_path)}",
            file_path=file_path,
            symbols=symbols or []
        )
        
        return CSVDataSource(config)
    
    @staticmethod
    def create_sample_data(symbol: str, days: int = 30, 
                          base_price: float = 10.0) -> CSVDataSource:
        """创建示例数据"""
        # 生成示例数据
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                             end=datetime.now(), freq='1min')
        
        # 生成价格数据（随机游走）
        returns = np.random.normal(0, 0.001, len(dates))
        prices = base_price * np.exp(np.cumsum(returns))
        
        # 生成OHLC数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            open_price = close * (1 + np.random.normal(0, 0.002))
            high_price = max(open_price, close) * (1 + np.abs(np.random.normal(0, 0.005)))
            low_price = min(open_price, close) * (1 - np.abs(np.random.normal(0, 0.005)))
            volume = np.random.randint(1000000, 10000000)
            
            data.append({
                'timestamp': date,
                'symbol': symbol,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close,
                'volume': volume,
                'amount': close * volume,
                'change': close - open_price,
                'change_pct': (close - open_price) / open_price
            })
        
        # 保存为临时CSV文件
        df = pd.DataFrame(data)
        temp_file = f"temp/sample_data_{symbol}_{days}days.csv"
        os.makedirs("temp", exist_ok=True)
        df.to_csv(temp_file, index=False)
        
        # 创建数据源
        config = DataSourceConfig(
            source_type="csv_file",
            name=f"Sample_{symbol}",
            file_path=temp_file,
            symbols=[symbol]
        )
        
        return CSVDataSource(config)
