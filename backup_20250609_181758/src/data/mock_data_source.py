"""
模拟数据源实现
用于测试和演示的模拟数据生成器
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger
import threading
import time
import random

from .data_source_interface import (
    DataSourceInterface, DataSourceConfig, MarketDataPoint, 
    DataFrequency, DataSourceType
)


class MockDataSource(DataSourceInterface):
    """模拟数据源"""
    
    def __init__(self, config: DataSourceConfig = None):
        if config is None:
            config = DataSourceConfig(
                source_type=DataSourceType.MOCK,
                name="MockDataSource",
                enabled=True,
                priority=3
            )
        
        super().__init__(config)
        
        # 模拟数据参数
        self.base_prices = {}  # 基础价格
        self.price_trends = {}  # 价格趋势
        self.volatilities = {}  # 波动率
        self.volumes_base = {}  # 基础成交量
        
        # 市场状态模拟
        self.market_state = "normal"  # normal, volatile, crash, rally
        self.market_factor = 1.0
        
        # 数据生成线程
        self.generation_thread = None
        self.generation_active = False
        
        # 默认监控的股票
        self.default_symbols = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ',
            '600000.SH', '600036.SH', '600519.SH', '300750.SZ'
        ]
        
        # 初始化股票参数
        self._initialize_stock_params()
    
    def _initialize_stock_params(self):
        """初始化股票参数"""
        for symbol in self.default_symbols:
            # 基础价格 (8-50元)
            self.base_prices[symbol] = random.uniform(8.0, 50.0)
            
            # 价格趋势 (-0.001 到 0.001)
            self.price_trends[symbol] = random.uniform(-0.001, 0.001)
            
            # 波动率 (0.01 到 0.05)
            self.volatilities[symbol] = random.uniform(0.01, 0.05)
            
            # 基础成交量 (100万到1000万)
            self.volumes_base[symbol] = random.randint(1000000, 10000000)
    
    def connect(self) -> bool:
        """连接模拟数据源"""
        try:
            self.is_connected = True
            logger.info("模拟数据源连接成功")
            return True
        except Exception as e:
            logger.error(f"模拟数据源连接失败: {e}")
            self._handle_error(e)
            return False
    
    def disconnect(self) -> bool:
        """断开连接"""
        try:
            self.generation_active = False
            if self.generation_thread and self.generation_thread.is_alive():
                self.generation_thread.join(timeout=3)
            
            self.is_connected = False
            logger.info("模拟数据源已断开连接")
            return True
        except Exception as e:
            logger.error(f"断开模拟数据源失败: {e}")
            return False
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        """获取实时数据"""
        if not self.is_connected:
            raise Exception("模拟数据源未连接")
        
        result = {}
        current_time = datetime.now()
        
        for symbol in symbols:
            # 确保股票参数存在
            if symbol not in self.base_prices:
                self._initialize_stock_param(symbol)
            
            # 生成价格数据
            price_data = self._generate_price_data(symbol)
            
            # 生成成交量
            volume = self._generate_volume(symbol)
            
            # 创建数据点
            data_point = MarketDataPoint(
                symbol=symbol,
                timestamp=current_time,
                open=price_data['open'],
                high=price_data['high'],
                low=price_data['low'],
                close=price_data['close'],
                volume=volume,
                amount=price_data['close'] * volume,
                change=price_data['change'],
                change_pct=price_data['change_pct'],
                bid=price_data['close'] * 0.999,  # 买一价
                ask=price_data['close'] * 1.001,  # 卖一价
                source=self.config.name
            )
            
            result[symbol] = data_point
            
            # 更新基础价格
            self.base_prices[symbol] = price_data['close']
        
        self.last_update = current_time
        return result
    
    def _initialize_stock_param(self, symbol: str):
        """初始化单个股票参数"""
        self.base_prices[symbol] = random.uniform(8.0, 50.0)
        self.price_trends[symbol] = random.uniform(-0.001, 0.001)
        self.volatilities[symbol] = random.uniform(0.01, 0.05)
        self.volumes_base[symbol] = random.randint(1000000, 10000000)
    
    def _generate_price_data(self, symbol: str) -> Dict[str, float]:
        """生成价格数据"""
        base_price = self.base_prices[symbol]
        trend = self.price_trends[symbol]
        volatility = self.volatilities[symbol]
        
        # 应用市场因子
        adjusted_volatility = volatility * self.market_factor
        
        # 生成价格变动
        price_change = np.random.normal(trend, adjusted_volatility)
        
        # 计算新价格
        new_price = base_price * (1 + price_change)
        
        # 生成OHLC数据
        open_price = base_price
        close_price = new_price
        
        # 高低价基于开盘和收盘价
        price_range = abs(close_price - open_price) * random.uniform(1.2, 2.0)
        high_price = max(open_price, close_price) + price_range * random.uniform(0, 0.5)
        low_price = min(open_price, close_price) - price_range * random.uniform(0, 0.5)
        
        # 确保价格合理性
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # 计算涨跌
        change = close_price - open_price
        change_pct = change / open_price if open_price > 0 else 0
        
        return {
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'change': round(change, 2),
            'change_pct': round(change_pct, 4)
        }
    
    def _generate_volume(self, symbol: str) -> int:
        """生成成交量"""
        base_volume = self.volumes_base[symbol]
        
        # 成交量变动 (0.5-2.0倍)
        volume_factor = random.uniform(0.5, 2.0)
        
        # 应用市场因子
        if self.market_state == "volatile":
            volume_factor *= random.uniform(1.5, 3.0)
        elif self.market_state in ["crash", "rally"]:
            volume_factor *= random.uniform(2.0, 5.0)
        
        return int(base_volume * volume_factor)
    
    def get_historical_data(self, symbol: str, start_date: datetime, 
                          end_date: datetime, frequency: DataFrequency) -> pd.DataFrame:
        """获取历史数据"""
        if not self.is_connected:
            raise Exception("模拟数据源未连接")
        
        # 生成时间序列
        if frequency == DataFrequency.DAILY:
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
        elif frequency == DataFrequency.HOUR:
            dates = pd.date_range(start=start_date, end=end_date, freq='H')
        elif frequency == DataFrequency.MINUTE:
            dates = pd.date_range(start=start_date, end=end_date, freq='T')
        else:
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # 确保股票参数存在
        if symbol not in self.base_prices:
            self._initialize_stock_param(symbol)
        
        # 生成历史数据
        data = []
        current_price = self.base_prices[symbol]
        
        for date in dates:
            # 生成价格变动
            trend = self.price_trends[symbol]
            volatility = self.volatilities[symbol]
            price_change = np.random.normal(trend, volatility)
            
            # 计算OHLC
            open_price = current_price
            close_price = current_price * (1 + price_change)
            
            price_range = abs(close_price - open_price) * random.uniform(1.2, 2.0)
            high_price = max(open_price, close_price) + price_range * random.uniform(0, 0.5)
            low_price = min(open_price, close_price) - price_range * random.uniform(0, 0.5)
            
            # 确保价格合理性
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # 生成成交量
            volume = self._generate_volume(symbol)
            
            data.append({
                'timestamp': date,
                'symbol': symbol,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(close_price * volume, 2),
                'change': round(close_price - open_price, 2),
                'change_pct': round((close_price - open_price) / open_price, 4)
            })
            
            current_price = close_price
        
        return pd.DataFrame(data)
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        # 模拟股票信息
        market = "深交所" if symbol.endswith('.SZ') else "上交所"
        
        return {
            "symbol": symbol,
            "name": f"模拟股票{symbol[:6]}",
            "market": market,
            "industry": random.choice(["科技", "金融", "医药", "地产", "能源"]),
            "list_date": "2020-01-01",
            "market_cap": random.randint(1000000000, 100000000000),
            "pe_ratio": round(random.uniform(10, 50), 2),
            "pb_ratio": round(random.uniform(1, 10), 2),
            "source": self.config.name
        }
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码"""
        # 简单的股票代码格式验证
        if len(symbol) >= 6:
            code = symbol[:6]
            return code.isdigit() and (symbol.endswith('.SZ') or symbol.endswith('.SH'))
        return False
    
    def set_market_state(self, state: str, factor: float = 1.0):
        """设置市场状态"""
        valid_states = ["normal", "volatile", "crash", "rally"]
        if state in valid_states:
            self.market_state = state
            self.market_factor = factor
            logger.info(f"市场状态设置为: {state}, 因子: {factor}")
        else:
            logger.warning(f"无效的市场状态: {state}")
    
    def simulate_market_event(self, event_type: str, symbols: List[str] = None):
        """模拟市场事件"""
        target_symbols = symbols or self.default_symbols
        
        if event_type == "crash":
            # 模拟暴跌
            for symbol in target_symbols:
                if symbol in self.base_prices:
                    self.base_prices[symbol] *= random.uniform(0.85, 0.95)
                    self.volatilities[symbol] *= random.uniform(2.0, 4.0)
            logger.info(f"模拟暴跌事件，影响{len(target_symbols)}只股票")
            
        elif event_type == "rally":
            # 模拟暴涨
            for symbol in target_symbols:
                if symbol in self.base_prices:
                    self.base_prices[symbol] *= random.uniform(1.05, 1.15)
                    self.volatilities[symbol] *= random.uniform(1.5, 3.0)
            logger.info(f"模拟暴涨事件，影响{len(target_symbols)}只股票")
            
        elif event_type == "volatility":
            # 模拟高波动
            for symbol in target_symbols:
                if symbol in self.volatilities:
                    self.volatilities[symbol] *= random.uniform(2.0, 5.0)
            logger.info(f"模拟高波动事件，影响{len(target_symbols)}只股票")
    
    def reset_market_params(self):
        """重置市场参数"""
        self.market_state = "normal"
        self.market_factor = 1.0
        
        # 重置股票参数
        for symbol in self.default_symbols:
            self.volatilities[symbol] = random.uniform(0.01, 0.05)
            self.price_trends[symbol] = random.uniform(-0.001, 0.001)
        
        logger.info("市场参数已重置")
    
    def start_auto_generation(self, interval: float = 2.0):
        """启动自动数据生成"""
        if self.generation_active:
            logger.warning("自动数据生成已在运行")
            return
        
        self.generation_active = True
        self.generation_interval = interval
        
        self.generation_thread = threading.Thread(target=self._generation_loop)
        self.generation_thread.daemon = True
        self.generation_thread.start()
        
        logger.info(f"启动自动数据生成，间隔: {interval}秒")
    
    def stop_auto_generation(self):
        """停止自动数据生成"""
        self.generation_active = False
        logger.info("停止自动数据生成")
    
    def _generation_loop(self):
        """数据生成循环"""
        while self.generation_active and self.is_connected:
            try:
                # 随机改变市场状态
                if random.random() < 0.01:  # 1%概率
                    states = ["normal", "volatile"]
                    new_state = random.choice(states)
                    factor = random.uniform(0.5, 2.0)
                    self.set_market_state(new_state, factor)
                
                # 随机模拟市场事件
                if random.random() < 0.005:  # 0.5%概率
                    events = ["crash", "rally", "volatility"]
                    event = random.choice(events)
                    self.simulate_market_event(event)
                
                time.sleep(self.generation_interval)
                
            except Exception as e:
                logger.error(f"数据生成循环错误: {e}")
                time.sleep(self.generation_interval)
