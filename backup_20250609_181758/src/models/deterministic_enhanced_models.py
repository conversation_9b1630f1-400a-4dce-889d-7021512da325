"""
确定性增强随机微积分模型
通过增加确定性因子、改进参数估计和模型校准来提高模型的可预测性
减少纯随机性，增强基于历史数据和市场规律的确定性成分
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Tuple, Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime, timedelta

try:
    from .stochastic_models import BSParameters, BlackScholesModel
except ImportError:
    from src.models.stochastic_models import BSParameters, BlackScholesModel


@dataclass
class DeterministicFactors:
    """确定性因子"""
    trend_component: float = 0.0        # 趋势成分
    mean_reversion_force: float = 0.0   # 均值回归力
    momentum_persistence: float = 0.0   # 动量持续性
    support_resistance: float = 0.0     # 支撑阻力
    seasonal_component: float = 0.0     # 季节成分


@dataclass
class EnhancedDeterministicParameters(BSParameters):
    """确定性增强参数"""
    # 确定性成分权重
    deterministic_weight: float = 0.7   # 确定性成分权重 (0-1)
    stochastic_weight: float = 0.3      # 随机成分权重 (0-1)
    
    # 趋势参数
    trend_strength: float = 0.0         # 趋势强度
    trend_persistence: float = 0.8      # 趋势持续性
    
    # 均值回归参数
    mean_reversion_speed: float = 0.1   # 均值回归速度
    long_term_mean: float = 100.0       # 长期均值
    
    # 技术分析参数
    support_level: float = 0.0          # 支撑位
    resistance_level: float = 0.0       # 阻力位
    
    # 校准参数
    calibration_window: int = 60        # 校准窗口
    update_frequency: int = 5           # 更新频率


class DeterministicEnhancedModel(BlackScholesModel):
    """确定性增强随机微积分模型"""
    
    def __init__(self, params: EnhancedDeterministicParameters):
        super().__init__(params)
        self.det_params = params
        self.price_history = []
        self.return_history = []
        
        # 确定性成分
        self.deterministic_factors = DeterministicFactors()
        
        # 校准状态
        self.last_calibration = None
        self.calibration_count = 0
        
    def update_market_data(self, price: float, volume: float, timestamp: datetime):
        """更新市场数据并重新校准模型"""
        self.price_history.append((timestamp, price))
        
        # 计算收益率
        if len(self.price_history) > 1:
            prev_price = self.price_history[-2][1]
            return_rate = np.log(price / prev_price)
            self.return_history.append((timestamp, return_rate))
        
        # 保持历史数据长度
        max_history = self.det_params.calibration_window * 2
        if len(self.price_history) > max_history:
            self.price_history = self.price_history[-max_history:]
            self.return_history = self.return_history[-max_history:]
        
        # 定期重新校准
        if (len(self.price_history) % self.det_params.update_frequency == 0 and 
            len(self.price_history) >= 20):
            self._recalibrate_model()
    
    def _recalibrate_model(self):
        """重新校准模型参数"""
        if len(self.price_history) < 20:
            return
        
        prices = np.array([p[1] for p in self.price_history[-self.det_params.calibration_window:]])
        returns = np.array([r[1] for r in self.return_history[-self.det_params.calibration_window:]])
        
        # 校准各个成分
        self._calibrate_trend_component(prices)
        self._calibrate_mean_reversion_component(prices)
        self._calibrate_technical_components(prices)
        self._update_deterministic_factors(prices, returns)
        
        self.last_calibration = datetime.now()
        self.calibration_count += 1
        
    def _calibrate_trend_component(self, prices: np.ndarray):
        """校准趋势成分"""
        if len(prices) < 10:
            return
        
        # 使用线性回归计算趋势
        x = np.arange(len(prices))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, prices)
        
        # 趋势强度 = 斜率 / 平均价格
        self.det_params.trend_strength = slope / np.mean(prices) * 252  # 年化
        
        # 趋势持续性 = R²
        self.det_params.trend_persistence = max(0.5, r_value**2)
    
    def _calibrate_mean_reversion_component(self, prices: np.ndarray):
        """校准均值回归成分"""
        if len(prices) < 15:
            return
        
        # 计算长期均值
        self.det_params.long_term_mean = np.mean(prices)
        
        # 使用简化的均值回归速度估计
        log_prices = np.log(prices)
        mean_log_price = np.mean(log_prices)
        
        deviations = log_prices - mean_log_price
        if len(deviations) > 1:
            lagged_deviations = deviations[:-1]
            current_deviations = deviations[1:]
            
            if len(lagged_deviations) > 0 and np.std(lagged_deviations) > 0:
                correlation = np.corrcoef(lagged_deviations, current_deviations)[0, 1]
                if not np.isnan(correlation) and 0 < correlation < 1:
                    self.det_params.mean_reversion_speed = -np.log(correlation)
                else:
                    self.det_params.mean_reversion_speed = 0.1
    
    def _calibrate_technical_components(self, prices: np.ndarray):
        """校准技术分析成分"""
        if len(prices) < 20:
            return
        
        # 计算支撑和阻力位
        recent_prices = prices[-min(30, len(prices)):]
        
        # 支撑位：最近价格的下四分位数
        self.det_params.support_level = np.percentile(recent_prices, 25)
        
        # 阻力位：最近价格的上四分位数
        self.det_params.resistance_level = np.percentile(recent_prices, 75)
    
    def _update_deterministic_factors(self, prices: np.ndarray, returns: np.ndarray):
        """更新确定性因子"""
        current_price = prices[-1]
        
        # 趋势成分
        if len(prices) >= 5:
            recent_trend = (prices[-1] - prices[-5]) / prices[-5] / 5  # 日均变化率
            self.deterministic_factors.trend_component = recent_trend * self.det_params.trend_persistence
        
        # 均值回归力
        deviation = (current_price - self.det_params.long_term_mean) / self.det_params.long_term_mean
        self.deterministic_factors.mean_reversion_force = -self.det_params.mean_reversion_speed * deviation
        
        # 支撑阻力效应
        if current_price <= self.det_params.support_level * 1.02:  # 接近支撑位
            self.deterministic_factors.support_resistance = 0.005  # 向上推力
        elif current_price >= self.det_params.resistance_level * 0.98:  # 接近阻力位
            self.deterministic_factors.support_resistance = -0.005  # 向下推力
        else:
            self.deterministic_factors.support_resistance = 0.0
        
        # 动量持续性
        if len(returns) >= 3:
            recent_returns = returns[-3:]
            momentum = np.mean(recent_returns)
            self.deterministic_factors.momentum_persistence = momentum * 0.5  # 动量衰减
    
    def simulate_deterministic_enhanced(self, n_paths: int, n_steps: int) -> np.ndarray:
        """确定性增强的路径模拟"""
        dt = self.det_params.T / n_steps
        paths = np.zeros((n_paths, n_steps + 1))
        paths[:, 0] = self.det_params.S0
        
        for i in range(1, n_steps + 1):
            # 确定性成分
            deterministic_drift = self._calculate_deterministic_drift(paths[:, i-1], i * dt)
            
            # 随机成分
            dW = np.random.normal(0, np.sqrt(dt), n_paths)
            stochastic_component = self.det_params.sigma * dW
            
            # 组合确定性和随机成分
            total_drift = (self.det_params.deterministic_weight * deterministic_drift + 
                          self.det_params.stochastic_weight * (self.det_params.mu - 0.5 * self.det_params.sigma**2) * dt)
            
            total_diffusion = self.det_params.stochastic_weight * stochastic_component
            
            # 价格更新
            paths[:, i] = paths[:, i-1] * np.exp(total_drift + total_diffusion)
            
            # 确保价格为正
            paths[:, i] = np.maximum(paths[:, i], 0.01)
        
        return paths
    
    def _calculate_deterministic_drift(self, current_prices: np.ndarray, current_time: float) -> np.ndarray:
        """计算确定性漂移项"""
        dt = self.det_params.T / 252  # 假设252个交易日
        
        # 基础漂移
        base_drift = self.det_params.mu * dt
        
        # 趋势成分
        trend_drift = self.deterministic_factors.trend_component * dt
        
        # 均值回归成分
        mean_reversion_drift = self.deterministic_factors.mean_reversion_force * dt
        
        # 动量成分
        momentum_drift = self.deterministic_factors.momentum_persistence * dt
        
        # 支撑阻力成分
        support_resistance_drift = self.deterministic_factors.support_resistance * dt
        
        total_drift = (base_drift + trend_drift + mean_reversion_drift + 
                      momentum_drift + support_resistance_drift)
        
        return np.full_like(current_prices, total_drift)
    
    def get_model_diagnostics(self) -> Dict[str, Any]:
        """获取模型诊断信息"""
        return {
            'calibration_count': self.calibration_count,
            'deterministic_weight': self.det_params.deterministic_weight,
            'trend_strength': self.det_params.trend_strength,
            'mean_reversion_speed': self.det_params.mean_reversion_speed,
            'support_level': self.det_params.support_level,
            'resistance_level': self.det_params.resistance_level,
            'deterministic_factors': {
                'trend_component': self.deterministic_factors.trend_component,
                'mean_reversion_force': self.deterministic_factors.mean_reversion_force,
                'momentum_persistence': self.deterministic_factors.momentum_persistence,
                'support_resistance': self.deterministic_factors.support_resistance
            }
        }


if __name__ == "__main__":
    # 测试确定性增强模型
    params = EnhancedDeterministicParameters(
        S0=100, mu=0.05, sigma=0.2, r=0.03, T=1.0,
        deterministic_weight=0.7, stochastic_weight=0.3
    )
    
    model = DeterministicEnhancedModel(params)
    
    # 模拟历史数据
    for i in range(60):
        timestamp = datetime.now() - timedelta(days=60-i)
        price = 100 + np.random.normal(0, 1) + 0.1 * i  # 带趋势的价格
        volume = 1000000
        model.update_market_data(price, volume, timestamp)
    
    # 生成路径
    paths = model.simulate_deterministic_enhanced(1000, 252)
    
    print("确定性增强模型测试:")
    print(f"路径形状: {paths.shape}")
    print(f"最终价格均值: {np.mean(paths[:, -1]):.2f}")
    print(f"最终价格标准差: {np.std(paths[:, -1]):.2f}")
    
    # 模型诊断
    diagnostics = model.get_model_diagnostics()
    print("\n模型诊断:")
    for key, value in diagnostics.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for k, v in value.items():
                print(f"  {k}: {v:.4f}" if isinstance(v, float) else f"  {k}: {v}")
        else:
            print(f"{key}: {value:.4f}" if isinstance(value, float) else f"{key}: {value}")
