"""
随机微积分模型模块
实现Black-Scholes、<PERSON><PERSON>、CIR等随机微积分方程
用于股票价格建模和期权定价
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from typing import Tuple, Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class ModelParameters:
    """模型参数基类"""
    pass


@dataclass
class BSParameters(ModelParameters):
    """Black-Scholes模型参数"""
    S0: float  # 初始股价
    mu: float  # 漂移率
    sigma: float  # 波动率
    r: float  # 无风险利率
    T: float  # 到期时间
    

@dataclass
class HestonParameters(ModelParameters):
    """Heston模型参数"""
    S0: float  # 初始股价
    V0: float  # 初始方差
    mu: float  # 股价漂移率
    kappa: float  # 方差回归速度
    theta: float  # 长期方差
    sigma_v: float  # 方差的波动率
    rho: float  # 相关系数
    r: float  # 无风险利率
    T: float  # 到期时间


class StochasticModel:
    """随机模型基类"""
    
    def __init__(self, params: ModelParameters):
        self.params = params
        
    def simulate(self, n_paths: int, n_steps: int) -> np.ndarray:
        """模拟路径"""
        raise NotImplementedError
        
    def calibrate(self, market_data: pd.DataFrame) -> ModelParameters:
        """参数校准"""
        raise NotImplementedError


class BlackScholesModel(StochasticModel):
    """Black-Scholes模型"""
    
    def __init__(self, params: BSParameters):
        super().__init__(params)
        
    def simulate(self, n_paths: int, n_steps: int) -> np.ndarray:
        """
        使用几何布朗运动模拟股价路径
        dS = μS dt + σS dW
        """
        dt = self.params.T / n_steps
        paths = np.zeros((n_paths, n_steps + 1))
        paths[:, 0] = self.params.S0
        
        for i in range(1, n_steps + 1):
            dW = np.random.normal(0, np.sqrt(dt), n_paths)
            paths[:, i] = paths[:, i-1] * np.exp(
                (self.params.mu - 0.5 * self.params.sigma**2) * dt + 
                self.params.sigma * dW
            )
            
        return paths
    
    def option_price(self, K: float, option_type: str = "call") -> float:
        """Black-Scholes期权定价公式"""
        S = self.params.S0
        r = self.params.r
        sigma = self.params.sigma
        T = self.params.T
        
        d1 = (np.log(S/K) + (r + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        if option_type.lower() == "call":
            price = S*stats.norm.cdf(d1) - K*np.exp(-r*T)*stats.norm.cdf(d2)
        else:  # put
            price = K*np.exp(-r*T)*stats.norm.cdf(-d2) - S*stats.norm.cdf(-d1)
            
        return price
    
    def greeks(self, K: float, option_type: str = "call") -> Dict[str, float]:
        """计算期权希腊字母"""
        S = self.params.S0
        r = self.params.r
        sigma = self.params.sigma
        T = self.params.T
        
        d1 = (np.log(S/K) + (r + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
        d2 = d1 - sigma*np.sqrt(T)
        
        greeks = {}
        
        if option_type.lower() == "call":
            greeks["delta"] = stats.norm.cdf(d1)
            greeks["theta"] = (-S*stats.norm.pdf(d1)*sigma/(2*np.sqrt(T)) - 
                              r*K*np.exp(-r*T)*stats.norm.cdf(d2))
        else:  # put
            greeks["delta"] = stats.norm.cdf(d1) - 1
            greeks["theta"] = (-S*stats.norm.pdf(d1)*sigma/(2*np.sqrt(T)) + 
                              r*K*np.exp(-r*T)*stats.norm.cdf(-d2))
        
        greeks["gamma"] = stats.norm.pdf(d1) / (S*sigma*np.sqrt(T))
        greeks["vega"] = S*stats.norm.pdf(d1)*np.sqrt(T)
        greeks["rho"] = (K*T*np.exp(-r*T)*stats.norm.cdf(d2) if option_type.lower() == "call" 
                         else -K*T*np.exp(-r*T)*stats.norm.cdf(-d2))
        
        return greeks
    
    def calibrate(self, market_data: pd.DataFrame) -> BSParameters:
        """基于历史数据校准参数"""
        returns = market_data["close"].pct_change().dropna()
        
        mu = returns.mean() * 252  # 年化收益率
        sigma = returns.std() * np.sqrt(252)  # 年化波动率
        
        return BSParameters(
            S0=market_data["close"].iloc[-1],
            mu=mu,
            sigma=sigma,
            r=self.params.r,  # 保持原有无风险利率
            T=self.params.T   # 保持原有到期时间
        )


class HestonModel(StochasticModel):
    """Heston随机波动率模型"""
    
    def __init__(self, params: HestonParameters):
        super().__init__(params)
        
    def simulate(self, n_paths: int, n_steps: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        模拟Heston模型路径
        dS = μS dt + √V S dW1
        dV = κ(θ - V) dt + σ_v √V dW2
        """
        dt = self.params.T / n_steps
        
        S_paths = np.zeros((n_paths, n_steps + 1))
        V_paths = np.zeros((n_paths, n_steps + 1))
        
        S_paths[:, 0] = self.params.S0
        V_paths[:, 0] = self.params.V0
        
        for i in range(1, n_steps + 1):
            # 生成相关的随机数
            dW1 = np.random.normal(0, np.sqrt(dt), n_paths)
            dW2_indep = np.random.normal(0, np.sqrt(dt), n_paths)
            dW2 = self.params.rho * dW1 + np.sqrt(1 - self.params.rho**2) * dW2_indep
            
            # 更新方差（使用全截断方案避免负值）
            V_paths[:, i] = np.maximum(
                V_paths[:, i-1] + self.params.kappa * (self.params.theta - V_paths[:, i-1]) * dt + 
                self.params.sigma_v * np.sqrt(np.maximum(V_paths[:, i-1], 0)) * dW2,
                0
            )
            
            # 更新股价
            S_paths[:, i] = S_paths[:, i-1] * np.exp(
                (self.params.mu - 0.5 * V_paths[:, i-1]) * dt + 
                np.sqrt(np.maximum(V_paths[:, i-1], 0)) * dW1
            )
            
        return S_paths, V_paths
    
    def calibrate(self, market_data: pd.DataFrame, option_data: Optional[pd.DataFrame] = None) -> HestonParameters:
        """校准Heston模型参数"""
        returns = market_data["close"].pct_change().dropna()
        
        # 初始参数估计
        mu = returns.mean() * 252
        V0 = (returns.std() * np.sqrt(252))**2
        
        if option_data is not None:
            # 使用期权数据进行更精确的校准
            return self._calibrate_with_options(market_data, option_data)
        else:
            # 仅使用历史数据的简单校准
            return HestonParameters(
                S0=market_data["close"].iloc[-1],
                V0=V0,
                mu=mu,
                kappa=2.0,  # 默认值
                theta=V0,
                sigma_v=0.3,  # 默认值
                rho=-0.7,  # 默认负相关
                r=self.params.r,
                T=self.params.T
            )
    
    def _calibrate_with_options(self, market_data: pd.DataFrame, option_data: pd.DataFrame) -> HestonParameters:
        """使用期权数据校准参数"""
        # 这里可以实现更复杂的校准算法
        # 例如使用最小二乘法拟合市场期权价格
        pass


class CIRModel:
    """Cox-Ingersoll-Ross利率模型"""
    
    def __init__(self, r0: float, kappa: float, theta: float, sigma: float):
        self.r0 = r0      # 初始利率
        self.kappa = kappa  # 回归速度
        self.theta = theta  # 长期均值
        self.sigma = sigma  # 波动率
        
    def simulate(self, T: float, n_steps: int, n_paths: int) -> np.ndarray:
        """
        模拟CIR利率路径
        dr = κ(θ - r) dt + σ√r dW
        """
        dt = T / n_steps
        paths = np.zeros((n_paths, n_steps + 1))
        paths[:, 0] = self.r0
        
        for i in range(1, n_steps + 1):
            dW = np.random.normal(0, np.sqrt(dt), n_paths)
            dr = (self.kappa * (self.theta - paths[:, i-1]) * dt + 
                  self.sigma * np.sqrt(np.maximum(paths[:, i-1], 0)) * dW)
            paths[:, i] = np.maximum(paths[:, i-1] + dr, 0)
            
        return paths


class JumpDiffusionModel(StochasticModel):
    """跳跃扩散模型（Merton模型）"""
    
    def __init__(self, S0: float, mu: float, sigma: float, lambda_jump: float, 
                 mu_jump: float, sigma_jump: float, r: float, T: float):
        self.S0 = S0
        self.mu = mu
        self.sigma = sigma
        self.lambda_jump = lambda_jump  # 跳跃强度
        self.mu_jump = mu_jump          # 跳跃幅度均值
        self.sigma_jump = sigma_jump    # 跳跃幅度标准差
        self.r = r
        self.T = T
        
    def simulate(self, n_paths: int, n_steps: int) -> np.ndarray:
        """
        模拟跳跃扩散过程
        dS = μS dt + σS dW + S(e^J - 1)dN
        """
        dt = self.T / n_steps
        paths = np.zeros((n_paths, n_steps + 1))
        paths[:, 0] = self.S0
        
        for i in range(1, n_steps + 1):
            # 布朗运动部分
            dW = np.random.normal(0, np.sqrt(dt), n_paths)
            
            # 跳跃部分
            dN = np.random.poisson(self.lambda_jump * dt, n_paths)
            jumps = np.zeros(n_paths)
            
            for j in range(n_paths):
                if dN[j] > 0:
                    jump_sizes = np.random.normal(self.mu_jump, self.sigma_jump, dN[j])
                    jumps[j] = np.sum(np.exp(jump_sizes) - 1)
            
            # 更新股价
            paths[:, i] = paths[:, i-1] * (
                np.exp((self.mu - 0.5 * self.sigma**2) * dt + self.sigma * dW) * 
                (1 + jumps)
            )
            
        return paths


def monte_carlo_option_pricing(model: StochasticModel, K: float, option_type: str = "call", 
                             n_paths: int = 10000, n_steps: int = 252) -> Dict[str, float]:
    """蒙特卡洛期权定价"""
    if isinstance(model, HestonModel):
        S_paths, _ = model.simulate(n_paths, n_steps)
    else:
        S_paths = model.simulate(n_paths, n_steps)
    
    # 计算期权收益
    S_T = S_paths[:, -1]
    
    if option_type.lower() == "call":
        payoffs = np.maximum(S_T - K, 0)
    else:  # put
        payoffs = np.maximum(K - S_T, 0)
    
    # 折现到现值
    r = model.params.r if hasattr(model.params, "r") else model.r
    T = model.params.T if hasattr(model.params, "T") else model.T
    
    option_price = np.exp(-r * T) * np.mean(payoffs)
    std_error = np.exp(-r * T) * np.std(payoffs) / np.sqrt(n_paths)
    
    return {
        "price": option_price,
        "std_error": std_error,
        "confidence_interval": [option_price - 1.96*std_error, option_price + 1.96*std_error]
    }


if __name__ == "__main__":
    # 示例使用
    bs_params = BSParameters(S0=100, mu=0.05, sigma=0.2, r=0.03, T=1.0)
    bs_model = BlackScholesModel(bs_params)
    
    # 模拟路径
    paths = bs_model.simulate(1000, 252)
    print(f"模拟路径形状: {paths.shape}")
    
    # 期权定价
    call_price = bs_model.option_price(105, "call")
    print(f"看涨期权价格: {call_price:.4f}")
    
    # 希腊字母
    greeks = bs_model.greeks(105, "call")
    print(f"Delta: {greeks['delta']:.4f}")
    print(f"Gamma: {greeks['gamma']:.4f}")
