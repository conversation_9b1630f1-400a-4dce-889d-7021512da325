"""
临时套利策略模块
专门处理爆跌、爆涨等极端市场情况的快速套利
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from loguru import logger


class EmergencyStrategy(Enum):
    """临时套利策略类型"""
    CRASH_ARBITRAGE = "crash_arbitrage"  # 爆跌套利
    SURGE_ARBITRAGE = "surge_arbitrage"  # 爆涨套利
    VOLATILITY_ARBITRAGE = "volatility_arbitrage"  # 波动率套利
    PAIRS_DIVERGENCE = "pairs_divergence"  # 配对分化套利


@dataclass
class EmergencySignal:
    """临时套利信号"""
    signal_id: str
    timestamp: datetime
    strategy_type: EmergencyStrategy
    trigger_symbol: str
    reference_symbol: Optional[str]
    action: str  # BUY, SELL, HEDGE
    urgency_level: int  # 1-5, 5最紧急
    confidence: float
    expected_return: float
    max_hold_time: int  # 最大持有时间（分钟）
    entry_price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    position_size_ratio: float  # 建议仓位比例


class CrashArbitrageStrategy:
    """爆跌套利策略"""
    
    def __init__(self, crash_threshold: float = -0.05, recovery_threshold: float = 0.02):
        self.crash_threshold = crash_threshold  # 触发阈值
        self.recovery_threshold = recovery_threshold  # 反弹阈值
        self.active_signals = []
        
    def detect_crash_opportunity(self, market_data: Dict[str, Dict]) -> List[EmergencySignal]:
        """检测爆跌套利机会"""
        signals = []
        
        for symbol, data in market_data.items():
            change = data.get('change', 0)
            price = data.get('price', 0)
            volume = data.get('volume', 0)
            
            # 检测爆跌条件
            if change <= self.crash_threshold and volume > 0:
                # 计算信号强度
                crash_magnitude = abs(change)
                confidence = min(crash_magnitude / abs(self.crash_threshold), 1.0)
                
                # 预期反弹幅度
                expected_return = min(crash_magnitude * 0.3, 0.03)  # 最多3%
                
                # 计算紧急程度
                urgency = self._calculate_urgency(crash_magnitude, volume)
                
                signal = EmergencySignal(
                    signal_id=f"CRASH_{symbol}_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    strategy_type=EmergencyStrategy.CRASH_ARBITRAGE,
                    trigger_symbol=symbol,
                    reference_symbol=None,
                    action="BUY",
                    urgency_level=urgency,
                    confidence=confidence,
                    expected_return=expected_return,
                    max_hold_time=30,  # 30分钟
                    entry_price=price,
                    target_price=price * (1 + expected_return),
                    stop_loss=price * 0.97,  # 3%止损
                    position_size_ratio=min(0.1, confidence * 0.15)  # 最多15%仓位
                )
                
                signals.append(signal)
                logger.info(f"🔴 检测到爆跌套利机会: {symbol} 跌幅{change:.2%}")
        
        return signals
    
    def _calculate_urgency(self, magnitude: float, volume: float) -> int:
        """计算紧急程度"""
        # 基于跌幅和成交量计算紧急程度
        magnitude_score = min(magnitude / 0.1, 1.0) * 3  # 跌幅贡献
        volume_score = min(volume / 1000000, 1.0) * 2   # 成交量贡献
        
        total_score = magnitude_score + volume_score
        return min(int(total_score) + 1, 5)


class SurgeArbitrageStrategy:
    """爆涨套利策略"""
    
    def __init__(self, surge_threshold: float = 0.05, correction_threshold: float = -0.02):
        self.surge_threshold = surge_threshold
        self.correction_threshold = correction_threshold
        self.active_signals = []
    
    def detect_surge_opportunity(self, market_data: Dict[str, Dict]) -> List[EmergencySignal]:
        """检测爆涨套利机会"""
        signals = []
        
        for symbol, data in market_data.items():
            change = data.get('change', 0)
            price = data.get('price', 0)
            volume = data.get('volume', 0)
            
            # 检测爆涨条件
            if change >= self.surge_threshold and volume > 0:
                surge_magnitude = change
                confidence = min(surge_magnitude / self.surge_threshold, 1.0)
                
                # 预期回调幅度
                expected_return = min(surge_magnitude * 0.2, 0.02)  # 最多2%
                
                urgency = self._calculate_urgency(surge_magnitude, volume)
                
                signal = EmergencySignal(
                    signal_id=f"SURGE_{symbol}_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    strategy_type=EmergencyStrategy.SURGE_ARBITRAGE,
                    trigger_symbol=symbol,
                    reference_symbol=None,
                    action="SELL",
                    urgency_level=urgency,
                    confidence=confidence,
                    expected_return=expected_return,
                    max_hold_time=20,  # 20分钟
                    entry_price=price,
                    target_price=price * (1 - expected_return),
                    stop_loss=price * 1.02,  # 2%止损
                    position_size_ratio=min(0.08, confidence * 0.12)  # 最多12%仓位
                )
                
                signals.append(signal)
                logger.info(f"🟢 检测到爆涨套利机会: {symbol} 涨幅{change:.2%}")
        
        return signals
    
    def _calculate_urgency(self, magnitude: float, volume: float) -> int:
        """计算紧急程度"""
        magnitude_score = min(magnitude / 0.1, 1.0) * 3
        volume_score = min(volume / 1000000, 1.0) * 2
        
        total_score = magnitude_score + volume_score
        return min(int(total_score) + 1, 5)


class VolatilityArbitrageStrategy:
    """波动率套利策略"""
    
    def __init__(self, volatility_threshold: float = 0.03):
        self.volatility_threshold = volatility_threshold
        self.price_history = {}
        
    def detect_volatility_opportunity(self, market_data: Dict[str, Dict]) -> List[EmergencySignal]:
        """检测波动率套利机会"""
        signals = []
        
        # 更新价格历史
        self._update_price_history(market_data)
        
        for symbol, data in market_data.items():
            if symbol not in self.price_history or len(self.price_history[symbol]) < 10:
                continue
                
            price = data.get('price', 0)
            
            # 计算短期波动率
            prices = self.price_history[symbol][-10:]
            returns = np.diff(prices) / prices[:-1]
            volatility = np.std(returns)
            
            if volatility >= self.volatility_threshold:
                confidence = min(volatility / self.volatility_threshold, 1.0)
                
                # 波动率套利通常是中性策略
                signal = EmergencySignal(
                    signal_id=f"VOL_{symbol}_{datetime.now().strftime('%H%M%S')}",
                    timestamp=datetime.now(),
                    strategy_type=EmergencyStrategy.VOLATILITY_ARBITRAGE,
                    trigger_symbol=symbol,
                    reference_symbol=None,
                    action="HEDGE",
                    urgency_level=3,
                    confidence=confidence,
                    expected_return=volatility * 0.5,
                    max_hold_time=15,  # 15分钟
                    entry_price=price,
                    target_price=None,
                    stop_loss=None,
                    position_size_ratio=min(0.06, confidence * 0.1)
                )
                
                signals.append(signal)
                logger.info(f"⚡ 检测到波动率套利机会: {symbol} 波动率{volatility:.2%}")
        
        return signals
    
    def _update_price_history(self, market_data: Dict[str, Dict]):
        """更新价格历史"""
        for symbol, data in market_data.items():
            price = data.get('price', 0)
            if price > 0:
                if symbol not in self.price_history:
                    self.price_history[symbol] = []
                
                self.price_history[symbol].append(price)
                
                # 保持最近50个价格
                if len(self.price_history[symbol]) > 50:
                    self.price_history[symbol] = self.price_history[symbol][-50:]


class PairsDivergenceStrategy:
    """配对分化套利策略"""
    
    def __init__(self, divergence_threshold: float = 0.03):
        self.divergence_threshold = divergence_threshold
        self.correlation_history = {}
        
    def detect_pairs_divergence(self, market_data: Dict[str, Dict]) -> List[EmergencySignal]:
        """检测配对分化套利机会"""
        signals = []
        symbols = list(market_data.keys())
        
        # 检查所有股票对
        for i in range(len(symbols)):
            for j in range(i + 1, len(symbols)):
                symbol1, symbol2 = symbols[i], symbols[j]
                
                data1 = market_data[symbol1]
                data2 = market_data[symbol2]
                
                change1 = data1.get('change', 0)
                change2 = data2.get('change', 0)
                
                # 计算分化程度
                divergence = abs(change1 - change2)
                
                if divergence >= self.divergence_threshold:
                    # 确定强弱股票
                    strong_symbol = symbol1 if change1 > change2 else symbol2
                    weak_symbol = symbol2 if change1 > change2 else symbol1
                    
                    confidence = min(divergence / self.divergence_threshold, 1.0)
                    expected_return = divergence * 0.3
                    
                    # 生成做空强势股、做多弱势股的信号
                    signal = EmergencySignal(
                        signal_id=f"PAIR_{strong_symbol}_{weak_symbol}_{datetime.now().strftime('%H%M%S')}",
                        timestamp=datetime.now(),
                        strategy_type=EmergencyStrategy.PAIRS_DIVERGENCE,
                        trigger_symbol=strong_symbol,
                        reference_symbol=weak_symbol,
                        action="PAIRS_TRADE",
                        urgency_level=2,
                        confidence=confidence,
                        expected_return=expected_return,
                        max_hold_time=45,  # 45分钟
                        entry_price=market_data[strong_symbol]['price'],
                        target_price=None,
                        stop_loss=None,
                        position_size_ratio=min(0.05, confidence * 0.08)
                    )
                    
                    signals.append(signal)
                    logger.info(f"🔄 检测到配对分化机会: {strong_symbol}({change1:.2%}) vs {weak_symbol}({change2:.2%})")
        
        return signals


class EmergencyArbitrageEngine:
    """临时套利引擎"""
    
    def __init__(self):
        self.crash_strategy = CrashArbitrageStrategy()
        self.surge_strategy = SurgeArbitrageStrategy()
        self.volatility_strategy = VolatilityArbitrageStrategy()
        self.pairs_strategy = PairsDivergenceStrategy()
        
        self.active_signals = []
        self.executed_signals = []
        self.performance_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'total_return': 0.0,
            'avg_hold_time': 0.0
        }
    
    def scan_emergency_opportunities(self, market_data: Dict[str, Dict]) -> List[EmergencySignal]:
        """扫描所有临时套利机会"""
        all_signals = []
        
        # 爆跌套利
        crash_signals = self.crash_strategy.detect_crash_opportunity(market_data)
        all_signals.extend(crash_signals)
        
        # 爆涨套利
        surge_signals = self.surge_strategy.detect_surge_opportunity(market_data)
        all_signals.extend(surge_signals)
        
        # 波动率套利
        volatility_signals = self.volatility_strategy.detect_volatility_opportunity(market_data)
        all_signals.extend(volatility_signals)
        
        # 配对分化套利
        pairs_signals = self.pairs_strategy.detect_pairs_divergence(market_data)
        all_signals.extend(pairs_signals)
        
        # 按紧急程度和置信度排序
        all_signals.sort(key=lambda x: (x.urgency_level, x.confidence), reverse=True)
        
        # 更新统计
        self.performance_stats['total_signals'] += len(all_signals)
        
        return all_signals
    
    def select_best_emergency_signal(self, signals: List[EmergencySignal]) -> Optional[EmergencySignal]:
        """选择最佳临时套利信号"""
        if not signals:
            return None
        
        # 综合评分：紧急程度 * 0.4 + 置信度 * 0.4 + 预期收益 * 0.2
        def calculate_score(signal):
            urgency_score = signal.urgency_level / 5.0
            confidence_score = signal.confidence
            return_score = min(signal.expected_return / 0.05, 1.0)  # 5%收益得满分
            
            return urgency_score * 0.4 + confidence_score * 0.4 + return_score * 0.2
        
        best_signal = max(signals, key=calculate_score)
        
        logger.info(f"🎯 选择最佳临时套利信号: {best_signal.strategy_type.value}")
        logger.info(f"目标: {best_signal.trigger_symbol}, 操作: {best_signal.action}")
        logger.info(f"紧急程度: {best_signal.urgency_level}/5, 置信度: {best_signal.confidence:.2f}")
        
        return best_signal
    
    def execute_emergency_signal(self, signal: EmergencySignal) -> bool:
        """执行临时套利信号"""
        try:
            self.active_signals.append(signal)
            
            logger.warning(f"🚨 执行临时套利: {signal.signal_id}")
            logger.warning(f"策略: {signal.strategy_type.value}")
            logger.warning(f"操作: {signal.action} {signal.trigger_symbol}")
            logger.warning(f"入场价: ¥{signal.entry_price:.2f}")
            logger.warning(f"预期收益: {signal.expected_return:.2%}")
            logger.warning(f"最大持有: {signal.max_hold_time}分钟")
            
            return True
            
        except Exception as e:
            logger.error(f"临时套利执行失败: {e}")
            return False
    
    def check_signal_expiry(self):
        """检查信号过期"""
        current_time = datetime.now()
        expired_signals = []
        
        for signal in self.active_signals:
            elapsed_minutes = (current_time - signal.timestamp).total_seconds() / 60
            
            if elapsed_minutes >= signal.max_hold_time:
                expired_signals.append(signal)
                logger.info(f"⏰ 临时套利信号过期: {signal.signal_id}")
        
        # 移除过期信号
        for signal in expired_signals:
            self.active_signals.remove(signal)
            self.executed_signals.append(signal)
        
        return expired_signals
    
    def get_emergency_status(self) -> Dict:
        """获取临时套利状态"""
        return {
            'active_signals_count': len(self.active_signals),
            'executed_signals_count': len(self.executed_signals),
            'total_signals': self.performance_stats['total_signals'],
            'success_rate': (self.performance_stats['successful_signals'] / 
                           max(self.performance_stats['total_signals'], 1)),
            'total_return': self.performance_stats['total_return'],
            'active_signals': [
                {
                    'id': signal.signal_id,
                    'strategy': signal.strategy_type.value,
                    'symbol': signal.trigger_symbol,
                    'action': signal.action,
                    'urgency': signal.urgency_level,
                    'confidence': signal.confidence,
                    'elapsed_time': (datetime.now() - signal.timestamp).total_seconds() / 60
                }
                for signal in self.active_signals
            ]
        }


if __name__ == "__main__":
    # 测试临时套利引擎
    engine = EmergencyArbitrageEngine()
    
    # 模拟极端市场数据
    market_data = {
        '000001.SZ': {'price': 10.0, 'change': -0.08, 'volume': 2000000},  # 爆跌8%
        '000002.SZ': {'price': 12.0, 'change': 0.06, 'volume': 1500000},   # 爆涨6%
        '600000.SH': {'price': 8.0, 'change': -0.02, 'volume': 800000},
        '600036.SH': {'price': 45.0, 'change': 0.01, 'volume': 600000}
    }
    
    # 扫描机会
    signals = engine.scan_emergency_opportunities(market_data)
    print(f"检测到 {len(signals)} 个临时套利机会")
    
    # 选择最佳信号
    best_signal = engine.select_best_emergency_signal(signals)
    if best_signal:
        print(f"最佳信号: {best_signal.strategy_type.value}")
        engine.execute_emergency_signal(best_signal)
    
    # 查看状态
    status = engine.get_emergency_status()
    print(f"临时套利状态: {status}")
