"""
策略比较和优选模块
在生成交易信号前比较不同策略，选择最优策略
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import sqlite3
from loguru import logger


@dataclass
class StrategyPerformance:
    """策略性能指标"""
    strategy_name: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    avg_return_per_trade: float
    volatility: float
    calmar_ratio: float
    sortino_ratio: float
    score: float
    confidence: float


@dataclass
class StrategySignal:
    """策略信号"""
    strategy_name: str
    symbol1: str
    symbol2: str
    signal_type: str
    confidence: float
    expected_return: float
    risk_level: str
    entry_price1: float
    entry_price2: float
    timestamp: datetime
    performance_score: float


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.historical_performance = []
        self.recent_signals = []
    
    @abstractmethod
    def generate_signal(self, market_data: Dict) -> Optional[StrategySignal]:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def calculate_performance(self, historical_data: pd.DataFrame) -> StrategyPerformance:
        """计算策略性能"""
        pass


class PairTradingStrategy(BaseStrategy):
    """配对交易策略"""
    
    def __init__(self, lookback_period: int = 60, entry_threshold: float = 2.0):
        super().__init__("配对交易")
        self.lookback_period = lookback_period
        self.entry_threshold = entry_threshold
        self.price_history = {}
    
    def generate_signal(self, market_data: Dict) -> Optional[StrategySignal]:
        """生成配对交易信号"""
        # 简化的配对交易逻辑
        symbols = list(market_data.keys())
        if len(symbols) < 2:
            return None
        
        # 选择两只相关性较高的股票
        symbol1, symbol2 = symbols[0], symbols[1]
        price1, price2 = market_data[symbol1]['price'], market_data[symbol2]['price']
        
        # 计算价格比率和Z-score
        ratio = price1 / price2
        mean_ratio = 1.0  # 简化假设
        std_ratio = 0.1
        z_score = (ratio - mean_ratio) / std_ratio
        
        if abs(z_score) > self.entry_threshold:
            signal_type = "SHORT_LONG" if z_score > 0 else "LONG_SHORT"
            confidence = min(abs(z_score) / self.entry_threshold, 1.0)
            
            return StrategySignal(
                strategy_name=self.name,
                symbol1=symbol1,
                symbol2=symbol2,
                signal_type=signal_type,
                confidence=confidence,
                expected_return=abs(z_score) * 0.01,
                risk_level="MEDIUM",
                entry_price1=price1,
                entry_price2=price2,
                timestamp=datetime.now(),
                performance_score=0.0  # 将在优化器中计算
            )
        
        return None
    
    def calculate_performance(self, historical_data: pd.DataFrame) -> StrategyPerformance:
        """计算配对交易策略性能"""
        # 模拟历史性能计算
        returns = np.random.normal(0.001, 0.02, 252)  # 模拟日收益率
        
        total_return = np.prod(1 + returns) - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
        max_drawdown = self._calculate_max_drawdown(returns)
        win_rate = len(returns[returns > 0]) / len(returns)
        volatility = np.std(returns) * np.sqrt(252)
        
        # 计算其他指标
        calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
        downside_returns = returns[returns < 0]
        sortino_ratio = np.mean(returns) / np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        
        # 综合评分
        score = self._calculate_strategy_score(total_return, sharpe_ratio, max_drawdown, win_rate)
        
        return StrategyPerformance(
            strategy_name=self.name,
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            avg_return_per_trade=np.mean(returns),
            volatility=volatility,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio,
            score=score,
            confidence=0.8
        )
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """计算最大回撤"""
        cumulative = np.cumprod(1 + returns)
        peak = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - peak) / peak
        return np.min(drawdown)
    
    def _calculate_strategy_score(self, total_return: float, sharpe_ratio: float, 
                                max_drawdown: float, win_rate: float) -> float:
        """计算策略综合评分"""
        # 权重分配
        weights = {
            'return': 0.3,
            'sharpe': 0.3,
            'drawdown': 0.2,
            'win_rate': 0.2
        }
        
        # 标准化指标 (0-1)
        return_score = min(max(total_return * 2, 0), 1)  # 50%收益率得满分
        sharpe_score = min(max(sharpe_ratio / 2, 0), 1)  # 夏普比率2得满分
        drawdown_score = max(1 + max_drawdown * 5, 0)  # 回撤越小分数越高
        win_rate_score = win_rate
        
        score = (weights['return'] * return_score + 
                weights['sharpe'] * sharpe_score + 
                weights['drawdown'] * drawdown_score + 
                weights['win_rate'] * win_rate_score)
        
        return score


class StatisticalArbitrageStrategy(BaseStrategy):
    """统计套利策略"""
    
    def __init__(self, threshold: float = 0.05):
        super().__init__("统计套利")
        self.threshold = threshold
    
    def generate_signal(self, market_data: Dict) -> Optional[StrategySignal]:
        """生成统计套利信号"""
        symbols = list(market_data.keys())
        if len(symbols) < 2:
            return None
        
        # 寻找行业内分化机会
        prices = [market_data[symbol]['price'] for symbol in symbols]
        changes = [market_data[symbol].get('change', 0) for symbol in symbols]
        
        if len(changes) < 2:
            return None
        
        # 找到表现差异最大的两只股票
        max_change_idx = np.argmax(changes)
        min_change_idx = np.argmin(changes)
        
        if max_change_idx == min_change_idx:
            return None
        
        change_diff = changes[max_change_idx] - changes[min_change_idx]
        
        if abs(change_diff) > self.threshold:
            symbol1 = symbols[max_change_idx]
            symbol2 = symbols[min_change_idx]
            
            return StrategySignal(
                strategy_name=self.name,
                symbol1=symbol1,
                symbol2=symbol2,
                signal_type="SHORT_LONG",  # 卖出强势，买入弱势
                confidence=min(abs(change_diff) / self.threshold, 1.0),
                expected_return=abs(change_diff) * 0.5,
                risk_level="MEDIUM",
                entry_price1=prices[max_change_idx],
                entry_price2=prices[min_change_idx],
                timestamp=datetime.now(),
                performance_score=0.0
            )
        
        return None
    
    def calculate_performance(self, historical_data: pd.DataFrame) -> StrategyPerformance:
        """计算统计套利策略性能"""
        # 模拟性能计算
        returns = np.random.normal(0.0008, 0.015, 252)
        
        total_return = np.prod(1 + returns) - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
        max_drawdown = self._calculate_max_drawdown(returns)
        win_rate = len(returns[returns > 0]) / len(returns)
        volatility = np.std(returns) * np.sqrt(252)
        
        calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
        downside_returns = returns[returns < 0]
        sortino_ratio = np.mean(returns) / np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        
        score = self._calculate_strategy_score(total_return, sharpe_ratio, max_drawdown, win_rate)
        
        return StrategyPerformance(
            strategy_name=self.name,
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            avg_return_per_trade=np.mean(returns),
            volatility=volatility,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio,
            score=score,
            confidence=0.75
        )
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """计算最大回撤"""
        cumulative = np.cumprod(1 + returns)
        peak = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - peak) / peak
        return np.min(drawdown)
    
    def _calculate_strategy_score(self, total_return: float, sharpe_ratio: float, 
                                max_drawdown: float, win_rate: float) -> float:
        """计算策略综合评分"""
        weights = {
            'return': 0.25,
            'sharpe': 0.35,
            'drawdown': 0.25,
            'win_rate': 0.15
        }
        
        return_score = min(max(total_return * 2.5, 0), 1)
        sharpe_score = min(max(sharpe_ratio / 1.8, 0), 1)
        drawdown_score = max(1 + max_drawdown * 6, 0)
        win_rate_score = win_rate
        
        score = (weights['return'] * return_score + 
                weights['sharpe'] * sharpe_score + 
                weights['drawdown'] * drawdown_score + 
                weights['win_rate'] * win_rate_score)
        
        return score


class VolatilityArbitrageStrategy(BaseStrategy):
    """波动率套利策略"""
    
    def __init__(self, vol_threshold: float = 0.3):
        super().__init__("波动率套利")
        self.vol_threshold = vol_threshold
    
    def generate_signal(self, market_data: Dict) -> Optional[StrategySignal]:
        """生成波动率套利信号"""
        symbols = list(market_data.keys())
        if len(symbols) < 1:
            return None
        
        symbol = symbols[0]
        price = market_data[symbol]['price']
        
        # 模拟波动率计算
        historical_vol = np.random.uniform(0.15, 0.45)
        implied_vol = np.random.uniform(0.10, 0.50)
        
        vol_diff = abs(historical_vol - implied_vol)
        
        if vol_diff > self.vol_threshold:
            signal_type = "LONG_VOL" if historical_vol > implied_vol else "SHORT_VOL"
            
            return StrategySignal(
                strategy_name=self.name,
                symbol1=symbol,
                symbol2="IMPLIED_VOL",
                signal_type=signal_type,
                confidence=min(vol_diff / self.vol_threshold, 1.0),
                expected_return=vol_diff * 0.1,
                risk_level="HIGH",
                entry_price1=price,
                entry_price2=implied_vol,
                timestamp=datetime.now(),
                performance_score=0.0
            )
        
        return None
    
    def calculate_performance(self, historical_data: pd.DataFrame) -> StrategyPerformance:
        """计算波动率套利策略性能"""
        returns = np.random.normal(0.0012, 0.025, 252)
        
        total_return = np.prod(1 + returns) - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
        max_drawdown = self._calculate_max_drawdown(returns)
        win_rate = len(returns[returns > 0]) / len(returns)
        volatility = np.std(returns) * np.sqrt(252)
        
        calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
        downside_returns = returns[returns < 0]
        sortino_ratio = np.mean(returns) / np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        
        score = self._calculate_strategy_score(total_return, sharpe_ratio, max_drawdown, win_rate)
        
        return StrategyPerformance(
            strategy_name=self.name,
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            avg_return_per_trade=np.mean(returns),
            volatility=volatility,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio,
            score=score,
            confidence=0.7
        )
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """计算最大回撤"""
        cumulative = np.cumprod(1 + returns)
        peak = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - peak) / peak
        return np.min(drawdown)
    
    def _calculate_strategy_score(self, total_return: float, sharpe_ratio: float, 
                                max_drawdown: float, win_rate: float) -> float:
        """计算策略综合评分"""
        weights = {
            'return': 0.4,
            'sharpe': 0.25,
            'drawdown': 0.2,
            'win_rate': 0.15
        }
        
        return_score = min(max(total_return * 1.8, 0), 1)
        sharpe_score = min(max(sharpe_ratio / 1.5, 0), 1)
        drawdown_score = max(1 + max_drawdown * 4, 0)
        win_rate_score = win_rate
        
        score = (weights['return'] * return_score + 
                weights['sharpe'] * sharpe_score + 
                weights['drawdown'] * drawdown_score + 
                weights['win_rate'] * win_rate_score)
        
        return score


class StrategyOptimizer:
    """策略优化器"""
    
    def __init__(self):
        self.strategies = [
            PairTradingStrategy(),
            StatisticalArbitrageStrategy(),
            VolatilityArbitrageStrategy()
        ]
        self.performance_history = {}
        self.comparison_results = []
    
    def evaluate_strategies(self, market_data: Dict, historical_data: pd.DataFrame = None) -> List[Tuple[BaseStrategy, StrategyPerformance, Optional[StrategySignal]]]:
        """评估所有策略"""
        results = []
        
        for strategy in self.strategies:
            # 计算策略性能
            if historical_data is not None:
                performance = strategy.calculate_performance(historical_data)
            else:
                # 使用模拟数据
                performance = strategy.calculate_performance(pd.DataFrame())
            
            # 生成信号
            signal = strategy.generate_signal(market_data)
            if signal:
                signal.performance_score = performance.score
            
            results.append((strategy, performance, signal))
            
            # 记录性能历史
            if strategy.name not in self.performance_history:
                self.performance_history[strategy.name] = []
            self.performance_history[strategy.name].append(performance)
        
        return results
    
    def select_best_strategy(self, market_data: Dict, historical_data: pd.DataFrame = None) -> Tuple[Optional[StrategySignal], Dict]:
        """选择最优策略"""
        evaluation_results = self.evaluate_strategies(market_data, historical_data)
        
        # 过滤出有信号的策略
        valid_results = [(strategy, performance, signal) for strategy, performance, signal in evaluation_results if signal is not None]
        
        if not valid_results:
            return None, self._create_comparison_summary(evaluation_results)
        
        # 根据综合评分选择最优策略
        best_result = max(valid_results, key=lambda x: x[1].score * x[2].confidence)
        best_strategy, best_performance, best_signal = best_result
        
        # 记录比较结果
        comparison_result = {
            'timestamp': datetime.now(),
            'market_data': market_data,
            'evaluation_results': evaluation_results,
            'selected_strategy': best_strategy.name,
            'selected_signal': best_signal,
            'selection_reason': f"最高综合评分: {best_performance.score:.3f}"
        }
        self.comparison_results.append(comparison_result)
        
        # 创建比较摘要
        comparison_summary = self._create_comparison_summary(evaluation_results)
        comparison_summary['selected_strategy'] = best_strategy.name
        comparison_summary['selection_score'] = best_performance.score
        
        logger.info(f"选择最优策略: {best_strategy.name}, 评分: {best_performance.score:.3f}")
        
        return best_signal, comparison_summary
    
    def _create_comparison_summary(self, evaluation_results: List) -> Dict:
        """创建策略比较摘要"""
        summary = {
            'timestamp': datetime.now(),
            'strategies_compared': len(evaluation_results),
            'strategy_performances': {},
            'ranking': [],
            'signals_generated': 0
        }
        
        # 收集性能数据
        performances = []
        for strategy, performance, signal in evaluation_results:
            summary['strategy_performances'][strategy.name] = {
                'total_return': performance.total_return,
                'sharpe_ratio': performance.sharpe_ratio,
                'max_drawdown': performance.max_drawdown,
                'win_rate': performance.win_rate,
                'score': performance.score,
                'has_signal': signal is not None
            }
            
            performances.append((strategy.name, performance.score))
            
            if signal is not None:
                summary['signals_generated'] += 1
        
        # 排序
        performances.sort(key=lambda x: x[1], reverse=True)
        summary['ranking'] = performances
        
        return summary
    
    def get_strategy_comparison_data(self) -> Dict:
        """获取策略比较数据用于可视化"""
        if not self.comparison_results:
            return {}
        
        latest_comparison = self.comparison_results[-1]
        
        # 准备可视化数据
        strategy_names = []
        scores = []
        returns = []
        sharpe_ratios = []
        max_drawdowns = []
        win_rates = []
        has_signals = []
        
        for strategy, performance, signal in latest_comparison['evaluation_results']:
            strategy_names.append(strategy.name)
            scores.append(performance.score)
            returns.append(performance.total_return)
            sharpe_ratios.append(performance.sharpe_ratio)
            max_drawdowns.append(abs(performance.max_drawdown))
            win_rates.append(performance.win_rate)
            has_signals.append(signal is not None)
        
        return {
            'strategy_names': strategy_names,
            'scores': scores,
            'returns': returns,
            'sharpe_ratios': sharpe_ratios,
            'max_drawdowns': max_drawdowns,
            'win_rates': win_rates,
            'has_signals': has_signals,
            'selected_strategy': latest_comparison.get('selected_strategy'),
            'timestamp': latest_comparison['timestamp']
        }


if __name__ == "__main__":
    # 测试策略优化器
    optimizer = StrategyOptimizer()
    
    # 模拟市场数据
    market_data = {
        '000001.SZ': {'price': 10.5, 'change': 0.02},
        '000002.SZ': {'price': 12.3, 'change': -0.01},
        '600000.SH': {'price': 8.7, 'change': 0.03}
    }
    
    # 选择最优策略
    best_signal, comparison = optimizer.select_best_strategy(market_data)
    
    if best_signal:
        print(f"最优策略: {best_signal.strategy_name}")
        print(f"信号类型: {best_signal.signal_type}")
        print(f"置信度: {best_signal.confidence:.2f}")
    else:
        print("未生成交易信号")
    
    print(f"\n策略比较结果:")
    for strategy_name, perf in comparison['strategy_performances'].items():
        print(f"{strategy_name}: 评分={perf['score']:.3f}, 有信号={perf['has_signal']}")
