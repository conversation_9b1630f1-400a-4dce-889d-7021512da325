"""
沪深股市交易规则模块
实现A股市场的交易规则、限制和约束
"""

import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import holidays


class MarketType(Enum):
    """市场类型"""
    SHANGHAI = "SH"  # 上海证券交易所
    SHENZHEN = "SZ"  # 深圳证券交易所
    BEIJING = "BJ"   # 北京证券交易所


class StockType(Enum):
    """股票类型"""
    MAIN_BOARD = "主板"
    SME_BOARD = "中小板"
    CHINEXT = "创业板"
    STAR_MARKET = "科创板"
    NEEQ = "新三板"


@dataclass
class TradingSession:
    """交易时段"""
    start_time: time
    end_time: time
    session_name: str


@dataclass
class PriceLimits:
    """价格限制"""
    up_limit: float
    down_limit: float
    tick_size: float


class ChinaStockMarketRules:
    """中国A股市场交易规则"""
    
    def __init__(self):
        # 交易时间设置
        self.trading_sessions = {
            "morning": TradingSession(time(9, 30), time(11, 30), "上午交易"),
            "afternoon": TradingSession(time(13, 0), time(15, 0), "下午交易"),
            "call_auction_open": TradingSession(time(9, 15), time(9, 25), "开盘集合竞价"),
            "call_auction_close": TradingSession(time(14, 57), time(15, 0), "收盘集合竞价")
        }
        
        # 涨跌停限制
        self.price_limit_rules = {
            StockType.MAIN_BOARD: 0.10,      # 主板±10%
            StockType.SME_BOARD: 0.10,       # 中小板±10%
            StockType.CHINEXT: 0.20,         # 创业板±20%
            StockType.STAR_MARKET: 0.20,     # 科创板±20%
            StockType.NEEQ: 0.30             # 新三板±30%
        }
        
        # 最小价格变动单位（分）
        self.tick_sizes = {
            "below_2": 0.01,      # 2元以下，1分
            "2_to_5": 0.01,       # 2-5元，1分
            "5_to_10": 0.01,      # 5-10元，1分
            "10_to_20": 0.01,     # 10-20元，1分
            "20_to_100": 0.01,    # 20-100元，1分
            "above_100": 0.01     # 100元以上，1分
        }
        
        # 交易单位
        self.lot_size = 100  # 1手 = 100股
        
        # 中国节假日
        self.china_holidays = holidays.China()
        
    def is_trading_day(self, date: Union[datetime, str]) -> bool:
        """判断是否为交易日"""
        if isinstance(date, str):
            date = datetime.strptime(date, "%Y-%m-%d")
            
        # 周末不交易
        if date.weekday() >= 5:
            return False
            
        # 节假日不交易
        if date.date() in self.china_holidays:
            return False
            
        return True
    
    def is_trading_time(self, dt: datetime) -> bool:
        """判断是否在交易时间内"""
        if not self.is_trading_day(dt):
            return False
            
        current_time = dt.time()
        
        # 检查是否在交易时段内
        morning_session = self.trading_sessions["morning"]
        afternoon_session = self.trading_sessions["afternoon"]
        
        return (morning_session.start_time <= current_time <= morning_session.end_time or
                afternoon_session.start_time <= current_time <= afternoon_session.end_time)
    
    def get_price_limits(self, prev_close: float, stock_type: StockType) -> PriceLimits:
        """计算涨跌停价格"""
        limit_ratio = self.price_limit_rules[stock_type]
        
        up_limit = prev_close * (1 + limit_ratio)
        down_limit = prev_close * (1 - limit_ratio)
        
        # 价格精度处理
        up_limit = round(up_limit, 2)
        down_limit = round(down_limit, 2)
        
        tick_size = self.get_tick_size(prev_close)
        
        return PriceLimits(up_limit, down_limit, tick_size)
    
    def get_tick_size(self, price: float) -> float:
        """获取最小价格变动单位"""
        return 0.01  # A股统一为1分
    
    def validate_order_price(self, price: float, prev_close: float, stock_type: StockType) -> Tuple[bool, str]:
        """验证订单价格是否合规"""
        limits = self.get_price_limits(prev_close, stock_type)
        
        if price > limits.up_limit:
            return False, f"价格{price}超过涨停价{limits.up_limit}"
        
        if price < limits.down_limit:
            return False, f"价格{price}低于跌停价{limits.down_limit}"
        
        # 检查价格精度
        if round(price, 2) != price:
            return False, f"价格精度不符合要求，应为{limits.tick_size}的整数倍"
        
        return True, "价格合规"
    
    def validate_order_quantity(self, quantity: int) -> Tuple[bool, str]:
        """验证订单数量是否合规"""
        if quantity <= 0:
            return False, "数量必须大于0"
        
        if quantity % self.lot_size != 0:
            return False, f"数量必须是{self.lot_size}的整数倍"
        
        return True, "数量合规"
    
    def calculate_transaction_cost(self, amount: float, is_buy: bool = True) -> Dict[str, float]:
        """计算交易成本"""
        costs = {}
        
        # 佣金（双向收取，最低5元）
        commission_rate = 0.0003  # 万分之三
        commission = max(amount * commission_rate, 5.0)
        costs["commission"] = commission
        
        # 印花税（仅卖出收取）
        if not is_buy:
            stamp_tax_rate = 0.001  # 千分之一
            costs["stamp_tax"] = amount * stamp_tax_rate
        else:
            costs["stamp_tax"] = 0.0
        
        # 过户费（双向收取）
        transfer_fee_rate = 0.00002  # 万分之0.2
        costs["transfer_fee"] = amount * transfer_fee_rate
        
        # 总成本
        costs["total_cost"] = sum(costs.values())
        
        return costs
    
    def get_next_trading_day(self, date: Union[datetime, str], days: int = 1) -> datetime:
        """获取下一个交易日"""
        if isinstance(date, str):
            date = datetime.strptime(date, "%Y-%m-%d")
        
        current_date = date
        count = 0
        
        while count < days:
            current_date += timedelta(days=1)
            if self.is_trading_day(current_date):
                count += 1
        
        return current_date
    
    def get_trading_calendar(self, start_date: str, end_date: str) -> List[datetime]:
        """获取交易日历"""
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        trading_days = []
        current = start
        
        while current <= end:
            if self.is_trading_day(current):
                trading_days.append(current)
            current += timedelta(days=1)
        
        return trading_days


class OrderValidator:
    """订单验证器"""
    
    def __init__(self, market_rules: ChinaStockMarketRules):
        self.market_rules = market_rules
    
    def validate_order(self, order: Dict) -> Tuple[bool, List[str]]:
        """验证订单"""
        errors = []
        
        # 检查必要字段
        required_fields = ["symbol", "price", "quantity", "side", "prev_close", "stock_type"]
        for field in required_fields:
            if field not in order:
                errors.append(f"缺少必要字段: {field}")
        
        if errors:
            return False, errors
        
        # 验证价格
        price_valid, price_msg = self.market_rules.validate_order_price(
            order["price"], order["prev_close"], order["stock_type"]
        )
        if not price_valid:
            errors.append(price_msg)
        
        # 验证数量
        qty_valid, qty_msg = self.market_rules.validate_order_quantity(order["quantity"])
        if not qty_valid:
            errors.append(qty_msg)
        
        # 验证交易时间
        if "timestamp" in order:
            if not self.market_rules.is_trading_time(order["timestamp"]):
                errors.append("当前不在交易时间内")
        
        return len(errors) == 0, errors


class PositionManager:
    """持仓管理器"""
    
    def __init__(self, market_rules: ChinaStockMarketRules):
        self.market_rules = market_rules
        self.positions = {}  # {symbol: {quantity: int, avg_cost: float, market_value: float}}
    
    def update_position(self, symbol: str, quantity: int, price: float, side: str):
        """更新持仓"""
        if symbol not in self.positions:
            self.positions[symbol] = {"quantity": 0, "avg_cost": 0.0, "market_value": 0.0}
        
        pos = self.positions[symbol]
        
        if side.lower() == "buy":
            # 买入
            total_cost = pos["quantity"] * pos["avg_cost"] + quantity * price
            total_quantity = pos["quantity"] + quantity
            pos["avg_cost"] = total_cost / total_quantity if total_quantity > 0 else 0
            pos["quantity"] = total_quantity
        else:
            # 卖出
            pos["quantity"] -= quantity
            if pos["quantity"] < 0:
                raise ValueError(f"卖出数量超过持仓: {symbol}")
    
    def get_position(self, symbol: str) -> Dict:
        """获取持仓信息"""
        return self.positions.get(symbol, {"quantity": 0, "avg_cost": 0.0, "market_value": 0.0})
    
    def update_market_values(self, prices: Dict[str, float]):
        """更新市值"""
        for symbol, pos in self.positions.items():
            if symbol in prices:
                pos["market_value"] = pos["quantity"] * prices[symbol]
    
    def get_total_market_value(self) -> float:
        """获取总市值"""
        return sum(pos["market_value"] for pos in self.positions.values())
    
    def get_pnl(self, symbol: str = None) -> float:
        """获取盈亏"""
        if symbol:
            pos = self.positions.get(symbol, {})
            if pos:
                return pos["market_value"] - pos["quantity"] * pos["avg_cost"]
            return 0.0
        else:
            total_pnl = 0.0
            for pos in self.positions.values():
                total_pnl += pos["market_value"] - pos["quantity"] * pos["avg_cost"]
            return total_pnl


class RiskManager:
    """风险管理器"""
    
    def __init__(self, max_position_ratio: float = 0.1, max_daily_loss: float = 0.05):
        self.max_position_ratio = max_position_ratio  # 单只股票最大仓位比例
        self.max_daily_loss = max_daily_loss          # 最大日损失比例
        self.daily_pnl = 0.0
        self.initial_capital = 0.0
    
    def set_initial_capital(self, capital: float):
        """设置初始资金"""
        self.initial_capital = capital
    
    def check_position_limit(self, symbol: str, quantity: int, price: float, 
                           total_capital: float) -> Tuple[bool, str]:
        """检查仓位限制"""
        position_value = quantity * price
        position_ratio = position_value / total_capital
        
        if position_ratio > self.max_position_ratio:
            return False, f"单只股票仓位比例{position_ratio:.2%}超过限制{self.max_position_ratio:.2%}"
        
        return True, "仓位检查通过"
    
    def check_daily_loss_limit(self, current_pnl: float) -> Tuple[bool, str]:
        """检查日损失限制"""
        if self.initial_capital == 0:
            return True, "未设置初始资金"
        
        loss_ratio = abs(current_pnl) / self.initial_capital
        
        if current_pnl < 0 and loss_ratio > self.max_daily_loss:
            return False, f"日损失比例{loss_ratio:.2%}超过限制{self.max_daily_loss:.2%}"
        
        return True, "损失检查通过"
    
    def update_daily_pnl(self, pnl: float):
        """更新日盈亏"""
        self.daily_pnl = pnl


if __name__ == "__main__":
    # 示例使用
    market_rules = ChinaStockMarketRules()
    
    # 检查交易日
    print(f"今天是否交易日: {market_rules.is_trading_day(datetime.now())}")
    
    # 计算涨跌停价格
    limits = market_rules.get_price_limits(10.0, StockType.MAIN_BOARD)
    print(f"涨停价: {limits.up_limit}, 跌停价: {limits.down_limit}")
    
    # 验证订单
    validator = OrderValidator(market_rules)
    order = {
        "symbol": "000001.SZ",
        "price": 10.5,
        "quantity": 100,
        "side": "buy",
        "prev_close": 10.0,
        "stock_type": StockType.MAIN_BOARD
    }
    
    is_valid, errors = validator.validate_order(order)
    print(f"订单验证结果: {is_valid}, 错误: {errors}")
    
    # 计算交易成本
    costs = market_rules.calculate_transaction_cost(10000, is_buy=True)
    print(f"交易成本: {costs}")
