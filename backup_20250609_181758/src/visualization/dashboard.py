"""
专业量化套利系统仪表板
提供实时监控、策略分析、风险管理等功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import time
import sys
import os
from typing import Dict, List, Optional, Any

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from src.risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
    from src.strategies.strategy_combiner import StrategyCombiner, CombinationMethod
    from src.strategies.strategy_optimizer import StrategyOptimizer
    from src.database.signal_database import SignalDatabase
    from src.strategies.emergency_arbitrage import EmergencyArbitrageEngine
except ImportError as e:
    st.error(f"模块导入失败: {e}")
    st.stop()


class QuantitativeDashboard:
    """量化套利系统仪表板"""

    def __init__(self):
        self.risk_manager = None
        self.strategy_combiner = None
        self.strategy_optimizer = None
        self.database = None
        self.emergency_engine = None

        # 初始化组件
        self._initialize_components()

        # 仪表板状态
        self.last_update = datetime.now()
        self.update_interval = 5  # 秒

    def _initialize_components(self):
        """初始化系统组件"""
        try:
            # 动态风险管理器
            limits = DynamicPositionLimits(initial_capital=200000)
            self.risk_manager = DynamicRiskManager(limits)

            # 策略组合器
            self.strategy_combiner = StrategyCombiner(CombinationMethod.DYNAMIC_WEIGHT)

            # 策略优化器
            self.strategy_optimizer = StrategyOptimizer()

            # 数据库
            self.database = SignalDatabase("dashboard.db")

            # 临时套利引擎
            self.emergency_engine = EmergencyArbitrageEngine()

        except Exception as e:
            st.error(f"组件初始化失败: {e}")

    def render_main_dashboard(self):
        """渲染主仪表板"""
        st.set_page_config(
            page_title="量化套利系统仪表板",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="expanded"
        )

        # 主标题
        st.title("📊 量化套利系统专业仪表板")
        st.markdown("---")

        # 侧边栏
        self._render_sidebar()

        # 主要内容区域
        self._render_main_content()

        # 底部状态栏
        self._render_status_bar()

    def _render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("🎛️ 系统控制")

            # 系统状态
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🚀 启动系统", type="primary"):
                    st.session_state.system_running = True
                    st.success("系统已启动")

            with col2:
                if st.button("⏹️ 停止系统"):
                    st.session_state.system_running = False
                    st.info("系统已停止")

            # 系统状态显示
            status = "🟢 运行中" if st.session_state.get('system_running', False) else "🔴 已停止"
            st.markdown(f"**状态**: {status}")

            st.markdown("---")

            # 风险管理设置
            st.header("⚠️ 风险管理")

            # 交易模式
            trading_mode = st.selectbox(
                "交易模式",
                ["正常模式 (70%)", "临时套利模式 (85%)"],
                index=0
            )

            # 仓位限制
            max_position = st.slider("最大仓位比例", 0.5, 0.9, 0.7, 0.05)
            max_single_stock = st.slider("单股最大仓位", 0.05, 0.2, 0.15, 0.01)

            st.markdown("---")

            # 策略设置
            st.header("🎯 策略设置")

            strategy_method = st.selectbox(
                "策略组合方法",
                ["动态权重", "风险平价", "等权重", "最大夏普比率"],
                index=0
            )

            auto_rebalance = st.checkbox("自动再平衡", value=True)
            rebalance_threshold = st.slider("再平衡阈值", 0.05, 0.2, 0.1, 0.01)

            st.markdown("---")

            # 数据刷新
            st.header("🔄 数据刷新")
            auto_refresh = st.checkbox("自动刷新", value=True)
            refresh_interval = st.slider("刷新间隔(秒)", 1, 30, 5, 1)

            if st.button("🔄 立即刷新"):
                st.rerun()

    def _render_main_content(self):
        """渲染主要内容"""
        # 创建标签页
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "📈 实时监控", "🎯 策略分析", "⚠️ 风险管理",
            "📊 性能分析", "🚨 临时套利"
        ])

        with tab1:
            self._render_realtime_monitoring()

        with tab2:
            self._render_strategy_analysis()

        with tab3:
            self._render_risk_management()

        with tab4:
            self._render_performance_analysis()

        with tab5:
            self._render_emergency_arbitrage()

    def _render_realtime_monitoring(self):
        """渲染实时监控"""
        st.subheader("📈 实时市场监控")

        # 关键指标卡片
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                label="总资产",
                value="¥200,000",
                delta="¥2,500 (+1.25%)"
            )

        with col2:
            st.metric(
                label="可用现金",
                value="¥60,000",
                delta="¥-5,000"
            )

        with col3:
            st.metric(
                label="持仓数量",
                value="8",
                delta="2"
            )

        with col4:
            st.metric(
                label="今日收益",
                value="¥1,200",
                delta="0.6%"
            )

        # 实时图表
        col1, col2 = st.columns(2)

        with col1:
            self._render_portfolio_chart()

        with col2:
            self._render_pnl_chart()

        # 实时信号表
        st.subheader("🎯 实时交易信号")
        self._render_realtime_signals()

    def _render_portfolio_chart(self):
        """渲染投资组合图表"""
        st.subheader("投资组合分布")

        # 模拟数据
        portfolio_data = {
            '股票代码': ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '现金'],
            '市值': [25000, 30000, 20000, 15000, 60000],
            '权重': [16.7, 20.0, 13.3, 10.0, 40.0]
        }

        df = pd.DataFrame(portfolio_data)

        fig = px.pie(
            df,
            values='市值',
            names='股票代码',
            title="投资组合分布",
            color_discrete_sequence=px.colors.qualitative.Set3
        )

        fig.update_traces(textposition='inside', textinfo='percent+label')
        fig.update_layout(height=400)

        st.plotly_chart(fig, use_container_width=True)

    def _render_pnl_chart(self):
        """渲染盈亏图表"""
        st.subheader("盈亏曲线")

        # 生成模拟盈亏数据
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
        np.random.seed(42)
        daily_returns = np.random.normal(0.001, 0.02, len(dates))
        cumulative_pnl = np.cumsum(daily_returns) * 200000  # 基于20万本金

        fig = go.Figure()

        # 累计盈亏线
        fig.add_trace(go.Scatter(
            x=dates,
            y=cumulative_pnl,
            mode='lines',
            name='累计盈亏',
            line=dict(color='#2E86AB', width=2),
            fill='tonexty'
        ))

        # 添加零线
        fig.add_hline(y=0, line_dash="dash", line_color="gray")

        fig.update_layout(
            title="累计盈亏曲线",
            xaxis_title="日期",
            yaxis_title="盈亏 (¥)",
            height=400,
            showlegend=False
        )

        st.plotly_chart(fig, use_container_width=True)

    def _render_realtime_signals(self):
        """渲染实时信号表"""
        # 模拟实时信号数据
        signals_data = {
            '时间': ['09:35:12', '09:42:33', '10:15:45', '10:28:17'],
            '策略': ['配对交易', 'ML增强', '多因子', '临时套利'],
            '交易对': ['000001/000002', '600000/600036', '000858/002415', '300750'],
            '信号': ['LONG_SHORT', 'SHORT_LONG', 'LONG_SHORT', 'BUY'],
            '置信度': [0.85, 0.92, 0.78, 0.95],
            '预期收益': ['2.1%', '1.8%', '2.5%', '3.2%'],
            '风险等级': ['中', '低', '中', '高'],
            '状态': ['待执行', '已执行', '待执行', '已执行']
        }

        df = pd.DataFrame(signals_data)

        # 样式化表格
        def highlight_status(val):
            if val == '已执行':
                return 'background-color: #d4edda; color: #155724'
            elif val == '待执行':
                return 'background-color: #fff3cd; color: #856404'
            return ''

        styled_df = df.style.applymap(highlight_status, subset=['状态'])
        st.dataframe(styled_df, use_container_width=True, height=200)

    def _render_strategy_analysis(self):
        """渲染策略分析"""
        st.subheader("🎯 策略分析与优化")

        # 策略性能对比
        col1, col2 = st.columns(2)

        with col1:
            self._render_strategy_performance()

        with col2:
            self._render_strategy_weights()

        # 策略详细分析
        st.subheader("策略详细分析")

        strategy_tabs = st.tabs(["配对交易", "统计套利", "ML增强", "多因子", "临时套利"])

        with strategy_tabs[0]:
            self._render_pairs_trading_analysis()

        with strategy_tabs[1]:
            self._render_statistical_arbitrage_analysis()

        with strategy_tabs[2]:
            self._render_ml_analysis()

        with strategy_tabs[3]:
            self._render_multifactor_analysis()

        with strategy_tabs[4]:
            self._render_emergency_analysis()

    def _render_strategy_performance(self):
        """渲染策略性能"""
        st.subheader("策略性能对比")

        # 模拟策略性能数据
        strategies = ['配对交易', 'ML增强', '多因子', '统计套利', '临时套利']
        returns = [0.025, 0.032, 0.028, 0.021, 0.045]
        sharpe_ratios = [1.2, 1.5, 1.3, 1.1, 1.8]
        win_rates = [0.65, 0.72, 0.68, 0.62, 0.75]

        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('收益率', '夏普比率', '胜率', '综合评分'),
            specs=[[{"type": "bar"}, {"type": "bar"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )

        # 收益率
        fig.add_trace(
            go.Bar(x=strategies, y=returns, name='收益率', marker_color='lightblue'),
            row=1, col=1
        )

        # 夏普比率
        fig.add_trace(
            go.Bar(x=strategies, y=sharpe_ratios, name='夏普比率', marker_color='lightgreen'),
            row=1, col=2
        )

        # 胜率
        fig.add_trace(
            go.Bar(x=strategies, y=win_rates, name='胜率', marker_color='lightcoral'),
            row=2, col=1
        )

        # 综合评分散点图
        scores = [r * s * w * 100 for r, s, w in zip(returns, sharpe_ratios, win_rates)]
        fig.add_trace(
            go.Scatter(x=strategies, y=scores, mode='markers+lines',
                      name='综合评分', marker=dict(size=10, color='purple')),
            row=2, col=2
        )

        fig.update_layout(height=600, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)

    def _render_strategy_weights(self):
        """渲染策略权重"""
        st.subheader("策略权重分配")

        # 模拟权重数据
        strategies = ['配对交易', 'ML增强', '多因子', '统计套利', '临时套利']
        current_weights = [0.25, 0.30, 0.20, 0.15, 0.10]
        target_weights = [0.22, 0.32, 0.25, 0.13, 0.08]

        fig = go.Figure()

        fig.add_trace(go.Bar(
            name='当前权重',
            x=strategies,
            y=current_weights,
            marker_color='lightblue'
        ))

        fig.add_trace(go.Bar(
            name='目标权重',
            x=strategies,
            y=target_weights,
            marker_color='orange'
        ))

        fig.update_layout(
            title="策略权重对比",
            xaxis_title="策略",
            yaxis_title="权重",
            barmode='group',
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)

    def _render_pairs_trading_analysis(self):
        """渲染配对交易分析"""
        st.write("### 配对交易策略详情")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("活跃交易对", "12", "2")

        with col2:
            st.metric("平均持仓时间", "3.2天", "-0.5天")

        with col3:
            st.metric("协整系数", "0.85", "0.02")

        # 配对交易热力图
        st.write("#### 股票相关性热力图")

        # 生成模拟相关性矩阵
        symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
        np.random.seed(42)
        corr_matrix = np.random.uniform(0.3, 0.9, (5, 5))
        np.fill_diagonal(corr_matrix, 1.0)

        fig = go.Figure(data=go.Heatmap(
            z=corr_matrix,
            x=symbols,
            y=symbols,
            colorscale='RdYlBu',
            zmid=0.5,
            text=np.round(corr_matrix, 2),
            texttemplate="%{text}",
            textfont={"size": 10},
            colorbar=dict(title="相关系数")
        ))

        fig.update_layout(
            title="股票相关性矩阵",
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)
