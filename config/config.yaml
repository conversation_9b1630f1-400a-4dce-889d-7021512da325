# 量化套利系统配置文件

# 数据源配置
data_sources:
  tushare:
    token: "your_tushare_token_here"
    enabled: true
    rate_limit: 200  # 每分钟请求限制
  
  akshare:
    enabled: true
    cache_timeout: 300  # 缓存超时时间(秒)
  
  yfinance:
    enabled: false
    timeout: 30

# 交易配置
trading:
  initial_capital: 1000000  # 初始资金
  max_position_ratio: 0.1   # 最大单一持仓比例
  max_daily_loss: 0.05      # 最大日损失比例
  commission_rate: 0.0003   # 佣金费率
  slippage: 0.001          # 滑点
  
  # 交易时间
  trading_hours:
    morning_start: "09:30"
    morning_end: "11:30"
    afternoon_start: "13:00"
    afternoon_end: "15:00"

# 策略配置
strategies:
  pair_trading:
    enabled: true
    lookback_period: 60
    entry_threshold: 2.0
    exit_threshold: 0.5
    max_holding_days: 10
  
  statistical_arbitrage:
    enabled: true
    industry_threshold: 0.05
    individual_threshold: 0.03
    min_correlation: 0.7
  
  volatility_arbitrage:
    enabled: false
    vol_window: 20
    vol_threshold: 0.3

# 风险管理配置
risk_management:
  max_portfolio_var: 0.02    # 最大投资组合VaR
  var_confidence: 0.95       # VaR置信水平
  max_drawdown: 0.15         # 最大回撤限制
  position_limits:
    single_stock: 0.1        # 单只股票最大仓位
    sector: 0.3              # 单个行业最大仓位
    
  # 止损设置
  stop_loss:
    enabled: true
    fixed_stop: 0.05         # 固定止损比例
    trailing_stop: 0.03      # 跟踪止损比例

# 模型配置
models:
  black_scholes:
    default_risk_free_rate: 0.03
    default_volatility: 0.2
    calibration_window: 252
  
  heston:
    initial_variance: 0.04
    mean_reversion_speed: 2.0
    long_term_variance: 0.04
    vol_of_vol: 0.3
    correlation: -0.7
  
  enhanced_features:
    use_historical_factors: true
    lookback_days: 252
    trend_sensitivity: 0.1
    momentum_sensitivity: 0.05
    sentiment_sensitivity: 0.02

# 数据库配置
database:
  type: "sqlite"  # sqlite, mysql, postgresql
  path: "data/quantitative_arbitrage.db"
  
  # MySQL配置 (如果使用)
  mysql:
    host: "localhost"
    port: 3306
    database: "qa_system"
    username: "qa_user"
    password: "your_password"

# 缓存配置
cache:
  redis:
    enabled: false
    host: "localhost"
    port: 6379
    db: 0
    password: null
  
  local:
    enabled: true
    max_size: 1000
    ttl: 3600  # 生存时间(秒)

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/qa_system.log"
  max_size: "10MB"
  backup_count: 5
  
  # 特定模块日志级别
  modules:
    data_provider: "DEBUG"
    risk_manager: "INFO"
    arbitrage_engine: "INFO"

# Web界面配置
web:
  host: "0.0.0.0"
  port: 8501
  debug: false
  auto_refresh: 30  # 自动刷新间隔(秒)

# 通知配置
notifications:
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_app_password"
    recipients: ["<EMAIL>"]
  
  webhook:
    enabled: false
    url: "https://hooks.slack.com/your/webhook/url"

# 性能监控
monitoring:
  enabled: true
  metrics_interval: 60  # 指标收集间隔(秒)
  alert_thresholds:
    cpu_usage: 80
    memory_usage: 85
    error_rate: 0.05

# 回测配置
backtesting:
  start_date: "2020-01-01"
  end_date: "2023-12-31"
  benchmark: "000300.SH"  # 沪深300指数
  
  # 回测参数
  parameters:
    rebalance_frequency: "monthly"
    transaction_cost: 0.001
    market_impact: 0.0005
