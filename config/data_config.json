{"market_data": {"directory": "data/market", "file_pattern": "market_*.csv", "encoding": "utf-8", "refresh_interval": 2, "columns": {"symbol": "股票代码", "current_price": "当前价格", "change_pct": "涨跌幅", "volume": "成交量", "turnover": "成交额", "high": "最高价", "low": "最低价", "open": "开盘价", "prev_close": "昨收价"}}, "position_data": {"directory": "data/positions", "file_pattern": "positions_*.csv", "encoding": "utf-8", "refresh_interval": 5, "columns": {"symbol": "股票代码", "quantity": "持仓数量", "cost_price": "成本价", "market_price": "市价", "market_value": "市值", "pnl": "盈亏", "pnl_pct": "盈亏比例"}}, "capital_data": {"directory": "data/capital", "file_pattern": "capital_*.csv", "encoding": "utf-8", "refresh_interval": 10, "columns": {"total_capital": "总资金", "available_capital": "可用资金", "frozen_capital": "冻结资金", "position_value": "持仓市值", "cash_balance": "现金余额", "update_time": "更新时间"}}, "data_validation": {"market_data": {"required_columns": ["symbol", "current_price"], "price_range": [0.01, 1000], "volume_range": [0, 1000000000]}, "position_data": {"required_columns": ["symbol", "quantity"], "quantity_range": [-1000000, 1000000]}, "capital_data": {"required_columns": ["total_capital", "available_capital"], "capital_range": [0, 100000000]}}, "file_monitoring": {"check_interval": 1, "max_file_age": 300, "backup_count": 10}}