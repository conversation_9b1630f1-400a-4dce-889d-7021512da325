{"cache_ttl": 60, "quality_threshold": 0.7, "stream": {"enabled": true, "update_interval": 2, "auto_start": false, "symbols": ["000001.SZ", "000002.SZ", "000858.SZ", "002415.SZ", "600000.SH", "600036.SH", "600519.SH", "300750.SZ"]}, "sources": {"mock": {"enabled": true, "priority": 3, "auto_generation": true, "generation_interval": 2.0}, "csv": {"enabled": false, "files": [{"name": "历史数据1", "path": "data/sample_stock_data.csv", "symbols": ["000001.SZ", "000002.SZ"], "simulate": true, "speed": 1.0}, {"name": "历史数据2", "path": "data/market_data_2024.csv", "symbols": ["600000.SH", "600036.SH"], "simulate": false, "speed": 2.0}]}, "api": {"enabled": false, "tushare": {"enabled": false, "token": "", "rate_limit": 0.2, "retry_count": 3, "timeout": 30}, "yahoo": {"enabled": false, "timeout": 30, "retry_count": 3, "symbols_mapping": {"000001.SZ": "000001.SZ", "600000.SH": "600000.SS"}}, "akshare": {"enabled": false, "timeout": 30, "retry_count": 3}, "alpha_vantage": {"enabled": false, "api_key": "", "timeout": 30, "retry_count": 3}}, "websocket": {"enabled": false, "url": "wss://api.example.com/ws", "auth": {"type": "token", "token": ""}, "heartbeat_interval": 30, "reconnect_attempts": 5}, "database": {"enabled": false, "type": "postgresql", "connection": {"host": "localhost", "port": 5432, "database": "market_data", "username": "user", "password": "password"}, "tables": {"realtime": "realtime_quotes", "historical": "historical_data"}}}, "data_validation": {"enabled": true, "rules": {"price_range_check": true, "volume_check": true, "timestamp_check": true, "completeness_check": true}, "thresholds": {"min_price": 0.01, "max_price": 10000, "min_volume": 0, "max_delay_seconds": 300}}, "fallback": {"enabled": true, "strategy": "cascade", "sources_order": ["api", "csv", "mock"], "timeout_seconds": 10}, "monitoring": {"enabled": true, "metrics": {"response_time": true, "error_rate": true, "data_quality": true, "cache_hit_rate": true}, "alerts": {"high_error_rate": 0.1, "slow_response": 5.0, "low_quality": 0.5}}, "logging": {"level": "INFO", "file": "logs/data_sources.log", "max_size": "10MB", "backup_count": 5}}