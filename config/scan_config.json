{"scan_strategies": ["pair_trading", "statistical_arbitrage", "momentum_arbitrage", "mean_reversion", "volatility_arbitrage", "cross_market", "sector_rotation", "event_driven"], "filters": {"min_market_cap": 1000000000, "min_volume": 1000000, "max_price": 1000, "min_price": 1, "exclude_st": true, "exclude_suspended": true, "exclude_new_stocks": true, "min_trading_days": 60}, "thresholds": {"min_confidence": 0.6, "min_expected_return": 0.01, "max_risk_level": "HIGH", "max_position_size": 0.1}, "technical_indicators": {"rsi_period": 14, "ma_periods": [5, 10, 20, 60], "bollinger_period": 20, "bollinger_std": 2, "macd_periods": [12, 26, 9], "atr_period": 14, "volume_ma_period": 20}, "threading": {"max_threads": 80, "batch_size_per_thread": 50, "timeout_seconds": 300, "retry_attempts": 3}, "performance": {"cache_timeout": 60, "max_cache_size": 10000, "memory_limit_mb": 2048, "cpu_usage_limit": 0.8}, "markets": {"shanghai": {"enabled": true, "codes": ["60", "68"], "weight": 1.0}, "shenzhen": {"enabled": true, "codes": ["00", "30"], "weight": 1.0}}, "strategy_weights": {"pair_trading": 1.0, "statistical_arbitrage": 1.2, "momentum_arbitrage": 0.8, "mean_reversion": 1.1, "volatility_arbitrage": 0.9, "cross_market": 0.7, "sector_rotation": 0.8, "event_driven": 1.3}, "risk_management": {"max_single_position": 0.05, "max_strategy_exposure": 0.3, "max_sector_exposure": 0.2, "correlation_limit": 0.7, "var_limit": 0.02}, "output": {"max_results": 100, "sort_by": ["confidence", "expected_return"], "export_formats": ["json", "csv"], "save_to_database": true}}