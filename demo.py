#!/usr/bin/env python3
"""
量化套利系统演示脚本
展示随机微积分模型和实时交易信号生成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pandas as pd
from datetime import datetime
import time
import threading
from src.models.stochastic_models import BlackScholesModel, BSParameters
from src.arbitrage_engine import ArbitrageEngine, ArbitrageSignal

def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🚀 量化套利系统 - 沪深股市实时交易")
    print("=" * 60)
    print("📊 集成随机微积分方程")
    print("⚡ 实时行业数据输入")
    print("💰 智能交易信号输出")
    print("🛡️  符合沪深交易规则")
    print("=" * 60)

def demonstrate_stochastic_models():
    """演示随机微积分模型"""
    print("\n📈 随机微积分模型演示")
    print("-" * 40)
    
    # Black-Scholes模型
    bs_params = BSParameters(S0=100, mu=0.05, sigma=0.2, r=0.03, T=1.0)
    bs_model = BlackScholesModel(bs_params)
    
    print(f"Black-Scholes模型参数:")
    print(f"  初始股价: {bs_params.S0}")
    print(f"  漂移率: {bs_params.mu:.2%}")
    print(f"  波动率: {bs_params.sigma:.2%}")
    print(f"  无风险利率: {bs_params.r:.2%}")
    
    # 模拟股价路径
    paths = bs_model.simulate(n_paths=5, n_steps=10)
    print(f"\n模拟5条股价路径 (10个时间步):")
    for i, path in enumerate(paths):
        print(f"  路径{i+1}: {path[0]:.2f} -> {path[-1]:.2f} (收益率: {(path[-1]/path[0]-1)*100:.1f}%)")
    
    # 期权定价
    call_price = bs_model.option_price(105, "call")
    put_price = bs_model.option_price(105, "put")
    print(f"\n期权定价 (执行价格=105):")
    print(f"  看涨期权价格: {call_price:.4f}")
    print(f"  看跌期权价格: {put_price:.4f}")
    
    # 希腊字母
    greeks = bs_model.greeks(105, "call")
    print(f"\n希腊字母:")
    print(f"  Delta: {greeks['delta']:.4f}")
    print(f"  Gamma: {greeks['gamma']:.4f}")
    print(f"  Vega: {greeks['vega']:.4f}")

def signal_handler(signal: ArbitrageSignal):
    """处理交易信号"""
    print(f"\n🎯 新交易信号 [{signal.timestamp.strftime('%H:%M:%S')}]")
    print(f"   策略: {signal.strategy_name}")
    print(f"   交易对: {signal.symbol1} ↔ {signal.symbol2}")
    print(f"   信号类型: {signal.signal_type}")
    print(f"   置信度: {signal.confidence:.2f}")
    print(f"   预期收益: {signal.expected_return:.2%}")
    print(f"   风险等级: {signal.risk_level}")
    print(f"   入场价格: {signal.entry_price1:.2f} / {signal.entry_price2:.2f}")

def demonstrate_arbitrage_engine():
    """演示套利引擎"""
    print("\n🤖 套利引擎演示")
    print("-" * 40)
    
    # 创建套利引擎
    engine = ArbitrageEngine(initial_capital=1000000)
    engine.add_signal_callback(signal_handler)
    
    print("启动套利引擎...")
    engine.start()
    
    print("系统运行中，等待交易信号...")
    print("(按 Ctrl+C 停止)")
    
    try:
        # 运行30秒
        for i in range(30):
            time.sleep(1)
            if i % 5 == 0:
                active_signals = engine.get_active_signals()
                print(f"\r活跃信号数量: {len(active_signals)}", end="", flush=True)
    
    except KeyboardInterrupt:
        print("\n\n用户中断...")
    
    finally:
        engine.stop()
        print("套利引擎已停止")

def show_market_rules():
    """展示沪深股市交易规则"""
    print("\n📋 沪深股市交易规则")
    print("-" * 40)
    
    rules_info = [
        "🕘 交易时间: 9:30-11:30, 13:00-15:00",
        "📈 涨跌停限制: 主板±10%, 创业板/科创板±20%",
        "💱 最小变动单位: 0.01元",
        "📦 交易单位: 100股/手",
        "🔄 交易制度: T+1",
        "💰 交易费用: 佣金+印花税+过户费",
        "🚫 不允许裸卖空",
        "⏰ 集合竞价: 9:15-9:25, 14:57-15:00"
    ]
    
    for rule in rules_info:
        print(f"  {rule}")

def show_industry_simulation():
    """展示行业数据模拟"""
    print("\n🏭 行业数据输入模拟")
    print("-" * 40)
    
    industries = [
        "银行", "证券", "保险", "地产", "科技", 
        "医药", "消费", "制造", "能源", "通信"
    ]
    
    print("实时行业表现:")
    for industry in industries:
        change = np.random.normal(0, 2)
        volume = np.random.randint(1000, 10000)
        color = "🟢" if change > 0 else "🔴" if change < 0 else "⚪"
        print(f"  {color} {industry:6s}: {change:+6.2f}% (成交量: {volume:,}万)")

def main():
    """主函数"""
    print_banner()
    
    while True:
        print("\n请选择演示模块:")
        print("1. 随机微积分模型")
        print("2. 套利引擎实时运行")
        print("3. 沪深股市交易规则")
        print("4. 行业数据输入模拟")
        print("5. 完整系统演示")
        print("0. 退出")
        
        try:
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("感谢使用量化套利系统！")
                break
            elif choice == "1":
                demonstrate_stochastic_models()
            elif choice == "2":
                demonstrate_arbitrage_engine()
            elif choice == "3":
                show_market_rules()
            elif choice == "4":
                show_industry_simulation()
            elif choice == "5":
                demonstrate_stochastic_models()
                show_market_rules()
                show_industry_simulation()
                demonstrate_arbitrage_engine()
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n程序被中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
