"""
增强版量化套利系统演示
展示完善后的综合套利模块功能
"""

import sys
import os
import numpy as np
import time
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def demo_header():
    """演示标题"""
    print("🚀" + "="*60 + "🚀")
    print("    增强版量化套利系统 - 综合功能演示")
    print("🚀" + "="*60 + "🚀")
    print()

def demo_strategy_combiner():
    """演示策略组合器"""
    print("🎯 策略组合器演示")
    print("-" * 40)
    
    try:
        from strategies.strategy_combiner import StrategyCombiner, CombinationMethod
        from strategies.strategy_optimizer import StrategyPerformance
        
        # 创建策略组合器
        combiner = StrategyCombiner(CombinationMethod.DYNAMIC_WEIGHT)
        print("✅ 策略组合器初始化成功")
        
        # 模拟添加策略历史性能
        strategies = ['配对交易', 'ML增强', '多因子套利', '统计套利', '波动率套利']
        
        print("\n📊 添加策略历史性能数据...")
        for strategy_name in strategies:
            for i in range(20):  # 每个策略20个历史记录
                performance = StrategyPerformance(
                    strategy_name=strategy_name,
                    total_return=np.random.normal(0.02, 0.01),
                    sharpe_ratio=np.random.normal(1.2, 0.3),
                    max_drawdown=np.random.uniform(-0.15, -0.02),
                    win_rate=np.random.uniform(0.5, 0.8),
                    avg_return_per_trade=np.random.normal(0.001, 0.0005),
                    volatility=np.random.uniform(0.1, 0.3),
                    calmar_ratio=np.random.uniform(0.5, 2.0),
                    sortino_ratio=np.random.uniform(0.8, 2.5),
                    score=np.random.uniform(0.6, 0.9),
                    confidence=np.random.uniform(0.7, 0.95)
                )
                combiner.add_strategy_performance(strategy_name, performance)
        
        print(f"✅ 已添加 {len(strategies)} 个策略的历史数据")
        
        # 测试不同权重分配方法
        methods = [
            (CombinationMethod.EQUAL_WEIGHT, "等权重"),
            (CombinationMethod.RISK_PARITY, "风险平价"),
            (CombinationMethod.PERFORMANCE_BASED, "基于表现"),
            (CombinationMethod.DYNAMIC_WEIGHT, "动态权重")
        ]
        
        print("\n📈 不同权重分配方法对比:")
        for method, name in methods:
            combiner.combination_method = method
            weights = combiner.calculate_strategy_weights()
            print(f"  {name}:")
            for strategy, weight in weights.items():
                print(f"    {strategy}: {weight:.1%}")
        
        # 获取配置摘要
        summary = combiner.get_strategy_allocation_summary()
        print(f"\n📋 策略配置摘要:")
        print(f"  总策略数: {summary['total_strategies']}")
        print(f"  组合方法: {summary.get('combination_method', 'N/A')}")
        print(f"  分散化水平: {summary.get('diversification_level', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 策略组合器演示失败: {e}")
    
    print()

def demo_advanced_strategies():
    """演示高级套利策略"""
    print("🤖 高级套利策略演示")
    print("-" * 40)
    
    try:
        from strategies.advanced_arbitrage_strategies import MLEnhancedPairsStrategy, MultiFactorArbitrageStrategy
        
        # ML增强配对交易策略
        print("🧠 ML增强配对交易策略:")
        ml_strategy = MLEnhancedPairsStrategy(lookback_period=60)
        
        # 模拟市场数据
        market_data = {
            '000001.SZ': {'price': 10.50, 'volume': 3000000, 'change': 0.025},
            '000002.SZ': {'price': 12.30, 'volume': 2500000, 'change': -0.015},
            '600000.SH': {'price': 8.90, 'volume': 4000000, 'change': 0.018},
            '600036.SH': {'price': 15.60, 'volume': 1800000, 'change': -0.008},
            '000858.SZ': {'price': 22.40, 'volume': 1200000, 'change': 0.032}
        }
        
        print(f"  📊 市场数据: {len(market_data)} 只股票")
        
        # 扫描ML套利机会
        ml_opportunities = ml_strategy.scan_opportunities(market_data)
        print(f"  🎯 ML策略发现: {len(ml_opportunities)} 个套利机会")
        
        # 多因子套利策略
        print("\n📊 多因子套利策略:")
        factor_weights = {
            'value': 0.25,
            'momentum': 0.20,
            'quality': 0.20,
            'volatility': 0.15,
            'size': 0.10,
            'liquidity': 0.10
        }
        
        multifactor_strategy = MultiFactorArbitrageStrategy(factor_weights)
        print(f"  ⚖️ 因子权重: {factor_weights}")
        
        # 计算因子得分
        try:
            factor_scores = multifactor_strategy._calculate_factor_scores(market_data)
            if factor_scores:
                print("  📈 因子得分排名:")
                sorted_scores = sorted(factor_scores.items(), key=lambda x: x[1], reverse=True)
                for symbol, score in sorted_scores[:3]:
                    print(f"    {symbol}: {score:.2f}")
            else:
                print("  ⚠️ 因子得分计算需要更多历史数据")
        except Exception as e:
            print(f"  ⚠️ 因子计算警告: {e}")
        
        # 扫描多因子套利机会
        factor_opportunities = multifactor_strategy.scan_opportunities(market_data)
        print(f"  🎯 多因子策略发现: {len(factor_opportunities)} 个套利机会")
        
        # 显示最佳机会
        all_opportunities = ml_opportunities + factor_opportunities
        if all_opportunities:
            best_opportunity = max(all_opportunities, key=lambda x: x.confidence)
            print(f"\n🏆 最佳套利机会:")
            print(f"  策略类型: {best_opportunity.strategy_type}")
            print(f"  交易对: {' / '.join(best_opportunity.symbols)}")
            print(f"  置信度: {best_opportunity.confidence:.1%}")
            print(f"  预期收益: {best_opportunity.expected_return:.2%}")
            print(f"  预期风险: {best_opportunity.expected_risk:.2%}")
        
    except Exception as e:
        print(f"❌ 高级策略演示失败: {e}")
    
    print()

def demo_risk_management():
    """演示风险管理"""
    print("⚠️ 动态风险管理演示")
    print("-" * 40)
    
    try:
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits, TradingMode
        
        # 创建风险管理器
        limits = DynamicPositionLimits(initial_capital=200000)
        risk_manager = DynamicRiskManager(limits)
        
        print(f"💰 初始资金: ¥{risk_manager.limits.initial_capital:,.0f}")
        print(f"📊 正常模式仓位限制: {risk_manager.limits.normal_position_ratio:.0%}")
        print(f"🚨 临时套利仓位限制: {risk_manager.limits.emergency_position_ratio:.0%}")
        
        # 模拟不同市场情况
        market_scenarios = [
            ("正常市场", {
                '000001.SZ': {'price': 10.5, 'change': 0.01, 'volume': 2000000},
                '000002.SZ': {'price': 12.3, 'change': -0.005, 'volume': 1800000}
            }),
            ("爆跌市场", {
                '000001.SZ': {'price': 10.5, 'change': -0.07, 'volume': 5000000},
                '000002.SZ': {'price': 12.3, 'change': -0.06, 'volume': 4500000}
            }),
            ("爆涨市场", {
                '000001.SZ': {'price': 10.5, 'change': 0.08, 'volume': 4000000},
                '000002.SZ': {'price': 12.3, 'change': 0.06, 'volume': 3500000}
            })
        ]
        
        print("\n📈 市场情况分析:")
        for scenario_name, market_data in market_scenarios:
            print(f"\n  {scenario_name}:")
            
            # 检测市场状况
            market_condition = risk_manager.detect_market_condition(market_data)
            print(f"    市场状况: {market_condition.value}")
            
            # 检查是否触发临时套利
            should_trigger, emergency_signal = risk_manager.should_trigger_emergency_arbitrage(market_data)
            print(f"    触发临时套利: {'是' if should_trigger else '否'}")
            
            if emergency_signal:
                print(f"    建议操作: {emergency_signal.suggested_action}")
                print(f"    置信度: {emergency_signal.confidence:.1%}")
            
            # 当前仓位限制
            current_limit = risk_manager.get_current_position_limit()
            print(f"    当前仓位限制: {current_limit:.0%}")
        
        # 显示风险管理摘要
        print(f"\n📋 风险管理摘要:")
        print(f"  当前模式: {risk_manager.current_mode.value}")
        print(f"  总资金池: ¥{risk_manager.total_capital:,.0f}")
        print(f"  可用现金: ¥{risk_manager.available_cash:,.0f}")
        print(f"  总收益率: {risk_manager.total_return:.2%}")
        
    except Exception as e:
        print(f"❌ 风险管理演示失败: {e}")
    
    print()

def demo_database_operations():
    """演示数据库操作"""
    print("📊 数据库操作演示")
    print("-" * 40)
    
    try:
        from database.signal_database import SignalDatabase, TradingSignal
        
        # 创建数据库
        db = SignalDatabase("demo_enhanced.db")
        print("✅ 信号数据库初始化成功")
        
        # 创建并保存多个测试信号
        strategies = ['配对交易', 'ML增强', '多因子', '统计套利']
        signal_types = ['LONG_SHORT', 'SHORT_LONG', 'LONG', 'SHORT']
        
        print("\n💾 保存测试信号...")
        saved_signals = []
        
        for i in range(5):
            signal = TradingSignal(
                timestamp=datetime.now(),
                strategy_name=np.random.choice(strategies),
                symbol1=f"00000{np.random.randint(1,9)}.SZ",
                symbol2=f"60000{np.random.randint(1,9)}.SH",
                signal_type=np.random.choice(signal_types),
                confidence=np.random.uniform(0.6, 0.95),
                expected_return=np.random.uniform(0.01, 0.04),
                risk_level=np.random.choice(['LOW', 'MEDIUM', 'HIGH']),
                entry_price1=np.random.uniform(8, 50),
                entry_price2=np.random.uniform(8, 50),
                performance_score=np.random.uniform(0.5, 0.9),
                is_historical=False
            )
            
            signal_id = db.save_signal(signal)
            saved_signals.append(signal_id)
            print(f"  ✅ 保存信号 ID: {signal_id}")
        
        # 获取实时信号
        realtime_signals = db.get_realtime_signals(hours=1)
        print(f"\n📈 获取实时信号: {len(realtime_signals)} 个")
        
        # 获取策略统计
        stats = db.get_strategy_statistics()
        print(f"\n📊 策略统计:")
        for strategy, stat in stats.items():
            print(f"  {strategy}: {stat['count']}个信号, 平均置信度: {stat['avg_confidence']:.2f}")
        
        # 获取数据库信息
        db_info = db.get_database_info()
        print(f"\n🗄️ 数据库信息:")
        print(f"  数据库大小: {db_info['database_size_mb']:.3f} MB")
        print(f"  总记录数: {db_info['total_records']}")
        print(f"  表数量: {len(db_info['tables'])}")
        
    except Exception as e:
        print(f"❌ 数据库演示失败: {e}")
    
    print()

def demo_system_integration():
    """演示系统集成"""
    print("🔗 系统集成演示")
    print("-" * 40)
    
    try:
        from strategies.strategy_combiner import StrategyCombiner, CombinationMethod
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
        from strategies.advanced_arbitrage_strategies import MLEnhancedPairsStrategy
        
        print("🚀 初始化集成系统...")
        
        # 创建核心组件
        limits = DynamicPositionLimits(initial_capital=200000)
        risk_manager = DynamicRiskManager(limits)
        combiner = StrategyCombiner(CombinationMethod.DYNAMIC_WEIGHT)
        ml_strategy = MLEnhancedPairsStrategy()
        
        print("✅ 所有组件初始化完成")
        
        # 模拟实时交易流程
        print("\n🔄 模拟实时交易流程:")
        
        market_data = {
            '000001.SZ': {'price': 10.5, 'change': 0.02, 'volume': 3000000},
            '000002.SZ': {'price': 12.3, 'change': -0.01, 'volume': 2500000},
            '600000.SH': {'price': 8.9, 'change': 0.015, 'volume': 4000000}
        }
        
        # 1. 市场状况检测
        market_condition = risk_manager.detect_market_condition(market_data)
        print(f"  1️⃣ 市场状况: {market_condition.value}")
        
        # 2. 风险检查
        current_limit = risk_manager.get_current_position_limit()
        print(f"  2️⃣ 当前仓位限制: {current_limit:.0%}")
        
        # 3. 策略权重计算
        combiner.strategy_names = ['配对交易', 'ML增强', '统计套利']
        weights = combiner.calculate_strategy_weights()
        print(f"  3️⃣ 策略权重分配:")
        for strategy, weight in weights.items():
            print(f"      {strategy}: {weight:.1%}")
        
        # 4. 套利机会扫描
        opportunities = ml_strategy.scan_opportunities(market_data)
        print(f"  4️⃣ 发现套利机会: {len(opportunities)} 个")
        
        # 5. 系统状态摘要
        system_status = {
            'total_capital': risk_manager.total_capital,
            'available_cash': risk_manager.available_cash,
            'trading_mode': risk_manager.current_mode.value,
            'active_strategies': len(combiner.strategy_names),
            'market_condition': market_condition.value
        }
        
        print(f"  5️⃣ 系统状态摘要:")
        for key, value in system_status.items():
            print(f"      {key}: {value}")
        
        print("\n🎉 系统集成演示完成！所有模块协同工作正常。")
        
    except Exception as e:
        print(f"❌ 系统集成演示失败: {e}")
    
    print()

def main():
    """主演示函数"""
    demo_header()
    
    print("🎬 开始综合功能演示...\n")
    
    # 运行各个演示
    demo_strategy_combiner()
    time.sleep(1)
    
    demo_advanced_strategies()
    time.sleep(1)
    
    demo_risk_management()
    time.sleep(1)
    
    demo_database_operations()
    time.sleep(1)
    
    demo_system_integration()
    
    # 演示总结
    print("🎊 演示总结")
    print("-" * 40)
    print("✅ 策略组合器: 多种权重分配方法，智能信号组合")
    print("✅ 高级套利策略: ML增强配对交易，多因子套利")
    print("✅ 动态风险管理: 双模式仓位控制，智能市场检测")
    print("✅ 数据库系统: 完整信号存储，统计分析功能")
    print("✅ 系统集成: 所有模块协同工作，实时交易流程")
    print()
    print("🚀 增强版量化套利系统演示完成！")
    print("💡 系统现已具备专业级量化交易平台的核心功能。")
    print()

if __name__ == "__main__":
    main()
