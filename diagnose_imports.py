#!/usr/bin/env python3
"""
诊断模块导入问题
"""

import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

def test_import(module_name, import_statement):
    """测试单个模块导入"""
    print(f"Testing {module_name}...")
    try:
        exec(import_statement)
        print(f"  ✅ {module_name} imported successfully")
        return True
    except Exception as e:
        print(f"  ❌ {module_name} failed: {e}")
        return False

def main():
    print("🔍 诊断模块导入问题")
    print("=" * 50)
    
    # 检查文件存在性
    print("检查文件存在性:")
    files_to_check = [
        "src/__init__.py",
        "src/models/__init__.py", 
        "src/models/stochastic_models.py",
        "src/models/historical_stochastic_models.py",
        "src/models/enhanced_stochastic_models.py",
    ]
    
    for file_path in files_to_check:
        exists = os.path.exists(file_path)
        size = os.path.getsize(file_path) if exists else 0
        print(f"  {file_path}: {'✅' if exists else '❌'} ({size} bytes)")
    print()
    
    # 测试导入
    print("测试模块导入:")
    
    tests = [
        ("基础模块", "import src"),
        ("模型模块", "import src.models"),
        ("随机模型", "from src.models import stochastic_models"),
        ("BS模型类", "from src.models.stochastic_models import BlackScholesModel"),
    ]
    
    for name, import_stmt in tests:
        test_import(name, import_stmt)

if __name__ == "__main__":
    main()
