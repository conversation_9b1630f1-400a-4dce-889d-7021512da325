"""
实时集成套利系统界面
展示所有模块联动的实时行情、策略发现和机会展示
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import time
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from core.realtime_coordinator import RealTimeCoordinator, OpportunityAlert
    from database.signal_database import SignalDatabase
except ImportError as e:
    st.error(f"模块导入失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="实时集成套利系统",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化会话状态
if 'coordinator' not in st.session_state:
    st.session_state.coordinator = RealTimeCoordinator(initial_capital=200000)
    st.session_state.running = False
    st.session_state.ui_data = {}
    st.session_state.alerts = []
    st.session_state.signals_today = []

def ui_update_callback(data):
    """UI更新回调函数"""
    st.session_state.ui_data = data

def alert_callback(alert: OpportunityAlert):
    """警报回调函数"""
    st.session_state.alerts.append(alert)
    # 保持最近20个警报
    if len(st.session_state.alerts) > 20:
        st.session_state.alerts = st.session_state.alerts[-20:]

def signal_callback(signal):
    """信号回调函数"""
    st.session_state.signals_today.append(signal)
    # 保持最近50个信号
    if len(st.session_state.signals_today) > 50:
        st.session_state.signals_today = st.session_state.signals_today[-50:]

# 注册回调函数
st.session_state.coordinator.add_ui_callback(ui_update_callback)
st.session_state.coordinator.add_alert_callback(alert_callback)
st.session_state.coordinator.add_signal_callback(signal_callback)

def create_header():
    """创建页面标题"""
    st.title("⚡ 实时集成套利系统")
    st.markdown("**自动行情监控 | 智能策略发现 | 实时机会展示**")
    
    # 系统状态指示器
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        status = "🟢 运行中" if st.session_state.running else "🔴 已停止"
        st.markdown(f"**系统状态**: {status}")
    
    with col2:
        signals_count = len(st.session_state.signals_today)
        st.markdown(f"**今日信号**: {signals_count}")
    
    with col3:
        alerts_count = len(st.session_state.alerts)
        st.markdown(f"**活跃警报**: {alerts_count}")
    
    with col4:
        current_time = datetime.now().strftime("%H:%M:%S")
        st.markdown(f"**当前时间**: {current_time}")

def create_sidebar():
    """创建侧边栏控制"""
    with st.sidebar:
        st.header("🎛️ 系统控制")
        
        # 启动/停止按钮
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🚀 启动", type="primary", use_container_width=True):
                if not st.session_state.running:
                    st.session_state.coordinator.start()
                    st.session_state.running = True
                    st.success("系统已启动")
                    st.rerun()
        
        with col2:
            if st.button("⏹️ 停止", use_container_width=True):
                if st.session_state.running:
                    st.session_state.coordinator.stop()
                    st.session_state.running = False
                    st.info("系统已停止")
                    st.rerun()
        
        # 手动操作
        st.markdown("---")
        st.subheader("🔧 手动操作")
        
        if st.button("🔍 强制扫描", use_container_width=True):
            if st.session_state.running:
                opportunities = st.session_state.coordinator.force_strategy_scan()
                st.success(f"发现 {len(opportunities)} 个机会")
            else:
                st.warning("请先启动系统")
        
        if st.button("📊 刷新数据", use_container_width=True):
            st.rerun()
        
        # 系统设置
        st.markdown("---")
        st.subheader("⚙️ 系统设置")
        
        auto_refresh = st.checkbox("自动刷新", value=True)
        refresh_interval = st.slider("刷新间隔(秒)", 1, 10, 3)
        
        # 监控设置
        st.markdown("---")
        st.subheader("📡 监控设置")
        
        show_all_signals = st.checkbox("显示所有信号", value=False)
        min_confidence = st.slider("最小置信度", 0.0, 1.0, 0.6, 0.05)
        
        # 系统信息
        st.markdown("---")
        st.subheader("ℹ️ 系统信息")
        
        if st.session_state.running:
            status = st.session_state.coordinator.get_system_status()
            st.write(f"监控股票: {status['monitored_symbols']}")
            st.write(f"活跃机会: {status['active_opportunities']}")
            st.write(f"系统模式: {status['risk_manager_status']['mode']}")

def create_realtime_monitoring():
    """创建实时监控面板"""
    st.subheader("📈 实时市场监控")
    
    if not st.session_state.ui_data:
        st.info("等待系统启动和数据加载...")
        return
    
    ui_data = st.session_state.ui_data
    
    # 关键指标
    col1, col2, col3, col4 = st.columns(4)
    
    risk_status = ui_data.get('risk_status', {})
    
    with col1:
        total_capital = risk_status.get('total_capital', 200000)
        st.metric(
            label="总资产",
            value=f"¥{total_capital:,.0f}",
            delta=f"¥{ui_data.get('system_metrics', {}).get('current_pnl', 0):,.0f}"
        )
    
    with col2:
        available_cash = risk_status.get('available_cash', 200000)
        st.metric(
            label="可用现金",
            value=f"¥{available_cash:,.0f}",
            delta=f"{available_cash/total_capital:.1%}"
        )
    
    with col3:
        position_limit = risk_status.get('position_limit', 0.7)
        st.metric(
            label="仓位限制",
            value=f"{position_limit:.0%}",
            delta=risk_status.get('current_mode', 'normal')
        )
    
    with col4:
        signals_today = ui_data.get('system_metrics', {}).get('total_signals_today', 0)
        st.metric(
            label="今日信号",
            value=f"{signals_today}",
            delta=f"+{len(st.session_state.signals_today[-10:])}"
        )
    
    # 实时价格表
    st.subheader("💹 实时价格")
    market_data = ui_data.get('market_data', {})
    
    if market_data:
        price_data = []
        for symbol, data in market_data.items():
            price_data.append({
                '股票代码': symbol,
                '当前价格': f"¥{data.get('price', 0):.2f}",
                '涨跌幅': f"{data.get('change', 0):.2%}",
                '成交量': f"{data.get('volume', 0):,}",
                '更新时间': data.get('timestamp', datetime.now()).strftime('%H:%M:%S')
            })
        
        df = pd.DataFrame(price_data)
        
        # 添加颜色样式
        def highlight_change(val):
            if '涨跌幅' in val.name:
                if '+' in str(val):
                    return 'background-color: #d4edda; color: #155724'
                elif '-' in str(val):
                    return 'background-color: #f8d7da; color: #721c24'
            return ''
        
        styled_df = df.style.applymap(highlight_change)
        st.dataframe(styled_df, use_container_width=True, height=300)

def create_opportunity_alerts():
    """创建机会警报面板"""
    st.subheader("🚨 实时机会警报")
    
    if not st.session_state.alerts:
        st.info("暂无活跃机会警报")
        return
    
    # 最新警报
    latest_alerts = st.session_state.alerts[-10:]
    
    for alert in reversed(latest_alerts):
        # 确定警报样式
        if alert.is_emergency:
            alert_type = "🚨 紧急套利"
            color = "red"
        elif alert.confidence > 0.8:
            alert_type = "⭐ 高置信度"
            color = "green"
        else:
            alert_type = "💡 一般机会"
            color = "blue"
        
        with st.container():
            col1, col2, col3, col4 = st.columns([2, 2, 1, 1])
            
            with col1:
                st.markdown(f"**{alert_type}**: {alert.strategy_name}")
                st.caption(f"交易对: {' / '.join(alert.symbols)}")
            
            with col2:
                st.markdown(f"置信度: **{alert.confidence:.1%}**")
                st.caption(f"预期收益: {alert.expected_return:.2%}")
            
            with col3:
                st.markdown(f"风险: **{alert.risk_level}**")
            
            with col4:
                time_diff = (datetime.now() - alert.alert_time).seconds
                st.caption(f"{time_diff}秒前")
        
        st.markdown("---")

def create_strategy_performance():
    """创建策略性能面板"""
    st.subheader("📊 策略性能分析")
    
    if not st.session_state.signals_today:
        st.info("暂无今日信号数据")
        return
    
    # 策略统计
    strategy_stats = {}
    for signal in st.session_state.signals_today:
        strategy = signal.strategy_name
        if strategy not in strategy_stats:
            strategy_stats[strategy] = {
                'count': 0,
                'avg_confidence': 0,
                'avg_return': 0,
                'confidences': [],
                'returns': []
            }
        
        strategy_stats[strategy]['count'] += 1
        strategy_stats[strategy]['confidences'].append(signal.confidence)
        strategy_stats[strategy]['returns'].append(signal.expected_return)
    
    # 计算平均值
    for strategy, stats in strategy_stats.items():
        stats['avg_confidence'] = np.mean(stats['confidences'])
        stats['avg_return'] = np.mean(stats['returns'])
    
    # 显示策略表现
    if strategy_stats:
        col1, col2 = st.columns(2)
        
        with col1:
            # 策略信号数量
            strategies = list(strategy_stats.keys())
            counts = [stats['count'] for stats in strategy_stats.values()]
            
            fig = px.bar(
                x=strategies,
                y=counts,
                title="各策略信号数量",
                labels={'x': '策略', 'y': '信号数量'}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 策略置信度
            confidences = [stats['avg_confidence'] for stats in strategy_stats.values()]
            
            fig = px.bar(
                x=strategies,
                y=confidences,
                title="各策略平均置信度",
                labels={'x': '策略', 'y': '平均置信度'}
            )
            st.plotly_chart(fig, use_container_width=True)

def create_signal_timeline():
    """创建信号时间线"""
    st.subheader("⏰ 信号时间线")
    
    if not st.session_state.signals_today:
        st.info("暂无信号数据")
        return
    
    # 最近20个信号
    recent_signals = st.session_state.signals_today[-20:]
    
    signal_data = []
    for signal in recent_signals:
        signal_data.append({
            '时间': signal.timestamp.strftime('%H:%M:%S'),
            '策略': signal.strategy_name,
            '交易对': f"{signal.symbol1}/{signal.symbol2 or ''}",
            '类型': signal.signal_type,
            '置信度': f"{signal.confidence:.2f}",
            '预期收益': f"{signal.expected_return:.2%}",
            '风险等级': signal.risk_level
        })
    
    df = pd.DataFrame(signal_data)
    
    # 样式化表格
    def highlight_confidence(val):
        if '置信度' in val.name:
            conf = float(val)
            if conf > 0.8:
                return 'background-color: #d4edda'
            elif conf > 0.6:
                return 'background-color: #fff3cd'
            else:
                return 'background-color: #f8d7da'
        return ''
    
    styled_df = df.style.applymap(highlight_confidence)
    st.dataframe(styled_df, use_container_width=True, height=400)

def main():
    """主函数"""
    create_header()
    create_sidebar()
    
    # 主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs([
        "📈 实时监控", "🚨 机会警报", "📊 策略性能", "⏰ 信号时间线"
    ])
    
    with tab1:
        create_realtime_monitoring()
    
    with tab2:
        create_opportunity_alerts()
    
    with tab3:
        create_strategy_performance()
    
    with tab4:
        create_signal_timeline()
    
    # 自动刷新
    if st.session_state.running:
        time.sleep(2)
        st.rerun()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>⚡ 实时集成套利系统 v4.0 | 全自动策略发现 | 智能机会识别</p>
        <p>🔄 实时数据流 | 🤖 多策略联动 | 📊 可视化监控</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
