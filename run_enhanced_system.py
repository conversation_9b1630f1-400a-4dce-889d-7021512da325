#!/usr/bin/env python3
"""
增强版量化套利系统启动脚本
支持20万资金、70%仓位控制、策略优选、数据库管理
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_enhanced_banner():
    """打印增强版系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                🚀 增强版量化套利系统 v2.0                    ║
║                                                              ║
║  💰 20万初始资金专用版                                        ║
║  📊 70%总仓位控制 + 10只股票限制                              ║
║  🧠 智能策略比较和优选                                        ║
║  💾 SQLite数据库存储历史和实时信号                            ║
║  📈 策略比较过程可视化展示                                    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_enhanced_dependencies():
    """检查增强版依赖"""
    print("🔍 检查增强版系统依赖...")
    
    required_packages = [
        'streamlit', 'pandas', 'numpy', 'plotly', 
        'scipy', 'matplotlib', 'loguru', 'sqlite3'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    # 检查自定义模块
    custom_modules = [
        'src/risk/enhanced_risk_manager.py',
        'src/strategies/strategy_optimizer.py', 
        'src/database/signal_database.py'
    ]
    
    print("\n🔍 检查自定义模块...")
    for module_path in custom_modules:
        if os.path.exists(module_path):
            print(f"  ✅ {module_path}")
        else:
            print(f"  ❌ {module_path} (缺失)")
            missing_packages.append(module_path)
    
    if missing_packages:
        print(f"\n⚠️  缺少组件: {', '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖检查完成")
    return True

def show_enhanced_menu():
    """显示增强版菜单"""
    menu = """
🎛️  请选择运行模式:

1. 🚀 增强版可视化仪表板 (推荐)
   - 20万资金专用界面
   - 70%仓位控制监控
   - 智能策略比较
   - 实时信号数据库管理

2. 🧪 风险管理测试
   - 测试20万资金仓位控制
   - 验证10只股票限制
   - 风险指标计算

3. 🧠 策略比较演示
   - 多策略性能对比
   - 最优策略选择
   - 比较过程可视化

4. 💾 数据库管理
   - 查看信号数据库
   - 导出历史数据
   - 清理旧数据

5. 📊 系统性能测试
   - 完整功能测试
   - 性能基准测试
   - 模块集成验证

6. 📈 原版系统 (兼容)
   - 运行原版可视化界面
   - 基础功能演示

0. 🚪 退出系统

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    print(menu)

def run_enhanced_dashboard():
    """运行增强版仪表板"""
    print("🚀 启动增强版可视化仪表板...")
    print("💰 配置: 20万资金 | 70%仓位 | 10只股票")
    
    try:
        print("🌐 启动Web服务器...")
        print("📱 浏览器将自动打开 http://localhost:8501")
        print("⏹️  按 Ctrl+C 停止服务")
        
        subprocess.run(['streamlit', 'run', 'enhanced_visual_app.py'])
        
    except KeyboardInterrupt:
        print("\n⏹️  用户停止服务")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def test_risk_management():
    """测试风险管理"""
    print("🧪 启动风险管理测试...")
    
    try:
        test_code = '''
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from risk.enhanced_risk_manager import EnhancedRiskManager, PositionLimits

# 创建20万资金的风险管理器
limits = PositionLimits(
    initial_capital=200000,
    max_total_position_ratio=0.70,
    max_stock_count=10,
    max_single_position_ratio=0.15
)

risk_manager = EnhancedRiskManager(limits)

print("🛡️  风险管理器测试")
print("=" * 50)
print(f"初始资金: ¥{risk_manager.limits.initial_capital:,.0f}")
print(f"最大总仓位: {risk_manager.limits.max_total_position_ratio:.0%}")
print(f"最大股票数: {risk_manager.limits.max_stock_count}")
print(f"单股最大仓位: {risk_manager.limits.max_single_position_ratio:.0%}")

# 测试开仓检查
print("\\n📊 开仓测试:")
can_open, msg, info = risk_manager.can_open_position("000001.SZ", 10.0, 1000)
print(f"开仓检查: {can_open}")
print(f"消息: {msg}")
print(f"建议仓位: {info.get('suggested_quantity', 0)}股")
print(f"建议金额: ¥{info.get('suggested_value', 0):,.0f}")

# 获取投资组合摘要
print("\\n📈 投资组合摘要:")
summary = risk_manager.get_portfolio_summary()
for key, value in summary.items():
    if isinstance(value, float):
        if 'ratio' in key or 'pnl' in key:
            print(f"{key}: {value:.2%}")
        else:
            print(f"{key}: ¥{value:,.0f}")
    else:
        print(f"{key}: {value}")

print("\\n✅ 风险管理测试完成")
        '''
        
        exec(test_code)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def test_strategy_comparison():
    """测试策略比较"""
    print("🧠 启动策略比较测试...")
    
    try:
        test_code = '''
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from strategies.strategy_optimizer import StrategyOptimizer

# 创建策略优化器
optimizer = StrategyOptimizer()

# 模拟市场数据
market_data = {
    '000001.SZ': {'price': 10.5, 'change': 0.02},
    '000002.SZ': {'price': 12.3, 'change': -0.01},
    '600000.SH': {'price': 8.7, 'change': 0.03}
}

print("🧠 策略比较测试")
print("=" * 50)

# 评估所有策略
evaluation_results = optimizer.evaluate_strategies(market_data)

print("📊 策略性能评估:")
for strategy, performance, signal in evaluation_results:
    print(f"\\n策略: {strategy.name}")
    print(f"  总收益率: {performance.total_return:.2%}")
    print(f"  夏普比率: {performance.sharpe_ratio:.2f}")
    print(f"  最大回撤: {performance.max_drawdown:.2%}")
    print(f"  胜率: {performance.win_rate:.1%}")
    print(f"  综合评分: {performance.score:.3f}")
    print(f"  有信号: {'是' if signal else '否'}")

# 选择最优策略
best_signal, comparison = optimizer.select_best_strategy(market_data)

print(f"\\n🎯 最优策略选择:")
if best_signal:
    print(f"选中策略: {best_signal.strategy_name}")
    print(f"信号类型: {best_signal.signal_type}")
    print(f"置信度: {best_signal.confidence:.2f}")
    print(f"预期收益: {best_signal.expected_return:.2%}")
else:
    print("未生成有效信号")

print(f"\\n📈 比较摘要:")
print(f"比较策略数: {comparison['strategies_compared']}")
print(f"生成信号数: {comparison['signals_generated']}")

print("\\n✅ 策略比较测试完成")
        '''
        
        exec(test_code)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def test_database_management():
    """测试数据库管理"""
    print("💾 启动数据库管理测试...")
    
    try:
        test_code = '''
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.signal_database import SignalDatabase, TradingSignal
from datetime import datetime

# 创建数据库
db = SignalDatabase("test_enhanced.db")

print("💾 数据库管理测试")
print("=" * 50)

# 创建测试信号
test_signal = TradingSignal(
    timestamp=datetime.now(),
    strategy_name="配对交易",
    symbol1="000001.SZ",
    symbol2="000002.SZ",
    signal_type="LONG_SHORT",
    confidence=0.85,
    expected_return=0.02,
    risk_level="MEDIUM",
    entry_price1=10.5,
    entry_price2=12.3,
    performance_score=0.75,
    is_historical=False
)

# 保存信号
signal_id = db.save_signal(test_signal)
print(f"📊 保存信号 ID: {signal_id}")

# 获取信号统计
stats = db.get_strategy_statistics()
print(f"\\n📈 信号统计:")
print(f"总信号数: {stats['total_signals']}")
print(f"执行率: {stats['execution_rate']:.1%}")
print(f"平均置信度: {stats['avg_confidence']:.2f}")

# 获取数据库信息
db_info = db.get_database_info()
print(f"\\n💾 数据库信息:")
print(f"数据库大小: {db_info['database_size_mb']:.2f} MB")
print(f"总记录数: {db_info['total_records']}")

print("\\n✅ 数据库管理测试完成")
        '''
        
        exec(test_code)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def run_performance_test():
    """运行性能测试"""
    print("📊 启动系统性能测试...")
    
    tests = [
        ("风险管理", test_risk_management),
        ("策略比较", test_strategy_comparison),
        ("数据库管理", test_database_management)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强版系统可以正常运行")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return False

def run_original_system():
    """运行原版系统"""
    print("📈 启动原版系统...")
    
    try:
        subprocess.run(['streamlit', 'run', 'visual_app.py'])
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def show_system_info():
    """显示系统信息"""
    print("\n📋 增强版系统信息:")
    print(f"  🕒 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  📁 工作目录: {os.getcwd()}")
    print(f"  🐍 Python版本: {sys.version.split()[0]}")
    
    # 检查关键文件
    key_files = [
        'enhanced_visual_app.py',
        'src/risk/enhanced_risk_manager.py',
        'src/strategies/strategy_optimizer.py',
        'src/database/signal_database.py'
    ]
    
    print("\n📂 关键文件检查:")
    for file in key_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")

def main():
    """主函数"""
    print_enhanced_banner()
    show_system_info()
    
    if not check_enhanced_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的包和模块")
        return
    
    while True:
        show_enhanced_menu()
        
        try:
            choice = input("请输入选择 (0-6): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用增强版量化套利系统！")
                print("💰 祝您投资顺利，收益满满！")
                break
            
            elif choice == "1":
                if not run_enhanced_dashboard():
                    print("❌ 增强版仪表板启动失败")
            
            elif choice == "2":
                if not test_risk_management():
                    print("❌ 风险管理测试失败")
            
            elif choice == "3":
                if not test_strategy_comparison():
                    print("❌ 策略比较测试失败")
            
            elif choice == "4":
                if not test_database_management():
                    print("❌ 数据库管理测试失败")
            
            elif choice == "5":
                if not run_performance_test():
                    print("❌ 性能测试失败")
            
            elif choice == "6":
                if not run_original_system():
                    print("❌ 原版系统启动失败")
            
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\\n\\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\\n❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
