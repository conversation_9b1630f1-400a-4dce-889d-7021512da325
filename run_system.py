#!/usr/bin/env python3
"""
量化套利系统启动脚本
提供多种运行模式选择
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    """打印系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🚀 量化套利系统 v1.0                      ║
║                                                              ║
║  📊 基于随机微积分方程的沪深股市量化套利系统                  ║
║  ⚡ 实现实时行业数据输入和交易信号输出                        ║
║  🛡️  符合沪深交易规则，集成风险管理                          ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'streamlit', 'pandas', 'numpy', 'plotly', 
        'scipy', 'matplotlib', 'loguru'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包检查完成")
    return True

def show_menu():
    """显示菜单"""
    menu = """
🎛️  请选择运行模式:

1. 📊 可视化仪表板 (推荐)
   - 完整的Web界面
   - 实时图表和监控
   - 交互式控制面板

2. 🖥️  命令行演示
   - 终端界面演示
   - 随机微积分模型展示
   - 交易信号生成

3. 🧪 简化版演示
   - 快速体验版本
   - 核心功能展示
   - 适合初次使用

4. 📈 项目总结
   - 查看项目概览
   - 功能特性介绍
   - 使用说明

5. 🔧 系统测试
   - 运行系统测试
   - 检查模块完整性
   - 验证功能正常

0. 🚪 退出系统

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    print(menu)

def run_visual_dashboard():
    """运行可视化仪表板"""
    print("🚀 启动可视化仪表板...")
    print("📊 正在加载Web界面，请稍候...")
    
    try:
        # 检查streamlit是否可用
        result = subprocess.run(['streamlit', '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Streamlit未安装或不可用")
            return False
        
        print("🌐 启动Web服务器...")
        print("📱 浏览器将自动打开 http://localhost:8501")
        print("⏹️  按 Ctrl+C 停止服务")
        
        # 启动streamlit应用
        subprocess.run(['streamlit', 'run', 'visual_app.py'])
        
    except KeyboardInterrupt:
        print("\n⏹️  用户停止服务")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def run_command_demo():
    """运行命令行演示"""
    print("🖥️  启动命令行演示...")
    
    try:
        subprocess.run(['python3', 'demo.py'])
    except FileNotFoundError:
        print("❌ demo.py 文件不存在")
        return False
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False
    
    return True

def run_simple_demo():
    """运行简化演示"""
    print("🧪 启动简化版演示...")
    
    try:
        subprocess.run(['python3', 'simple_demo.py'])
    except FileNotFoundError:
        print("❌ simple_demo.py 文件不存在")
        return False
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False
    
    return True

def show_project_summary():
    """显示项目总结"""
    print("📈 加载项目总结...")
    
    try:
        subprocess.run(['python3', 'project_summary.py'])
    except FileNotFoundError:
        print("❌ project_summary.py 文件不存在")
        return False
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False
    
    return True

def run_system_test():
    """运行系统测试"""
    print("🔧 启动系统测试...")
    
    try:
        subprocess.run(['python3', 'test_system.py'])
    except FileNotFoundError:
        print("❌ test_system.py 文件不存在")
        return False
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False
    
    return True

def show_system_info():
    """显示系统信息"""
    print("\n📋 系统信息:")
    print(f"  🕒 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  📁 工作目录: {os.getcwd()}")
    print(f"  🐍 Python版本: {sys.version.split()[0]}")
    print(f"  💻 操作系统: {os.name}")
    
    # 检查关键文件
    key_files = [
        'visual_app.py', 'demo.py', 'simple_demo.py', 
        'project_summary.py', 'requirements.txt'
    ]
    
    print("\n📂 关键文件检查:")
    for file in key_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")

def main():
    """主函数"""
    print_banner()
    
    # 显示系统信息
    show_system_info()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的包")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用量化套利系统！")
                print("🎯 祝您投资顺利，收益满满！")
                break
            
            elif choice == "1":
                if not run_visual_dashboard():
                    print("❌ 可视化仪表板启动失败")
            
            elif choice == "2":
                if not run_command_demo():
                    print("❌ 命令行演示启动失败")
            
            elif choice == "3":
                if not run_simple_demo():
                    print("❌ 简化演示启动失败")
            
            elif choice == "4":
                if not show_project_summary():
                    print("❌ 项目总结显示失败")
            
            elif choice == "5":
                if not run_system_test():
                    print("❌ 系统测试运行失败")
            
            else:
                print("❌ 无效选择，请重新输入")
            
            # 暂停一下，让用户看到结果
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
