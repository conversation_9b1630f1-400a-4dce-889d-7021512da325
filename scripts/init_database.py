#!/usr/bin/env python3
"""
数据库初始化脚本
用于初始化量化套利系统的数据库结构和基础数据
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from src.database.signal_database import SignalDatabase, TradingSignal
    from src.strategies.strategy_optimizer import StrategyPerformance
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def print_header():
    """打印脚本标题"""
    print("=" * 60)
    print("           量化套利系统数据库初始化脚本")
    print("        Quantitative Arbitrage Database Initializer")
    print("=" * 60)
    print()

def check_database_exists(db_path):
    """检查数据库是否已存在"""
    return os.path.exists(db_path)

def backup_existing_database(db_path):
    """备份现有数据库"""
    if check_database_exists(db_path):
        backup_path = f"{db_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 现有数据库已备份到: {backup_path}")
        return backup_path
    return None

def create_directories():
    """创建必要的目录"""
    directories = ['logs', 'data', 'backups', 'temp', 'exports']
    
    print("📁 创建必要目录...")
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录已存在: {directory}")

def initialize_database(db_path):
    """初始化数据库"""
    print(f"🗄️  初始化数据库: {db_path}")
    
    try:
        # 创建数据库实例
        db = SignalDatabase(db_path)
        print("✅ 数据库连接成功")
        
        # 获取数据库信息
        info = db.get_database_info()
        print(f"✅ 数据库初始化完成")
        print(f"   - 数据库大小: {info['database_size_mb']:.2f} MB")
        print(f"   - 表数量: {len(info['tables'])}")
        print(f"   - 总记录数: {info['total_records']}")
        
        return db
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return None

def create_sample_data(db):
    """创建示例数据"""
    print("\n📊 创建示例数据...")
    
    try:
        # 创建示例交易信号
        sample_signals = [
            {
                'strategy_name': '配对交易',
                'symbol1': '000001.SZ',
                'symbol2': '000002.SZ',
                'signal_type': 'LONG_SHORT',
                'confidence': 0.85,
                'expected_return': 0.025,
                'risk_level': 'MEDIUM'
            },
            {
                'strategy_name': '统计套利',
                'symbol1': '600000.SH',
                'symbol2': '600036.SH',
                'signal_type': 'SHORT_LONG',
                'confidence': 0.78,
                'expected_return': 0.018,
                'risk_level': 'LOW'
            },
            {
                'strategy_name': 'ML增强',
                'symbol1': '000858.SZ',
                'symbol2': '002415.SZ',
                'signal_type': 'LONG_SHORT',
                'confidence': 0.92,
                'expected_return': 0.032,
                'risk_level': 'MEDIUM'
            },
            {
                'strategy_name': '波动率套利',
                'symbol1': '600519.SH',
                'symbol2': None,
                'signal_type': 'LONG',
                'confidence': 0.88,
                'expected_return': 0.028,
                'risk_level': 'HIGH'
            },
            {
                'strategy_name': '临时套利',
                'symbol1': '300750.SZ',
                'symbol2': None,
                'signal_type': 'BUY',
                'confidence': 0.95,
                'expected_return': 0.045,
                'risk_level': 'HIGH'
            }
        ]
        
        # 保存示例信号
        saved_count = 0
        for signal_data in sample_signals:
            # 创建多个时间点的信号
            for i in range(3):
                timestamp = datetime.now() - timedelta(hours=i*2)
                
                signal = TradingSignal(
                    timestamp=timestamp,
                    strategy_name=signal_data['strategy_name'],
                    symbol1=signal_data['symbol1'],
                    symbol2=signal_data['symbol2'],
                    signal_type=signal_data['signal_type'],
                    confidence=signal_data['confidence'] + (i * 0.01),
                    expected_return=signal_data['expected_return'] + (i * 0.002),
                    risk_level=signal_data['risk_level'],
                    entry_price1=10.0 + i,
                    entry_price2=12.0 + i if signal_data['symbol2'] else None,
                    performance_score=signal_data['confidence'],
                    is_historical=True
                )
                
                signal_id = db.save_signal(signal)
                saved_count += 1
                
                if saved_count <= 5:  # 只显示前5个
                    print(f"✅ 创建示例信号 {signal_id}: {signal_data['strategy_name']}")
        
        print(f"✅ 总共创建 {saved_count} 个示例信号")
        
        # 创建示例策略性能数据
        print("\n📈 创建示例策略性能数据...")
        strategies = ['配对交易', '统计套利', 'ML增强', '波动率套利', '临时套利']
        
        performance_count = 0
        for strategy in strategies:
            for i in range(5):  # 每个策略5个性能记录
                performance = StrategyPerformance(
                    strategy_name=strategy,
                    total_return=0.02 + (i * 0.005),
                    sharpe_ratio=1.2 + (i * 0.1),
                    max_drawdown=-0.05 - (i * 0.01),
                    win_rate=0.65 + (i * 0.02),
                    avg_return_per_trade=0.001 + (i * 0.0002),
                    volatility=0.15 + (i * 0.01),
                    calmar_ratio=1.5 + (i * 0.1),
                    sortino_ratio=1.8 + (i * 0.1),
                    score=0.75 + (i * 0.02),
                    confidence=0.8 + (i * 0.01)
                )
                performance_count += 1
        
        print(f"✅ 创建 {performance_count} 个策略性能记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        return False

def create_config_files():
    """创建配置文件"""
    print("\n⚙️  创建配置文件...")
    
    # 创建config目录
    config_dir = 'config'
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
        print(f"✅ 创建配置目录: {config_dir}")
    
    # 创建系统配置文件
    config_yaml_path = os.path.join(config_dir, 'config.yaml')
    if not os.path.exists(config_yaml_path):
        config_content = """# 量化套利系统配置文件
system:
  name: "量化套利系统"
  version: "4.0.0"
  timezone: "Asia/Shanghai"
  
trading:
  initial_capital: 200000
  max_position_ratio: 0.7
  emergency_position_ratio: 0.85
  
strategies:
  pairs_trading:
    enabled: true
    lookback_period: 60
  statistical_arbitrage:
    enabled: true
    window_size: 20
  ml_enhanced:
    enabled: true
    retrain_frequency: 30
    
risk_management:
  max_drawdown: 0.1
  var_confidence: 0.95
  
monitoring:
  enabled: true
  port: 8503
  refresh_interval: 3
"""
        
        with open(config_yaml_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
        print(f"✅ 创建配置文件: {config_yaml_path}")
    
    # 检查.env文件
    env_path = '.env'
    if not os.path.exists(env_path):
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy2('.env.example', env_path)
            print(f"✅ 从.env.example创建.env文件")
        else:
            print(f"⚠️  .env.example文件不存在，请手动创建.env文件")
    else:
        print(f"✅ .env文件已存在")

def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    # 检查数据库
    try:
        db = SignalDatabase('quantitative_arbitrage.db')
        info = db.get_database_info()
        print(f"✅ 数据库验证通过 - {info['total_records']} 条记录")
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False
    
    # 检查必要文件
    required_files = [
        'realtime_integrated_app.py',
        'scripts/start_system.sh',
        'scripts/stop_system.sh',
        'scripts/check_status.sh'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"⚠️  文件缺失: {file_path}")
    
    # 检查目录
    required_dirs = ['src', 'logs', 'data', 'backups']
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
            return False
    
    return True

def print_next_steps():
    """打印后续步骤"""
    print("\n" + "=" * 60)
    print("🎉 数据库初始化完成！")
    print("=" * 60)
    print()
    print("📋 后续步骤:")
    print("1. 检查并编辑配置文件:")
    print("   - 编辑 .env 文件设置环境变量")
    print("   - 编辑 config/config.yaml 设置系统参数")
    print()
    print("2. 启动系统:")
    print("   chmod +x scripts/*.sh")
    print("   ./scripts/start_system.sh")
    print()
    print("3. 访问界面:")
    print("   主界面: http://localhost:8503")
    print("   增强界面: http://localhost:8504")
    print("   动态界面: http://localhost:8505")
    print()
    print("4. 管理命令:")
    print("   检查状态: ./scripts/check_status.sh")
    print("   停止系统: ./scripts/stop_system.sh")
    print()
    print("📚 更多信息请查看 DEPLOYMENT_GUIDE.md")

def main():
    """主函数"""
    print_header()
    
    # 数据库路径
    db_path = 'quantitative_arbitrage.db'
    
    # 检查是否已存在数据库
    if check_database_exists(db_path):
        print(f"⚠️  数据库文件已存在: {db_path}")
        response = input("是否要备份现有数据库并重新初始化? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ 初始化已取消")
            return
        
        backup_path = backup_existing_database(db_path)
        if backup_path:
            print(f"✅ 数据库已备份")
    
    # 创建目录
    create_directories()
    
    # 初始化数据库
    db = initialize_database(db_path)
    if not db:
        print("❌ 数据库初始化失败，退出")
        return
    
    # 创建示例数据
    if create_sample_data(db):
        print("✅ 示例数据创建成功")
    
    # 创建配置文件
    create_config_files()
    
    # 验证安装
    if verify_installation():
        print("✅ 安装验证通过")
        print_next_steps()
    else:
        print("❌ 安装验证失败，请检查缺失的文件和目录")

if __name__ == "__main__":
    main()
