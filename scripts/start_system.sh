#!/bin/bash

# 量化套利系统启动脚本
# 使用方法: ./scripts/start_system.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示启动横幅
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    量化套利系统 v4.0                          ║"
echo "║                  Quantitative Arbitrage System              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${BLUE}🚀 正在启动量化套利系统...${NC}"
echo ""

# 检查操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac
echo "💻 检测到操作系统: $MACHINE"

# 检查Python环境
echo "🐍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安装，请先安装Python 3.8+${NC}"
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
echo "✅ Python版本: $PYTHON_VERSION"

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo -e "${YELLOW}⚠️  当前未在虚拟环境中，建议使用虚拟环境${NC}"
    echo "   创建虚拟环境: python3 -m venv venv"
    echo "   激活虚拟环境: source venv/bin/activate"
else
    echo "✅ 虚拟环境: $VIRTUAL_ENV"
fi

# 检查依赖
echo ""
echo "📦 检查系统依赖..."
REQUIRED_PACKAGES=("streamlit" "pandas" "numpy" "plotly" "loguru")
MISSING_PACKAGES=()

for package in "${REQUIRED_PACKAGES[@]}"; do
    if python3 -c "import $package" 2>/dev/null; then
        echo "✅ $package"
    else
        echo -e "${RED}❌ $package${NC}"
        MISSING_PACKAGES+=($package)
    fi
done

if [ ${#MISSING_PACKAGES[@]} -ne 0 ]; then
    echo -e "${RED}❌ 缺少依赖包，请运行: pip install -r requirements.txt${NC}"
    exit 1
fi

# 检查配置文件
echo ""
echo "⚙️  检查配置文件..."
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env文件不存在，创建默认配置${NC}"
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ 已从.env.example创建.env文件"
    else
        echo "# 量化套利系统环境配置" > .env
        echo "ENVIRONMENT=development" >> .env
        echo "DEBUG=true" >> .env
        echo "LOG_LEVEL=INFO" >> .env
        echo "INITIAL_CAPITAL=200000" >> .env
        echo "✅ 已创建默认.env文件"
    fi
else
    echo "✅ .env配置文件存在"
fi

# 创建必要目录
echo ""
echo "📁 创建必要目录..."
DIRECTORIES=("logs" "data" "backups" "temp")
for dir in "${DIRECTORIES[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "✅ 创建目录: $dir"
    else
        echo "✅ 目录已存在: $dir"
    fi
done

# 检查数据库
echo ""
echo "🗄️  检查数据库连接..."
python3 -c "
import sys
sys.path.append('.')
try:
    from src.database.signal_database import SignalDatabase
    db = SignalDatabase('quantitative_arbitrage.db')
    info = db.get_database_info()
    print(f'✅ 数据库连接正常 - {info[\"total_records\"]} 条记录')
except Exception as e:
    print(f'⚠️  数据库初始化: {e}')
    print('✅ 数据库已自动初始化')
"

# 检查端口占用
echo ""
echo "🌐 检查端口占用..."
PORTS=(8503 8504 8505)
OCCUPIED_PORTS=()

for port in "${PORTS[@]}"; do
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $port 已被占用${NC}"
        OCCUPIED_PORTS+=($port)
    else
        echo "✅ 端口 $port 可用"
    fi
done

if [ ${#OCCUPIED_PORTS[@]} -ne 0 ]; then
    echo -e "${YELLOW}是否要终止占用端口的进程? (y/N)${NC}"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        for port in "${OCCUPIED_PORTS[@]}"; do
            echo "🔴 终止端口 $port 的进程"
            lsof -ti :$port | xargs kill -9 2>/dev/null || true
        done
    fi
fi

# 启动系统组件
echo ""
echo -e "${GREEN}🎯 启动系统组件...${NC}"

# 启动主界面
echo "📊 启动主界面 (端口 8503)..."
nohup python3 -m streamlit run realtime_integrated_app.py --server.port 8503 --server.headless true > logs/main_app.log 2>&1 &
MAIN_PID=$!
echo $MAIN_PID > .main.pid
echo "✅ 主界面已启动 (PID: $MAIN_PID)"

# 等待主界面启动
sleep 3

# 启动增强界面 (可选)
if [ -f "enhanced_visual_app.py" ]; then
    echo "🔧 启动增强界面 (端口 8504)..."
    nohup python3 -m streamlit run enhanced_visual_app.py --server.port 8504 --server.headless true > logs/enhanced_app.log 2>&1 &
    ENHANCED_PID=$!
    echo $ENHANCED_PID > .enhanced.pid
    echo "✅ 增强界面已启动 (PID: $ENHANCED_PID)"
fi

# 启动动态界面 (可选)
if [ -f "dynamic_visual_app.py" ]; then
    echo "⚡ 启动动态界面 (端口 8505)..."
    nohup python3 -m streamlit run dynamic_visual_app.py --server.port 8505 --server.headless true > logs/dynamic_app.log 2>&1 &
    DYNAMIC_PID=$!
    echo $DYNAMIC_PID > .dynamic.pid
    echo "✅ 动态界面已启动 (PID: $DYNAMIC_PID)"
fi

# 等待所有服务启动
echo ""
echo "⏳ 等待服务启动完成..."
sleep 5

# 验证服务状态
echo ""
echo "🔍 验证服务状态..."
SERVICES_OK=true

for port in 8503 8504 8505; do
    if curl -s http://localhost:$port > /dev/null 2>&1; then
        echo "✅ 端口 $port 服务正常"
    else
        if [ $port -eq 8503 ]; then
            echo -e "${RED}❌ 端口 $port 服务异常${NC}"
            SERVICES_OK=false
        else
            echo -e "${YELLOW}⚠️  端口 $port 服务未启动 (可选服务)${NC}"
        fi
    fi
done

# 显示启动结果
echo ""
if [ "$SERVICES_OK" = true ]; then
    echo -e "${GREEN}✅ 系统启动完成！${NC}"
else
    echo -e "${YELLOW}⚠️  系统部分启动，请检查日志${NC}"
fi

echo ""
echo -e "${BLUE}📱 访问地址:${NC}"
echo "  🎯 主界面 (实时集成):  http://localhost:8503"
if [ -f ".enhanced.pid" ]; then
    echo "  🔧 增强界面:          http://localhost:8504"
fi
if [ -f ".dynamic.pid" ]; then
    echo "  ⚡ 动态界面:          http://localhost:8505"
fi

echo ""
echo -e "${BLUE}🛠️  管理命令:${NC}"
echo "  检查状态: ./scripts/check_status.sh"
echo "  停止系统: ./scripts/stop_system.sh"
echo "  查看日志: tail -f logs/main_app.log"

echo ""
echo -e "${GREEN}🎉 量化套利系统已成功启动！${NC}"
echo -e "${YELLOW}💡 提示: 首次启动可能需要几分钟来初始化所有组件${NC}"
