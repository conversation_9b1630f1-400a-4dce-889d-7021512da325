#!/bin/bash

# 量化套利系统停止脚本
# 使用方法: ./scripts/stop_system.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${YELLOW}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    停止量化套利系统                           ║"
echo "║                  Stopping Arbitrage System                  ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${BLUE}🛑 正在停止量化套利系统...${NC}"
echo ""

# 停止函数
stop_service() {
    local service_name=$1
    local pid_file=$2
    local port=$3
    
    if [ -f "$pid_file" ]; then
        PID=$(cat $pid_file)
        if kill -0 $PID 2>/dev/null; then
            echo "🔴 停止 $service_name (PID: $PID)"
            kill $PID
            
            # 等待进程结束
            local count=0
            while kill -0 $PID 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制终止
            if kill -0 $PID 2>/dev/null; then
                echo "⚠️  强制终止 $service_name"
                kill -9 $PID 2>/dev/null || true
            fi
            
            echo "✅ $service_name 已停止"
        else
            echo "⚠️  $service_name 进程不存在 (PID: $PID)"
        fi
        rm -f $pid_file
    else
        echo "⚠️  $service_name PID文件不存在"
    fi
    
    # 检查端口是否仍被占用
    if [ -n "$port" ] && lsof -i :$port > /dev/null 2>&1; then
        echo "🔴 端口 $port 仍被占用，强制释放"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
    fi
}

# 停止统一界面
echo "🚀 停止统一界面..."
stop_service "统一界面" ".unified.pid" "8506"

# 停止原主界面
echo "📊 停止原主界面..."
stop_service "原主界面" ".main.pid" "8503"

# 停止增强界面
echo "🔧 停止增强界面..."
stop_service "增强界面" ".enhanced.pid" "8504"

# 停止动态界面
echo "⚡ 停止动态界面..."
stop_service "动态界面" ".dynamic.pid" "8505"

# 清理所有Streamlit进程
echo ""
echo "🧹 清理残留进程..."
STREAMLIT_PIDS=$(pgrep -f "streamlit run" 2>/dev/null || true)
if [ -n "$STREAMLIT_PIDS" ]; then
    echo "🔴 发现残留Streamlit进程，正在清理..."
    echo $STREAMLIT_PIDS | xargs kill -9 2>/dev/null || true
    echo "✅ 残留进程已清理"
else
    echo "✅ 无残留进程"
fi

# 清理Python进程 (谨慎操作)
echo ""
echo "🔍 检查相关Python进程..."
PYTHON_PIDS=$(pgrep -f "realtime_integrated_app\|enhanced_visual_app\|dynamic_visual_app" 2>/dev/null || true)
if [ -n "$PYTHON_PIDS" ]; then
    echo "🔴 发现相关Python进程，正在清理..."
    echo $PYTHON_PIDS | xargs kill 2>/dev/null || true
    sleep 2
    # 如果还在运行，强制终止
    PYTHON_PIDS=$(pgrep -f "realtime_integrated_app\|enhanced_visual_app\|dynamic_visual_app" 2>/dev/null || true)
    if [ -n "$PYTHON_PIDS" ]; then
        echo $PYTHON_PIDS | xargs kill -9 2>/dev/null || true
    fi
    echo "✅ Python进程已清理"
fi

# 检查端口状态
echo ""
echo "🌐 检查端口状态..."
PORTS=(8503 8504 8505)
for port in "${PORTS[@]}"; do
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $port 仍被占用${NC}"
        # 显示占用进程信息
        lsof -i :$port
    else
        echo "✅ 端口 $port 已释放"
    fi
done

# 清理临时文件
echo ""
echo "🗑️  清理临时文件..."
TEMP_FILES=(".main.pid" ".enhanced.pid" ".dynamic.pid" "temp/*" "*.pyc" "__pycache__")
for pattern in "${TEMP_FILES[@]}"; do
    if ls $pattern 1> /dev/null 2>&1; then
        rm -rf $pattern
        echo "✅ 清理: $pattern"
    fi
done

# 保存停止时间日志
echo ""
echo "📝 记录停止日志..."
STOP_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo "[$STOP_TIME] 系统停止" >> logs/system_events.log

# 显示系统资源状态
echo ""
echo "📊 系统资源状态:"
if command -v free &> /dev/null; then
    echo "内存使用:"
    free -h | head -2
elif command -v vm_stat &> /dev/null; then
    echo "内存使用 (macOS):"
    vm_stat | head -5
fi

if command -v df &> /dev/null; then
    echo ""
    echo "磁盘使用:"
    df -h . | tail -1
fi

# 检查日志文件大小
echo ""
echo "📋 日志文件状态:"
if [ -d "logs" ]; then
    LOG_SIZE=$(du -sh logs 2>/dev/null | cut -f1)
    echo "日志目录大小: $LOG_SIZE"
    
    # 如果日志文件过大，提示清理
    LOG_SIZE_MB=$(du -sm logs 2>/dev/null | cut -f1)
    if [ "$LOG_SIZE_MB" -gt 100 ]; then
        echo -e "${YELLOW}⚠️  日志文件较大 (${LOG_SIZE})，建议清理${NC}"
        echo "   清理命令: ./scripts/cleanup_logs.sh"
    fi
fi

# 显示停止完成信息
echo ""
echo -e "${GREEN}✅ 量化套利系统已完全停止${NC}"
echo ""
echo -e "${BLUE}📋 后续操作:${NC}"
echo "  重新启动: ./scripts/start_system.sh"
echo "  检查状态: ./scripts/check_status.sh"
echo "  查看日志: ls -la logs/"
echo "  清理日志: ./scripts/cleanup_logs.sh"

# 可选：询问是否备份数据
echo ""
echo -e "${YELLOW}💾 是否需要备份数据库? (y/N)${NC}"
read -r -t 10 response || response="n"
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    if [ -f "quantitative_arbitrage.db" ]; then
        BACKUP_NAME="backup_$(date '+%Y%m%d_%H%M%S').db"
        cp quantitative_arbitrage.db "backups/$BACKUP_NAME"
        echo "✅ 数据库已备份到: backups/$BACKUP_NAME"
    else
        echo "⚠️  数据库文件不存在"
    fi
fi

echo ""
echo -e "${GREEN}🎉 系统停止完成！感谢使用量化套利系统${NC}"
