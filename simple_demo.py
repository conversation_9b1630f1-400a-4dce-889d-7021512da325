#!/usr/bin/env python3
"""
简化版量化套利系统演示
"""

import numpy as np
import pandas as pd
from datetime import datetime
import time

class SimpleBlackScholes:
    """简化的Black-Scholes模型"""
    
    def __init__(self, S0, mu, sigma, r, T):
        self.S0 = S0      # 初始股价
        self.mu = mu      # 漂移率
        self.sigma = sigma # 波动率
        self.r = r        # 无风险利率
        self.T = T        # 到期时间
    
    def simulate_price_path(self, n_steps=252):
        """模拟股价路径"""
        dt = self.T / n_steps
        prices = [self.S0]
        
        for i in range(n_steps):
            dW = np.random.normal(0, np.sqrt(dt))
            dS = self.mu * prices[-1] * dt + self.sigma * prices[-1] * dW
            prices.append(prices[-1] + dS)
        
        return prices
    
    def option_price(self, K, option_type="call"):
        """Black-Scholes期权定价"""
        from scipy.stats import norm
        
        d1 = (np.log(self.S0/K) + (self.r + 0.5*self.sigma**2)*self.T) / (self.sigma*np.sqrt(self.T))
        d2 = d1 - self.sigma*np.sqrt(self.T)
        
        if option_type == "call":
            price = self.S0*norm.cdf(d1) - K*np.exp(-self.r*self.T)*norm.cdf(d2)
        else:
            price = K*np.exp(-self.r*self.T)*norm.cdf(-d2) - self.S0*norm.cdf(-d1)
        
        return price

class SimplePairTrading:
    """简化的配对交易策略"""
    
    def __init__(self, entry_threshold=2.0):
        self.entry_threshold = entry_threshold
        self.price_history = {}
    
    def add_price(self, symbol, price):
        """添加价格数据"""
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        self.price_history[symbol].append(price)
        
        # 保持最近60个价格
        if len(self.price_history[symbol]) > 60:
            self.price_history[symbol] = self.price_history[symbol][-60:]
    
    def generate_signal(self, symbol1, symbol2, price1, price2):
        """生成交易信号"""
        self.add_price(symbol1, price1)
        self.add_price(symbol2, price2)
        
        if (len(self.price_history.get(symbol1, [])) < 20 or 
            len(self.price_history.get(symbol2, [])) < 20):
            return None
        
        # 计算价格比率
        ratios = []
        for p1, p2 in zip(self.price_history[symbol1], self.price_history[symbol2]):
            ratios.append(p1 / p2)
        
        # 计算Z-score
        mean_ratio = np.mean(ratios)
        std_ratio = np.std(ratios)
        current_ratio = price1 / price2
        
        if std_ratio == 0:
            return None
        
        z_score = (current_ratio - mean_ratio) / std_ratio
        
        if abs(z_score) > self.entry_threshold:
            signal_type = "SHORT_LONG" if z_score > 0 else "LONG_SHORT"
            confidence = min(abs(z_score) / self.entry_threshold, 1.0)
            
            return {
                "timestamp": datetime.now(),
                "symbol1": symbol1,
                "symbol2": symbol2,
                "signal_type": signal_type,
                "z_score": z_score,
                "confidence": confidence,
                "price1": price1,
                "price2": price2
            }
        
        return None

class SimpleArbitrageEngine:
    """简化的套利引擎"""
    
    def __init__(self):
        self.pair_strategy = SimplePairTrading()
        self.signals = []
        self.running = False
    
    def start_simulation(self, duration=30):
        """启动模拟"""
        print("🚀 启动量化套利系统模拟")
        print("=" * 50)
        
        self.running = True
        start_time = time.time()
        
        # 模拟股票对
        stocks = [
            ("平安银行", "000001.SZ"),
            ("万科A", "000002.SZ"),
            ("浦发银行", "600000.SH"),
            ("招商银行", "600036.SH")
        ]
        
        # 初始价格
        prices = {
            "000001.SZ": 10.0 + np.random.normal(0, 0.5),
            "000002.SZ": 12.0 + np.random.normal(0, 0.6),
            "600000.SH": 8.0 + np.random.normal(0, 0.4),
            "600036.SH": 45.0 + np.random.normal(0, 2.0)
        }
        
        signal_count = 0
        
        while self.running and (time.time() - start_time) < duration:
            # 更新价格（模拟实时数据）
            for symbol in prices:
                change = np.random.normal(0, 0.02)  # 2%的随机变动
                prices[symbol] *= (1 + change)
                prices[symbol] = max(prices[symbol], 0.1)  # 防止负价格
            
            # 检查配对交易机会
            pairs = [
                ("000001.SZ", "600036.SH"),  # 两只银行股
                ("000002.SZ", "600000.SH"),  # 地产和银行
            ]
            
            for symbol1, symbol2 in pairs:
                signal = self.pair_strategy.generate_signal(
                    symbol1, symbol2, prices[symbol1], prices[symbol2]
                )
                
                if signal:
                    signal_count += 1
                    self.signals.append(signal)
                    self.print_signal(signal, signal_count)
            
            time.sleep(2)  # 2秒更新一次
        
        print(f"\n📊 模拟结束，共生成 {signal_count} 个交易信号")
        return self.signals
    
    def print_signal(self, signal, count):
        """打印交易信号"""
        print(f"\n🎯 交易信号 #{count} [{signal['timestamp'].strftime('%H:%M:%S')}]")
        print(f"   交易对: {signal['symbol1']} ↔ {signal['symbol2']}")
        print(f"   信号类型: {signal['signal_type']}")
        print(f"   Z-Score: {signal['z_score']:.2f}")
        print(f"   置信度: {signal['confidence']:.2f}")
        print(f"   当前价格: {signal['price1']:.2f} / {signal['price2']:.2f}")
        
        if signal['signal_type'] == "LONG_SHORT":
            print(f"   建议: 买入 {signal['symbol1']}, 卖出 {signal['symbol2']}")
        else:
            print(f"   建议: 卖出 {signal['symbol1']}, 买入 {signal['symbol2']}")
    
    def stop(self):
        """停止引擎"""
        self.running = False

def demonstrate_black_scholes():
    """演示Black-Scholes模型"""
    print("\n📈 Black-Scholes模型演示")
    print("-" * 40)
    
    # 创建模型
    model = SimpleBlackScholes(S0=100, mu=0.05, sigma=0.2, r=0.03, T=1.0)
    
    print(f"模型参数:")
    print(f"  初始股价: {model.S0}")
    print(f"  年化收益率: {model.mu:.1%}")
    print(f"  年化波动率: {model.sigma:.1%}")
    print(f"  无风险利率: {model.r:.1%}")
    
    # 期权定价
    try:
        call_price = model.option_price(105, "call")
        put_price = model.option_price(105, "put")
        print(f"\n期权定价 (执行价=105):")
        print(f"  看涨期权: {call_price:.4f}")
        print(f"  看跌期权: {put_price:.4f}")
    except:
        print("\n期权定价需要scipy库，跳过此部分")
    
    # 模拟股价路径
    path = model.simulate_price_path(10)
    print(f"\n股价模拟路径 (10步):")
    print(f"  起始价格: {path[0]:.2f}")
    print(f"  结束价格: {path[-1]:.2f}")
    print(f"  收益率: {(path[-1]/path[0]-1)*100:.1f}%")

def show_market_rules():
    """显示沪深股市交易规则"""
    print("\n📋 沪深股市交易规则")
    print("-" * 40)
    
    rules = [
        "🕘 交易时间: 9:30-11:30, 13:00-15:00",
        "📈 涨跌停: 主板±10%, 创业板/科创板±20%",
        "💱 最小变动: 0.01元",
        "📦 交易单位: 100股/手",
        "🔄 交易制度: T+1",
        "💰 费用: 佣金+印花税+过户费",
        "🚫 不允许裸卖空",
        "⏰ 集合竞价: 9:15-9:25, 14:57-15:00"
    ]
    
    for rule in rules:
        print(f"  {rule}")

def main():
    """主函数"""
    print("🚀 量化套利系统 - 简化演示版")
    print("=" * 60)
    print("基于随机微积分方程的沪深股市套利系统")
    print("实现实时行业数据输入和交易信号输出")
    print("=" * 60)
    
    while True:
        print("\n请选择演示模块:")
        print("1. Black-Scholes随机微积分模型")
        print("2. 配对交易策略实时模拟")
        print("3. 沪深股市交易规则")
        print("4. 完整系统演示")
        print("0. 退出")
        
        try:
            choice = input("\n请输入选择 (0-4): ").strip()
            
            if choice == "0":
                print("感谢使用量化套利系统！")
                break
            elif choice == "1":
                demonstrate_black_scholes()
            elif choice == "2":
                engine = SimpleArbitrageEngine()
                try:
                    engine.start_simulation(duration=20)
                except KeyboardInterrupt:
                    print("\n用户中断模拟")
                    engine.stop()
            elif choice == "3":
                show_market_rules()
            elif choice == "4":
                demonstrate_black_scholes()
                show_market_rules()
                print("\n启动实时套利模拟...")
                engine = SimpleArbitrageEngine()
                try:
                    engine.start_simulation(duration=15)
                except KeyboardInterrupt:
                    print("\n用户中断模拟")
                    engine.stop()
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n程序被中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
