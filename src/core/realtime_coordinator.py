"""
实时系统协调器
负责协调所有模块间的联动，确保实时行情下自动调用策略、发现机会并显示到界面
"""

import threading
import time
import queue
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from loguru import logger
import numpy as np

try:
    from ..strategies.strategy_optimizer import StrategyOptimizer, StrategySignal
    from ..strategies.strategy_combiner import StrategyCombiner, CombinationMethod
    from ..risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
    from ..database.signal_database import SignalDatabase, TradingSignal
except ImportError:
    try:
        from strategies.strategy_optimizer import StrategyOptimizer, StrategySignal
        from strategies.strategy_combiner import StrategyCombiner, CombinationMethod
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
        from database.signal_database import SignalDatabase, TradingSignal
    except ImportError:
        # 简化导入用于测试
        StrategyOptimizer = None
        StrategySignal = None


@dataclass
class SystemEvent:
    """系统事件"""
    event_type: str  # 'market_data', 'signal_generated', 'risk_alert', 'strategy_update'
    timestamp: datetime
    data: Any
    source: str
    priority: int = 1  # 1=低, 2=中, 3=高, 4=紧急


@dataclass
class OpportunityAlert:
    """机会警报"""
    opportunity_id: str
    strategy_name: str
    symbols: List[str]
    confidence: float
    expected_return: float
    risk_level: str
    alert_time: datetime
    is_emergency: bool = False


class RealTimeCoordinator:
    """实时系统协调器"""
    
    def __init__(self, initial_capital: float = 200000):
        # 核心组件
        self.data_provider = None

        # 安全初始化组件
        try:
            self.strategy_optimizer = StrategyOptimizer() if StrategyOptimizer else None
            self.strategy_combiner = StrategyCombiner(CombinationMethod.DYNAMIC_WEIGHT) if StrategyCombiner else None
        except:
            self.strategy_optimizer = None
            self.strategy_combiner = None

        # 简化的策略组件
        self.ml_strategy = None
        self.multifactor_strategy = None
        self.emergency_engine = None

        # 风险管理
        try:
            limits = DynamicPositionLimits(initial_capital=initial_capital)
            self.risk_manager = DynamicRiskManager(limits)
        except:
            self.risk_manager = None

        # 数据库
        try:
            self.database = SignalDatabase("realtime_system.db")
        except:
            self.database = None
        
        # 系统状态
        self.is_running = False
        self.last_update = datetime.now()
        self.update_interval = 3  # 3秒更新间隔
        
        # 事件队列和回调
        self.event_queue = queue.PriorityQueue()
        self.ui_callbacks = []  # UI更新回调
        self.signal_callbacks = []  # 信号回调
        self.alert_callbacks = []  # 警报回调
        
        # 实时数据存储
        self.current_market_data = {}
        self.recent_signals = []
        self.active_opportunities = []
        self.system_metrics = {
            'total_signals_today': 0,
            'successful_signals': 0,
            'current_pnl': 0.0,
            'active_positions': 0
        }
        
        # 监控的股票列表
        self.monitored_symbols = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ',
            '600000.SH', '600036.SH', '600519.SH', '000858.SZ'
        ]
        
        logger.info(f"实时系统协调器初始化完成，监控{len(self.monitored_symbols)}只股票")
    
    def add_ui_callback(self, callback: Callable[[Dict], None]):
        """添加UI更新回调"""
        self.ui_callbacks.append(callback)
    
    def add_signal_callback(self, callback: Callable[[StrategySignal], None]):
        """添加信号回调"""
        self.signal_callbacks.append(callback)
    
    def add_alert_callback(self, callback: Callable[[OpportunityAlert], None]):
        """添加警报回调"""
        self.alert_callbacks.append(callback)
    
    def start(self):
        """启动实时协调器"""
        if self.is_running:
            logger.warning("系统已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 启动实时系统协调器")
        
        # 启动主要线程
        threading.Thread(target=self._main_coordination_loop, daemon=True).start()
        threading.Thread(target=self._market_data_loop, daemon=True).start()
        threading.Thread(target=self._strategy_analysis_loop, daemon=True).start()
        threading.Thread(target=self._event_processing_loop, daemon=True).start()
        
        logger.info("✅ 所有协调线程已启动")
    
    def stop(self):
        """停止实时协调器"""
        self.is_running = False
        logger.info("⏹️ 实时系统协调器已停止")
    
    def _main_coordination_loop(self):
        """主协调循环"""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 更新系统状态
                self._update_system_metrics()
                
                # 检查风险状况
                self._check_risk_conditions()
                
                # 更新UI
                self._notify_ui_update()
                
                # 记录系统心跳
                if (current_time - self.last_update).seconds >= 30:
                    logger.info(f"💓 系统心跳 - 活跃信号: {len(self.recent_signals)}, 监控股票: {len(self.current_market_data)}")
                    self.last_update = current_time
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"主协调循环错误: {e}")
                time.sleep(self.update_interval)
    
    def _market_data_loop(self):
        """市场数据循环"""
        while self.is_running:
            try:
                # 生成模拟市场数据（实际应用中替换为真实数据源）
                market_data = self._generate_mock_market_data()
                
                # 更新当前市场数据
                self.current_market_data.update(market_data)
                
                # 发送市场数据事件
                event = SystemEvent(
                    event_type='market_data',
                    timestamp=datetime.now(),
                    data=market_data,
                    source='market_data_loop',
                    priority=2
                )
                self.event_queue.put((event.priority, event))
                
                time.sleep(2)  # 2秒更新市场数据
                
            except Exception as e:
                logger.error(f"市场数据循环错误: {e}")
                time.sleep(2)
    
    def _strategy_analysis_loop(self):
        """策略分析循环"""
        while self.is_running:
            try:
                if not self.current_market_data:
                    time.sleep(5)
                    continue
                
                # 运行所有策略分析
                opportunities = self._run_all_strategies()
                
                # 处理发现的机会
                for opportunity in opportunities:
                    self._process_opportunity(opportunity)
                
                time.sleep(5)  # 5秒运行一次策略分析
                
            except Exception as e:
                logger.error(f"策略分析循环错误: {e}")
                time.sleep(5)
    
    def _event_processing_loop(self):
        """事件处理循环"""
        while self.is_running:
            try:
                # 处理事件队列
                if not self.event_queue.empty():
                    priority, event = self.event_queue.get(timeout=1)
                    self._handle_system_event(event)
                else:
                    time.sleep(0.1)
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"事件处理循环错误: {e}")
                time.sleep(1)
    
    def _generate_mock_market_data(self) -> Dict[str, Dict]:
        """生成模拟市场数据"""
        market_data = {}
        
        for symbol in self.monitored_symbols:
            # 生成随机价格变动
            base_price = 10 + hash(symbol) % 40  # 基础价格10-50
            price_change = np.random.normal(0, 0.02)  # 2%标准差的价格变动
            
            market_data[symbol] = {
                'price': base_price * (1 + price_change),
                'change': price_change,
                'volume': np.random.randint(1000000, 10000000),
                'timestamp': datetime.now(),
                'bid': base_price * (1 + price_change - 0.001),
                'ask': base_price * (1 + price_change + 0.001)
            }
        
        return market_data
    
    def _run_all_strategies(self) -> List[OpportunityAlert]:
        """运行所有策略"""
        opportunities = []

        try:
            # 1. 策略优化器
            if self.strategy_optimizer:
                try:
                    best_signal, comparison = self.strategy_optimizer.select_best_strategy(self.current_market_data)
                    if best_signal:
                        alert = OpportunityAlert(
                            opportunity_id=f"opt_{datetime.now().strftime('%H%M%S')}",
                            strategy_name=best_signal.strategy_name,
                            symbols=[best_signal.symbol1, best_signal.symbol2],
                            confidence=best_signal.confidence,
                            expected_return=best_signal.expected_return,
                            risk_level=best_signal.risk_level,
                            alert_time=datetime.now()
                        )
                        opportunities.append(alert)
                except Exception as e:
                    logger.debug(f"策略优化器执行警告: {e}")

            # 2. 临时套利检测
            if self.risk_manager:
                try:
                    should_emergency, emergency_signal = self.risk_manager.should_trigger_emergency_arbitrage(self.current_market_data)
                    if should_emergency and emergency_signal:
                        alert = OpportunityAlert(
                            opportunity_id=f"emg_{datetime.now().strftime('%H%M%S')}",
                            strategy_name="临时套利",
                            symbols=[emergency_signal.trigger_symbol],
                            confidence=emergency_signal.confidence,
                            expected_return=0.03,  # 临时套利预期收益
                            risk_level="HIGH",
                            alert_time=datetime.now(),
                            is_emergency=True
                        )
                        opportunities.append(alert)
                except Exception as e:
                    logger.debug(f"风险管理器执行警告: {e}")

            # 3. 简化的模拟策略
            if len(self.current_market_data) >= 2:
                # 生成模拟机会
                symbols = list(self.current_market_data.keys())
                if len(symbols) >= 2 and np.random.random() < 0.3:  # 30%概率生成机会
                    alert = OpportunityAlert(
                        opportunity_id=f"sim_{datetime.now().strftime('%H%M%S')}",
                        strategy_name="模拟配对交易",
                        symbols=symbols[:2],
                        confidence=np.random.uniform(0.6, 0.9),
                        expected_return=np.random.uniform(0.01, 0.03),
                        risk_level="MEDIUM",
                        alert_time=datetime.now()
                    )
                    opportunities.append(alert)

        except Exception as e:
            logger.error(f"策略运行错误: {e}")

        return opportunities
    
    def _process_opportunity(self, opportunity: OpportunityAlert):
        """处理发现的机会"""
        # 添加到活跃机会列表
        self.active_opportunities.append(opportunity)
        
        # 保持最近50个机会
        if len(self.active_opportunities) > 50:
            self.active_opportunities = self.active_opportunities[-50:]
        
        # 创建交易信号并保存到数据库
        if self.database and TradingSignal:
            try:
                signal = TradingSignal(
                    timestamp=opportunity.alert_time,
                    strategy_name=opportunity.strategy_name,
                    symbol1=opportunity.symbols[0] if opportunity.symbols else "UNKNOWN",
                    symbol2=opportunity.symbols[1] if len(opportunity.symbols) > 1 else None,
                    signal_type="LONG_SHORT",
                    confidence=opportunity.confidence,
                    expected_return=opportunity.expected_return,
                    risk_level=opportunity.risk_level,
                    entry_price1=self.current_market_data.get(opportunity.symbols[0], {}).get('price', 0) if opportunity.symbols else 0,
                    entry_price2=self.current_market_data.get(opportunity.symbols[1], {}).get('price', 0) if len(opportunity.symbols) > 1 else None,
                    performance_score=opportunity.confidence,
                    is_historical=False
                )

                signal_id = self.database.save_signal(signal)
                logger.info(f"💡 发现新机会: {opportunity.strategy_name} - {'/'.join(opportunity.symbols)} (ID: {signal_id})")

                # 更新系统指标
                self.system_metrics['total_signals_today'] += 1

                # 通知回调
                for callback in self.signal_callbacks:
                    try:
                        callback(signal)
                    except Exception as e:
                        logger.error(f"信号回调错误: {e}")

            except Exception as e:
                logger.error(f"保存信号失败: {e}")

        # 通知警报回调
        for callback in self.alert_callbacks:
            try:
                callback(opportunity)
            except Exception as e:
                logger.error(f"警报回调错误: {e}")
    
    def _handle_system_event(self, event: SystemEvent):
        """处理系统事件"""
        if event.event_type == 'market_data':
            # 市场数据更新事件
            pass
        elif event.event_type == 'signal_generated':
            # 信号生成事件
            pass
        elif event.event_type == 'risk_alert':
            # 风险警报事件
            logger.warning(f"⚠️ 风险警报: {event.data}")
    
    def _update_system_metrics(self):
        """更新系统指标"""
        try:
            if self.risk_manager:
                # 更新资金状况
                self.risk_manager.update_total_capital()

                # 计算当日PnL
                self.system_metrics['current_pnl'] = self.risk_manager.total_return * self.risk_manager.initial_capital

                # 统计活跃仓位
                self.system_metrics['active_positions'] = len(self.risk_manager.positions)
            else:
                # 模拟指标
                self.system_metrics['current_pnl'] = np.random.normal(500, 200)
                self.system_metrics['active_positions'] = np.random.randint(0, 5)
        except Exception as e:
            logger.debug(f"系统指标更新警告: {e}")
    
    def _check_risk_conditions(self):
        """检查风险条件"""
        try:
            if self.risk_manager:
                # 检查最大回撤
                if self.risk_manager.max_drawdown > 0.1:  # 10%回撤警报
                    event = SystemEvent(
                        event_type='risk_alert',
                        timestamp=datetime.now(),
                        data=f"最大回撤超过10%: {self.risk_manager.max_drawdown:.2%}",
                        source='risk_manager',
                        priority=4
                    )
                    self.event_queue.put((event.priority, event))
        except Exception as e:
            logger.debug(f"风险检查警告: {e}")
    
    def _notify_ui_update(self):
        """通知UI更新"""
        ui_data = {
            'market_data': self.current_market_data,
            'recent_signals': self.recent_signals[-10:],  # 最近10个信号
            'active_opportunities': self.active_opportunities[-5:],  # 最近5个机会
            'system_metrics': self.system_metrics,
            'risk_status': {
                'current_mode': self.risk_manager.current_mode.value,
                'position_limit': self.risk_manager.get_current_position_limit(),
                'total_capital': self.risk_manager.total_capital,
                'available_cash': self.risk_manager.available_cash
            },
            'timestamp': datetime.now()
        }
        
        for callback in self.ui_callbacks:
            try:
                callback(ui_data)
            except Exception as e:
                logger.error(f"UI回调错误: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            'is_running': self.is_running,
            'monitored_symbols': len(self.monitored_symbols),
            'active_opportunities': len(self.active_opportunities),
            'system_metrics': self.system_metrics,
            'last_update': self.last_update
        }

        if self.risk_manager:
            try:
                status['risk_manager_status'] = {
                    'mode': self.risk_manager.current_mode.value,
                    'total_capital': self.risk_manager.total_capital,
                    'position_limit': self.risk_manager.get_current_position_limit()
                }
            except:
                status['risk_manager_status'] = {
                    'mode': 'normal',
                    'total_capital': 200000,
                    'position_limit': 0.7
                }
        else:
            status['risk_manager_status'] = {
                'mode': 'normal',
                'total_capital': 200000,
                'position_limit': 0.7
            }

        return status
    
    def force_strategy_scan(self):
        """强制执行策略扫描"""
        if self.current_market_data:
            opportunities = self._run_all_strategies()
            for opportunity in opportunities:
                self._process_opportunity(opportunity)
            logger.info(f"🔍 强制扫描完成，发现 {len(opportunities)} 个机会")
            return opportunities
        return []
