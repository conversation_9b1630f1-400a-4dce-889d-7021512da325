"""
API数据源实现
支持多种API数据源：Tushare、Yahoo Finance、Alpha Vantage等
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger
import time
import json

from .data_source_interface import (
    DataSourceInterface, DataSourceConfig, MarketDataPoint, 
    DataFrequency, DataConverter, DataValidator
)


class TushareDataSource(DataSourceInterface):
    """Tushare数据源"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.api = None
        self.rate_limit_delay = 0.2  # API调用间隔
        self.last_call_time = 0
    
    def connect(self) -> bool:
        """连接Tushare API"""
        try:
            if not self.config.api_key:
                logger.error("Tushare API token未配置")
                return False
            
            # 尝试导入tushare
            try:
                import tushare as ts
                self.api = ts
                ts.set_token(self.config.api_key)
                self.pro = ts.pro_api()
            except ImportError:
                logger.error("tushare库未安装，请运行: pip install tushare")
                return False
            
            # 测试连接
            test_data = self.pro.daily(ts_code='000001.SZ', limit=1)
            if test_data is not None and not test_data.empty:
                self.is_connected = True
                logger.info("Tushare API连接成功")
                return True
            else:
                logger.error("Tushare API测试失败")
                return False
                
        except Exception as e:
            logger.error(f"Tushare API连接失败: {e}")
            self._handle_error(e)
            return False
    
    def disconnect(self) -> bool:
        """断开连接"""
        self.is_connected = False
        self.api = None
        self.pro = None
        logger.info("Tushare API已断开连接")
        return True
    
    def _rate_limit(self):
        """API调用频率限制"""
        current_time = time.time()
        time_since_last_call = current_time - self.last_call_time
        if time_since_last_call < self.rate_limit_delay:
            time.sleep(self.rate_limit_delay - time_since_last_call)
        self.last_call_time = time.time()
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        """获取实时数据"""
        if not self.is_connected:
            raise Exception("Tushare API未连接")
        
        result = {}
        
        for symbol in symbols:
            try:
                self._rate_limit()
                
                # 转换股票代码格式
                ts_code = self._convert_symbol_to_tushare(symbol)
                
                # 获取实时数据（使用最新日线数据模拟）
                df = self.pro.daily(ts_code=ts_code, limit=1)
                
                if not df.empty:
                    row = df.iloc[0]
                    data_point = MarketDataPoint(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        open=float(row['open']),
                        high=float(row['high']),
                        low=float(row['low']),
                        close=float(row['close']),
                        volume=int(row['vol'] * 100),  # 手转换为股
                        amount=float(row['amount'] * 1000),  # 千元转换为元
                        change=float(row['change']),
                        change_pct=float(row['pct_chg'] / 100),
                        source=self.config.name
                    )
                    result[symbol] = data_point
                    
            except Exception as e:
                logger.warning(f"获取{symbol}实时数据失败: {e}")
                self._handle_error(e)
        
        self.last_update = datetime.now()
        return result
    
    def get_historical_data(self, symbol: str, start_date: datetime, 
                          end_date: datetime, frequency: DataFrequency) -> pd.DataFrame:
        """获取历史数据"""
        if not self.is_connected:
            raise Exception("Tushare API未连接")
        
        try:
            self._rate_limit()
            
            ts_code = self._convert_symbol_to_tushare(symbol)
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            # 根据频率选择API
            if frequency == DataFrequency.DAILY:
                df = self.pro.daily(ts_code=ts_code, start_date=start_str, end_date=end_str)
            elif frequency in [DataFrequency.MINUTE, DataFrequency.MINUTE_5]:
                # 分钟数据需要特殊处理
                df = self.pro.stk_mins(ts_code=ts_code, start_date=start_str, end_date=end_str)
            else:
                # 默认使用日线数据
                df = self.pro.daily(ts_code=ts_code, start_date=start_str, end_date=end_str)
            
            if df.empty:
                return pd.DataFrame()
            
            # 数据预处理
            df = self._preprocess_tushare_data(df, symbol)
            return df
            
        except Exception as e:
            logger.error(f"获取{symbol}历史数据失败: {e}")
            self._handle_error(e)
            return pd.DataFrame()
    
    def _convert_symbol_to_tushare(self, symbol: str) -> str:
        """转换股票代码为Tushare格式"""
        if '.' in symbol:
            return symbol
        
        # 简单的代码转换逻辑
        if symbol.startswith('6'):
            return f"{symbol}.SH"
        elif symbol.startswith(('0', '3')):
            return f"{symbol}.SZ"
        else:
            return symbol
    
    def _preprocess_tushare_data(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """预处理Tushare数据"""
        # 重命名列
        column_mapping = {
            'trade_date': 'timestamp',
            'ts_code': 'symbol',
            'vol': 'volume',
            'pct_chg': 'change_pct'
        }
        df = df.rename(columns=column_mapping)
        
        # 处理时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 处理数值
        df['volume'] = df['volume'] * 100  # 手转股
        df['amount'] = df['amount'] * 1000  # 千元转元
        df['change_pct'] = df['change_pct'] / 100  # 百分比转小数
        
        # 添加symbol列
        df['symbol'] = symbol
        
        # 排序
        df = df.sort_values('timestamp')
        
        return df
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        if not self.is_connected:
            return {}
        
        try:
            self._rate_limit()
            ts_code = self._convert_symbol_to_tushare(symbol)
            
            # 获取股票基本信息
            df = self.pro.stock_basic(ts_code=ts_code)
            
            if not df.empty:
                row = df.iloc[0]
                return {
                    "symbol": symbol,
                    "name": row.get('name', ''),
                    "industry": row.get('industry', ''),
                    "market": row.get('market', ''),
                    "list_date": row.get('list_date', ''),
                    "source": self.config.name
                }
            
        except Exception as e:
            logger.warning(f"获取{symbol}信息失败: {e}")
        
        return {}
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码"""
        try:
            info = self.get_symbol_info(symbol)
            return bool(info)
        except:
            return False


class YahooFinanceDataSource(DataSourceInterface):
    """Yahoo Finance数据源"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.session = requests.Session()
    
    def connect(self) -> bool:
        """连接Yahoo Finance API"""
        try:
            # 尝试导入yfinance
            try:
                import yfinance as yf
                self.yf = yf
            except ImportError:
                logger.error("yfinance库未安装，请运行: pip install yfinance")
                return False
            
            # 测试连接
            test_ticker = self.yf.Ticker("AAPL")
            test_data = test_ticker.history(period="1d")
            
            if not test_data.empty:
                self.is_connected = True
                logger.info("Yahoo Finance API连接成功")
                return True
            else:
                logger.error("Yahoo Finance API测试失败")
                return False
                
        except Exception as e:
            logger.error(f"Yahoo Finance API连接失败: {e}")
            self._handle_error(e)
            return False
    
    def disconnect(self) -> bool:
        """断开连接"""
        self.is_connected = False
        self.yf = None
        logger.info("Yahoo Finance API已断开连接")
        return True
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        """获取实时数据"""
        if not self.is_connected:
            raise Exception("Yahoo Finance API未连接")
        
        result = {}
        
        for symbol in symbols:
            try:
                # 转换股票代码
                yf_symbol = self._convert_symbol_to_yahoo(symbol)
                ticker = self.yf.Ticker(yf_symbol)
                
                # 获取最新数据
                hist = ticker.history(period="1d", interval="1m")
                
                if not hist.empty:
                    row = hist.iloc[-1]
                    data_point = MarketDataPoint(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        open=float(row['Open']),
                        high=float(row['High']),
                        low=float(row['Low']),
                        close=float(row['Close']),
                        volume=int(row['Volume']),
                        amount=float(row['Close'] * row['Volume']),
                        change=0.0,  # 需要计算
                        change_pct=0.0,  # 需要计算
                        source=self.config.name
                    )
                    
                    # 计算涨跌幅
                    if len(hist) > 1:
                        prev_close = hist.iloc[-2]['Close']
                        data_point.change = data_point.close - prev_close
                        data_point.change_pct = data_point.change / prev_close
                    
                    result[symbol] = data_point
                    
            except Exception as e:
                logger.warning(f"获取{symbol}实时数据失败: {e}")
                self._handle_error(e)
        
        self.last_update = datetime.now()
        return result
    
    def get_historical_data(self, symbol: str, start_date: datetime, 
                          end_date: datetime, frequency: DataFrequency) -> pd.DataFrame:
        """获取历史数据"""
        if not self.is_connected:
            raise Exception("Yahoo Finance API未连接")
        
        try:
            yf_symbol = self._convert_symbol_to_yahoo(symbol)
            ticker = self.yf.Ticker(yf_symbol)
            
            # 频率映射
            interval_map = {
                DataFrequency.MINUTE: "1m",
                DataFrequency.MINUTE_5: "5m",
                DataFrequency.MINUTE_15: "15m",
                DataFrequency.MINUTE_30: "30m",
                DataFrequency.HOUR: "1h",
                DataFrequency.DAILY: "1d"
            }
            
            interval = interval_map.get(frequency, "1d")
            
            # 获取历史数据
            hist = ticker.history(start=start_date, end=end_date, interval=interval)
            
            if hist.empty:
                return pd.DataFrame()
            
            # 数据预处理
            df = self._preprocess_yahoo_data(hist, symbol)
            return df
            
        except Exception as e:
            logger.error(f"获取{symbol}历史数据失败: {e}")
            self._handle_error(e)
            return pd.DataFrame()
    
    def _convert_symbol_to_yahoo(self, symbol: str) -> str:
        """转换股票代码为Yahoo Finance格式"""
        # A股代码转换
        if symbol.endswith('.SZ'):
            code = symbol.replace('.SZ', '')
            return f"{code}.SZ"
        elif symbol.endswith('.SH'):
            code = symbol.replace('.SH', '')
            return f"{code}.SS"
        
        # 美股代码直接返回
        return symbol
    
    def _preprocess_yahoo_data(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """预处理Yahoo Finance数据"""
        # 重置索引，将时间戳作为列
        df = df.reset_index()
        
        # 重命名列
        column_mapping = {
            'Date': 'timestamp',
            'Datetime': 'timestamp',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume'
        }
        df = df.rename(columns=column_mapping)
        
        # 添加symbol列
        df['symbol'] = symbol
        
        # 计算amount
        df['amount'] = df['close'] * df['volume']
        
        # 计算涨跌幅
        df['change'] = df['close'].diff()
        df['change_pct'] = df['close'].pct_change()
        
        # 填充NaN值
        df = df.fillna(0)
        
        return df
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        if not self.is_connected:
            return {}
        
        try:
            yf_symbol = self._convert_symbol_to_yahoo(symbol)
            ticker = self.yf.Ticker(yf_symbol)
            info = ticker.info
            
            return {
                "symbol": symbol,
                "name": info.get('longName', info.get('shortName', '')),
                "sector": info.get('sector', ''),
                "industry": info.get('industry', ''),
                "market_cap": info.get('marketCap', 0),
                "currency": info.get('currency', 'USD'),
                "source": self.config.name
            }
            
        except Exception as e:
            logger.warning(f"获取{symbol}信息失败: {e}")
            return {}
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码"""
        try:
            yf_symbol = self._convert_symbol_to_yahoo(symbol)
            ticker = self.yf.Ticker(yf_symbol)
            hist = ticker.history(period="1d")
            return not hist.empty
        except:
            return False


class APIDataSourceFactory:
    """API数据源工厂"""
    
    @staticmethod
    def create_tushare_source(api_key: str, name: str = "Tushare") -> TushareDataSource:
        """创建Tushare数据源"""
        config = DataSourceConfig(
            source_type="api_tushare",
            name=name,
            api_key=api_key,
            timeout=30,
            retry_count=3
        )
        return TushareDataSource(config)
    
    @staticmethod
    def create_yahoo_source(name: str = "YahooFinance") -> YahooFinanceDataSource:
        """创建Yahoo Finance数据源"""
        config = DataSourceConfig(
            source_type="api_yfinance",
            name=name,
            timeout=30,
            retry_count=3
        )
        return YahooFinanceDataSource(config)
    
    @staticmethod
    def create_from_config(config: DataSourceConfig) -> DataSourceInterface:
        """根据配置创建数据源"""
        if config.source_type == "api_tushare":
            return TushareDataSource(config)
        elif config.source_type == "api_yfinance":
            return YahooFinanceDataSource(config)
        else:
            raise ValueError(f"不支持的API数据源类型: {config.source_type}")
