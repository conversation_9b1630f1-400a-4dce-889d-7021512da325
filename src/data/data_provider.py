"""
实时数据提供模块
支持多种数据源：tushare、akshare、yfinance等
实现实时行业数据输入和处理
"""

import pandas as pd
import numpy as np
import asyncio
import websocket
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import tushare as ts
import akshare as ak
import yfinance as yf
from loguru import logger


@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float
    prev_close: float = None
    change: float = None
    pct_change: float = None


@dataclass
class IndustryData:
    """行业数据结构"""
    industry_code: str
    industry_name: str
    timestamp: datetime
    stocks: List[str]
    avg_price: float
    total_volume: int
    total_amount: float
    rise_count: int
    fall_count: int
    flat_count: int
    avg_pct_change: float


class DataProvider(ABC):
    """数据提供者基类"""
    
    @abstractmethod
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """获取实时数据"""
        pass
    
    @abstractmethod
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取历史数据"""
        pass
    
    @abstractmethod
    def get_industry_data(self, industry_code: str = None) -> List[IndustryData]:
        """获取行业数据"""
        pass


class TushareProvider(DataProvider):
    """Tushare数据提供者"""
    
    def __init__(self, token: str):
        self.token = token
        ts.set_token(token)
        self.pro = ts.pro_api()
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """获取实时数据"""
        try:
            # 转换股票代码格式
            ts_symbols = [self._convert_symbol_to_ts(symbol) for symbol in symbols]
            
            # 获取实时数据
            df = ts.get_realtime_quotes(ts_symbols)
            
            result = {}
            for _, row in df.iterrows():
                symbol = self._convert_symbol_from_ts(row['code'])
                result[symbol] = MarketData(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    open=float(row['open']),
                    high=float(row['high']),
                    low=float(row['low']),
                    close=float(row['price']),
                    volume=int(row['volume']),
                    amount=float(row['amount']),
                    prev_close=float(row['pre_close']),
                    change=float(row['price']) - float(row['pre_close']),
                    pct_change=float(row['changepercent'])
                )
            
            return result
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return {}
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取历史数据"""
        try:
            ts_symbol = self._convert_symbol_to_ts(symbol)
            df = self.pro.daily(ts_code=ts_symbol, start_date=start_date.replace('-', ''), 
                               end_date=end_date.replace('-', ''))
            
            # 数据格式转换
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date')
            df.reset_index(drop=True, inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_industry_data(self, industry_code: str = None) -> List[IndustryData]:
        """获取行业数据"""
        try:
            # 获取行业分类
            industry_df = self.pro.index_classify(level='L1', src='SW2021')
            
            result = []
            for _, industry in industry_df.iterrows():
                # 获取行业成分股
                stocks_df = self.pro.index_member(index_code=industry['index_code'])
                
                if not stocks_df.empty:
                    # 获取成分股实时数据
                    symbols = stocks_df['con_code'].tolist()[:10]  # 限制数量避免API限制
                    realtime_data = self.get_realtime_data(symbols)
                    
                    if realtime_data:
                        # 计算行业统计数据
                        prices = [data.close for data in realtime_data.values()]
                        volumes = [data.volume for data in realtime_data.values()]
                        amounts = [data.amount for data in realtime_data.values()]
                        pct_changes = [data.pct_change for data in realtime_data.values() if data.pct_change]
                        
                        rise_count = sum(1 for pct in pct_changes if pct > 0)
                        fall_count = sum(1 for pct in pct_changes if pct < 0)
                        flat_count = len(pct_changes) - rise_count - fall_count
                        
                        industry_data = IndustryData(
                            industry_code=industry['index_code'],
                            industry_name=industry['index_name'],
                            timestamp=datetime.now(),
                            stocks=list(realtime_data.keys()),
                            avg_price=np.mean(prices) if prices else 0,
                            total_volume=sum(volumes),
                            total_amount=sum(amounts),
                            rise_count=rise_count,
                            fall_count=fall_count,
                            flat_count=flat_count,
                            avg_pct_change=np.mean(pct_changes) if pct_changes else 0
                        )
                        
                        result.append(industry_data)
            
            return result
            
        except Exception as e:
            logger.error(f"获取行业数据失败: {e}")
            return []
    
    def _convert_symbol_to_ts(self, symbol: str) -> str:
        """转换股票代码为tushare格式"""
        if '.' in symbol:
            code, market = symbol.split('.')
            if market.upper() == 'SZ':
                return f"{code}.SZ"
            elif market.upper() == 'SH':
                return f"{code}.SH"
        return symbol
    
    def _convert_symbol_from_ts(self, ts_symbol: str) -> str:
        """从tushare格式转换股票代码"""
        return ts_symbol


class AkshareProvider(DataProvider):
    """Akshare数据提供者"""
    
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """获取实时数据"""
        try:
            result = {}
            
            for symbol in symbols:
                # 获取实时行情
                code = symbol.split('.')[0] if '.' in symbol else symbol
                df = ak.stock_zh_a_spot_em()
                
                stock_data = df[df['代码'] == code]
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    result[symbol] = MarketData(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        open=float(row['今开']),
                        high=float(row['最高']),
                        low=float(row['最低']),
                        close=float(row['最新价']),
                        volume=int(row['成交量']),
                        amount=float(row['成交额']),
                        prev_close=float(row['昨收']),
                        change=float(row['涨跌额']),
                        pct_change=float(row['涨跌幅'])
                    )
            
            return result
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return {}
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取历史数据"""
        try:
            code = symbol.split('.')[0] if '.' in symbol else symbol
            df = ak.stock_zh_a_hist(symbol=code, period="daily", 
                                   start_date=start_date.replace('-', ''), 
                                   end_date=end_date.replace('-', ''))
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_industry_data(self, industry_code: str = None) -> List[IndustryData]:
        """获取行业数据"""
        try:
            # 获取行业板块数据
            df = ak.stock_board_industry_name_em()
            
            result = []
            for _, row in df.iterrows():
                # 获取板块成分股
                stocks_df = ak.stock_board_industry_cons_em(symbol=row['板块名称'])
                
                if not stocks_df.empty:
                    symbols = [f"{code}.SZ" if code.startswith(('00', '30')) else f"{code}.SH" 
                              for code in stocks_df['代码'].tolist()[:10]]
                    
                    # 计算行业统计
                    industry_data = IndustryData(
                        industry_code=row['板块代码'] if '板块代码' in row else row['板块名称'],
                        industry_name=row['板块名称'],
                        timestamp=datetime.now(),
                        stocks=symbols,
                        avg_price=0,  # 需要进一步计算
                        total_volume=0,
                        total_amount=0,
                        rise_count=0,
                        fall_count=0,
                        flat_count=0,
                        avg_pct_change=0
                    )
                    
                    result.append(industry_data)
            
            return result
            
        except Exception as e:
            logger.error(f"获取行业数据失败: {e}")
            return []


class RealTimeDataStream:
    """实时数据流"""
    
    def __init__(self, provider: DataProvider):
        self.provider = provider
        self.subscribers = {}  # {symbol: [callback_functions]}
        self.industry_subscribers = []  # [callback_functions]
        self.is_running = False
        self.update_interval = 1  # 更新间隔（秒）
        
    def subscribe(self, symbol: str, callback: Callable[[MarketData], None]):
        """订阅股票数据"""
        if symbol not in self.subscribers:
            self.subscribers[symbol] = []
        self.subscribers[symbol].append(callback)
    
    def subscribe_industry(self, callback: Callable[[List[IndustryData]], None]):
        """订阅行业数据"""
        self.industry_subscribers.append(callback)
    
    def start(self):
        """启动数据流"""
        self.is_running = True
        
        # 启动股票数据更新线程
        if self.subscribers:
            threading.Thread(target=self._update_stock_data, daemon=True).start()
        
        # 启动行业数据更新线程
        if self.industry_subscribers:
            threading.Thread(target=self._update_industry_data, daemon=True).start()
    
    def stop(self):
        """停止数据流"""
        self.is_running = False
    
    def _update_stock_data(self):
        """更新股票数据"""
        while self.is_running:
            try:
                symbols = list(self.subscribers.keys())
                if symbols:
                    data = self.provider.get_realtime_data(symbols)
                    
                    for symbol, market_data in data.items():
                        if symbol in self.subscribers:
                            for callback in self.subscribers[symbol]:
                                try:
                                    callback(market_data)
                                except Exception as e:
                                    logger.error(f"回调函数执行失败: {e}")
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"更新股票数据失败: {e}")
                time.sleep(self.update_interval)
    
    def _update_industry_data(self):
        """更新行业数据"""
        while self.is_running:
            try:
                industry_data = self.provider.get_industry_data()
                
                for callback in self.industry_subscribers:
                    try:
                        callback(industry_data)
                    except Exception as e:
                        logger.error(f"行业数据回调失败: {e}")
                
                time.sleep(30)  # 行业数据更新频率较低
                
            except Exception as e:
                logger.error(f"更新行业数据失败: {e}")
                time.sleep(30)


class DataCache:
    """数据缓存"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.stock_cache = {}  # {symbol: [MarketData]}
        self.industry_cache = []  # [IndustryData]
    
    def add_stock_data(self, symbol: str, data: MarketData):
        """添加股票数据到缓存"""
        if symbol not in self.stock_cache:
            self.stock_cache[symbol] = []
        
        self.stock_cache[symbol].append(data)
        
        # 限制缓存大小
        if len(self.stock_cache[symbol]) > self.max_size:
            self.stock_cache[symbol] = self.stock_cache[symbol][-self.max_size:]
    
    def add_industry_data(self, data: List[IndustryData]):
        """添加行业数据到缓存"""
        self.industry_cache.extend(data)
        
        # 限制缓存大小
        if len(self.industry_cache) > self.max_size:
            self.industry_cache = self.industry_cache[-self.max_size:]
    
    def get_stock_data(self, symbol: str, limit: int = None) -> List[MarketData]:
        """获取股票数据"""
        data = self.stock_cache.get(symbol, [])
        return data[-limit:] if limit else data
    
    def get_industry_data(self, limit: int = None) -> List[IndustryData]:
        """获取行业数据"""
        return self.industry_cache[-limit:] if limit else self.industry_cache


class DataManager:
    """数据管理器"""
    
    def __init__(self, provider: DataProvider):
        self.provider = provider
        self.cache = DataCache()
        self.stream = RealTimeDataStream(provider)
        
        # 设置数据流回调
        self.stream.subscribe_industry(self._on_industry_data)
    
    def subscribe_stock(self, symbol: str, callback: Callable[[MarketData], None]):
        """订阅股票数据"""
        # 添加到缓存的回调
        def cache_callback(data: MarketData):
            self.cache.add_stock_data(symbol, data)
            callback(data)
        
        self.stream.subscribe(symbol, cache_callback)
    
    def _on_industry_data(self, data: List[IndustryData]):
        """行业数据回调"""
        self.cache.add_industry_data(data)
    
    def start(self):
        """启动数据管理器"""
        self.stream.start()
    
    def stop(self):
        """停止数据管理器"""
        self.stream.stop()
    
    def get_cached_data(self, symbol: str, limit: int = None) -> List[MarketData]:
        """获取缓存数据"""
        return self.cache.get_stock_data(symbol, limit)
    
    def get_industry_data(self, limit: int = None) -> List[IndustryData]:
        """获取行业数据"""
        return self.cache.get_industry_data(limit)


if __name__ == "__main__":
    # 示例使用
    # provider = TushareProvider("your_token_here")
    provider = AkshareProvider()
    
    # 获取实时数据
    symbols = ["000001.SZ", "000002.SZ"]
    realtime_data = provider.get_realtime_data(symbols)
    
    for symbol, data in realtime_data.items():
        print(f"{symbol}: 价格={data.close}, 涨跌幅={data.pct_change}%")
    
    # 获取行业数据
    industry_data = provider.get_industry_data()
    for industry in industry_data[:3]:  # 显示前3个行业
        print(f"行业: {industry.industry_name}, 平均涨跌幅: {industry.avg_pct_change:.2f}%")
