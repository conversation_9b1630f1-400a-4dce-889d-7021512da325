"""
数据源接口定义
定义统一的数据接入接口，支持多种数据源类型
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import pandas as pd


class DataSourceType(Enum):
    """数据源类型"""
    MOCK = "mock"                    # 模拟数据
    CSV_FILE = "csv_file"           # CSV文件
    API_TUSHARE = "api_tushare"     # Tushare API
    API_YFINANCE = "api_yfinance"   # Yahoo Finance API
    API_AKSHARE = "api_akshare"     # AKShare API
    API_ALPHA_VANTAGE = "api_alpha_vantage"  # Alpha Vantage API
    API_CUSTOM = "api_custom"       # 自定义API
    WEBSOCKET = "websocket"         # WebSocket实时数据
    DATABASE = "database"           # 数据库


class DataFrequency(Enum):
    """数据频率"""
    TICK = "tick"           # 逐笔数据
    SECOND = "1s"           # 秒级数据
    MINUTE = "1min"         # 分钟数据
    MINUTE_5 = "5min"       # 5分钟数据
    MINUTE_15 = "15min"     # 15分钟数据
    MINUTE_30 = "30min"     # 30分钟数据
    HOUR = "1h"             # 小时数据
    DAILY = "1d"            # 日线数据
    WEEKLY = "1w"           # 周线数据
    MONTHLY = "1m"          # 月线数据


@dataclass
class MarketDataPoint:
    """市场数据点"""
    symbol: str                     # 股票代码
    timestamp: datetime             # 时间戳
    open: float                     # 开盘价
    high: float                     # 最高价
    low: float                      # 最低价
    close: float                    # 收盘价
    volume: int                     # 成交量
    amount: float                   # 成交额
    change: float                   # 涨跌幅
    change_pct: float              # 涨跌幅百分比
    
    # 可选字段
    bid: Optional[float] = None     # 买一价
    ask: Optional[float] = None     # 卖一价
    bid_volume: Optional[int] = None # 买一量
    ask_volume: Optional[int] = None # 卖一量
    turnover_rate: Optional[float] = None  # 换手率
    pe_ratio: Optional[float] = None       # 市盈率
    pb_ratio: Optional[float] = None       # 市净率
    market_cap: Optional[float] = None     # 市值
    
    # 技术指标
    ma5: Optional[float] = None     # 5日均线
    ma10: Optional[float] = None    # 10日均线
    ma20: Optional[float] = None    # 20日均线
    rsi: Optional[float] = None     # RSI指标
    macd: Optional[float] = None    # MACD指标
    
    # 元数据
    source: Optional[str] = None    # 数据源
    quality: Optional[str] = None   # 数据质量
    delay: Optional[int] = None     # 延迟(毫秒)


@dataclass
class DataSourceConfig:
    """数据源配置"""
    source_type: DataSourceType
    name: str
    enabled: bool = True
    priority: int = 1               # 优先级，数字越小优先级越高
    
    # 连接配置
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    base_url: Optional[str] = None
    timeout: int = 10
    retry_count: int = 3
    retry_delay: float = 1.0
    
    # 数据配置
    symbols: List[str] = None
    frequency: DataFrequency = DataFrequency.MINUTE
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    
    # 文件配置 (CSV等)
    file_path: Optional[str] = None
    encoding: str = "utf-8"
    delimiter: str = ","
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: int = 60             # 缓存时间(秒)
    
    # 其他配置
    extra_params: Dict[str, Any] = None


class DataSourceInterface(ABC):
    """数据源接口基类"""
    
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.is_connected = False
        self.last_update = None
        self.error_count = 0
        self.max_errors = 10
    
    @abstractmethod
    def connect(self) -> bool:
        """连接数据源"""
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """断开连接"""
        pass
    
    @abstractmethod
    def get_realtime_data(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        """获取实时数据"""
        pass
    
    @abstractmethod
    def get_historical_data(self, symbol: str, start_date: datetime, 
                          end_date: datetime, frequency: DataFrequency) -> pd.DataFrame:
        """获取历史数据"""
        pass
    
    @abstractmethod
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        pass
    
    @abstractmethod
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码"""
        pass
    
    def is_available(self) -> bool:
        """检查数据源是否可用"""
        return self.is_connected and self.error_count < self.max_errors
    
    def get_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        return {
            "name": self.config.name,
            "type": self.config.source_type.value,
            "connected": self.is_connected,
            "available": self.is_available(),
            "last_update": self.last_update,
            "error_count": self.error_count,
            "priority": self.config.priority
        }
    
    def reset_errors(self):
        """重置错误计数"""
        self.error_count = 0
    
    def _handle_error(self, error: Exception):
        """处理错误"""
        self.error_count += 1
        if self.error_count >= self.max_errors:
            self.is_connected = False


@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    completeness: float         # 完整性 (0-1)
    timeliness: float          # 及时性 (0-1)
    accuracy: float            # 准确性 (0-1)
    consistency: float         # 一致性 (0-1)
    overall_score: float       # 综合评分 (0-1)
    
    missing_fields: List[str] = None
    delayed_seconds: int = 0
    anomaly_count: int = 0
    last_check: datetime = None


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_market_data(data: MarketDataPoint) -> DataQualityMetrics:
        """验证市场数据质量"""
        issues = []
        
        # 检查必需字段
        required_fields = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_fields = []
        
        for field in required_fields:
            if getattr(data, field) is None:
                missing_fields.append(field)
        
        # 计算完整性
        completeness = 1.0 - (len(missing_fields) / len(required_fields))
        
        # 检查价格逻辑
        accuracy = 1.0
        if data.high < data.low:
            accuracy -= 0.2
        if data.open < 0 or data.close < 0:
            accuracy -= 0.3
        if data.volume < 0:
            accuracy -= 0.2
        
        # 检查时效性
        now = datetime.now()
        delay_seconds = (now - data.timestamp).total_seconds()
        timeliness = max(0, 1.0 - delay_seconds / 300)  # 5分钟内为满分
        
        # 一致性检查
        consistency = 1.0
        if abs(data.change_pct - (data.close - data.open) / data.open) > 0.01:
            consistency -= 0.3
        
        # 综合评分
        overall_score = (completeness * 0.3 + accuracy * 0.3 + 
                        timeliness * 0.2 + consistency * 0.2)
        
        return DataQualityMetrics(
            completeness=completeness,
            timeliness=timeliness,
            accuracy=accuracy,
            consistency=consistency,
            overall_score=overall_score,
            missing_fields=missing_fields,
            delayed_seconds=int(delay_seconds),
            last_check=now
        )


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.data_sources: Dict[str, DataSourceInterface] = {}
        self.primary_source: Optional[str] = None
        self.fallback_sources: List[str] = []
        self.quality_threshold = 0.7
    
    def register_source(self, name: str, source: DataSourceInterface):
        """注册数据源"""
        self.data_sources[name] = source
        
        # 按优先级排序
        if not self.primary_source or source.config.priority < self.data_sources[self.primary_source].config.priority:
            if self.primary_source:
                self.fallback_sources.insert(0, self.primary_source)
            self.primary_source = name
        else:
            self.fallback_sources.append(name)
    
    def get_best_source(self) -> Optional[DataSourceInterface]:
        """获取最佳可用数据源"""
        # 检查主数据源
        if self.primary_source and self.data_sources[self.primary_source].is_available():
            return self.data_sources[self.primary_source]
        
        # 检查备用数据源
        for source_name in self.fallback_sources:
            if self.data_sources[source_name].is_available():
                return self.data_sources[source_name]
        
        return None
    
    def get_realtime_data_with_fallback(self, symbols: List[str]) -> Dict[str, MarketDataPoint]:
        """获取实时数据（带故障转移）"""
        source = self.get_best_source()
        if not source:
            raise Exception("没有可用的数据源")
        
        try:
            return source.get_realtime_data(symbols)
        except Exception as e:
            source._handle_error(e)
            
            # 尝试下一个数据源
            next_source = self.get_best_source()
            if next_source and next_source != source:
                return next_source.get_realtime_data(symbols)
            else:
                raise e
    
    def get_all_sources_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有数据源状态"""
        return {name: source.get_status() for name, source in self.data_sources.items()}


# 数据转换工具
class DataConverter:
    """数据转换工具"""
    
    @staticmethod
    def dict_to_market_data(data: Dict[str, Any], symbol: str) -> MarketDataPoint:
        """字典转换为MarketDataPoint"""
        return MarketDataPoint(
            symbol=symbol,
            timestamp=data.get('timestamp', datetime.now()),
            open=float(data.get('open', 0)),
            high=float(data.get('high', 0)),
            low=float(data.get('low', 0)),
            close=float(data.get('close', 0)),
            volume=int(data.get('volume', 0)),
            amount=float(data.get('amount', 0)),
            change=float(data.get('change', 0)),
            change_pct=float(data.get('change_pct', 0)),
            bid=data.get('bid'),
            ask=data.get('ask'),
            source=data.get('source')
        )
    
    @staticmethod
    def dataframe_to_market_data(df: pd.DataFrame, symbol: str) -> List[MarketDataPoint]:
        """DataFrame转换为MarketDataPoint列表"""
        result = []
        for _, row in df.iterrows():
            data_point = MarketDataPoint(
                symbol=symbol,
                timestamp=pd.to_datetime(row.get('timestamp', row.name)),
                open=float(row.get('open', 0)),
                high=float(row.get('high', 0)),
                low=float(row.get('low', 0)),
                close=float(row.get('close', 0)),
                volume=int(row.get('volume', 0)),
                amount=float(row.get('amount', 0)),
                change=float(row.get('change', 0)),
                change_pct=float(row.get('change_pct', 0))
            )
            result.append(data_point)
        return result
