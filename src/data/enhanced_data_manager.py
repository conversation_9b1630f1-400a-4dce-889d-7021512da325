"""
增强的数据管理器
统一管理多种数据源，支持API和CSV文件数据接入
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from loguru import logger
import threading
import time
import json

from .data_source_interface import (
    DataSourceInterface, DataSourceManager, MarketDataPoint, 
    DataFrequency, DataValidator, DataQualityMetrics, DataSourceConfig
)
from .csv_data_source import CSVDataSource, CSVDataSourceFactory
from .api_data_source import APIDataSourceFactory


@dataclass
class DataStreamConfig:
    """数据流配置"""
    enabled: bool = True
    update_interval: int = 2  # 秒
    symbols: List[str] = None
    auto_start: bool = False
    quality_check: bool = True
    cache_enabled: bool = True
    cache_ttl: int = 60  # 秒


class EnhancedDataManager:
    """增强的数据管理器"""
    
    def __init__(self, config_file: str = None):
        self.source_manager = DataSourceManager()
        self.config = self._load_config(config_file)
        
        # 数据缓存
        self.data_cache: Dict[str, MarketDataPoint] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        self.cache_ttl = self.config.get('cache_ttl', 60)
        
        # 数据质量监控
        self.quality_metrics: Dict[str, DataQualityMetrics] = {}
        self.quality_threshold = self.config.get('quality_threshold', 0.7)
        
        # 实时数据流
        self.stream_config = DataStreamConfig(**self.config.get('stream', {}))
        self.streaming_active = False
        self.streaming_thread = None
        self.data_subscribers: List[Callable] = []
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'api_calls': 0,
            'errors': 0,
            'last_update': None
        }
        
        # 初始化数据源
        self._initialize_data_sources()
        
        logger.info("增强数据管理器初始化完成")
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            'cache_ttl': 60,
            'quality_threshold': 0.7,
            'stream': {
                'enabled': True,
                'update_interval': 2,
                'auto_start': False
            },
            'sources': {
                'mock': {'enabled': True, 'priority': 3},
                'csv': {'enabled': False, 'files': []},
                'api': {
                    'tushare': {'enabled': False, 'token': ''},
                    'yahoo': {'enabled': False}
                }
            }
        }
        
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                default_config.update(file_config)
                logger.info(f"加载配置文件: {config_file}")
            except Exception as e:
                logger.warning(f"加载配置文件失败: {e}，使用默认配置")
        
        return default_config
    
    def _initialize_data_sources(self):
        """初始化数据源"""
        sources_config = self.config.get('sources', {})
        
        # 1. 模拟数据源
        if sources_config.get('mock', {}).get('enabled', True):
            self._init_mock_source()
        
        # 2. CSV数据源
        if sources_config.get('csv', {}).get('enabled', False):
            self._init_csv_sources()
        
        # 3. API数据源
        if sources_config.get('api', {}).get('enabled', False):
            self._init_api_sources()
        
        logger.info(f"初始化完成，共{len(self.source_manager.data_sources)}个数据源")
    
    def _init_mock_source(self):
        """初始化模拟数据源"""
        try:
            from .mock_data_source import MockDataSource
            
            mock_source = MockDataSource()
            self.source_manager.register_source("mock", mock_source)
            mock_source.connect()
            logger.info("模拟数据源已连接")
            
        except ImportError:
            logger.warning("模拟数据源不可用")
    
    def _init_csv_sources(self):
        """初始化CSV数据源"""
        csv_config = self.config.get('sources', {}).get('csv', {})
        csv_files = csv_config.get('files', [])
        
        for file_config in csv_files:
            try:
                file_path = file_config.get('path')
                if not file_path or not os.path.exists(file_path):
                    continue
                
                name = file_config.get('name', f"CSV_{os.path.basename(file_path)}")
                symbols = file_config.get('symbols', [])
                
                csv_source = CSVDataSourceFactory.create_from_file(
                    file_path=file_path,
                    symbols=symbols,
                    name=name
                )
                
                self.source_manager.register_source(name, csv_source)
                if csv_source.connect():
                    logger.info(f"CSV数据源已连接: {name}")
                    
                    # 启动模拟（如果配置了）
                    if file_config.get('simulate', False):
                        speed = file_config.get('speed', 1.0)
                        csv_source.start_simulation(speed)
                        
            except Exception as e:
                logger.error(f"初始化CSV数据源失败: {e}")
    
    def _init_api_sources(self):
        """初始化API数据源"""
        api_config = self.config.get('sources', {}).get('api', {})
        
        # Tushare
        tushare_config = api_config.get('tushare', {})
        if tushare_config.get('enabled', False):
            token = tushare_config.get('token')
            if token:
                try:
                    tushare_source = APIDataSourceFactory.create_tushare_source(token)
                    self.source_manager.register_source("tushare", tushare_source)
                    if tushare_source.connect():
                        logger.info("Tushare数据源已连接")
                except Exception as e:
                    logger.error(f"Tushare数据源连接失败: {e}")
        
        # Yahoo Finance
        yahoo_config = api_config.get('yahoo', {})
        if yahoo_config.get('enabled', False):
            try:
                yahoo_source = APIDataSourceFactory.create_yahoo_source()
                self.source_manager.register_source("yahoo", yahoo_source)
                if yahoo_source.connect():
                    logger.info("Yahoo Finance数据源已连接")
            except Exception as e:
                logger.error(f"Yahoo Finance数据源连接失败: {e}")
    
    def get_realtime_data(self, symbols: List[str], use_cache: bool = True) -> Dict[str, MarketDataPoint]:
        """获取实时数据"""
        self.stats['total_requests'] += 1
        result = {}
        
        # 检查缓存
        if use_cache:
            cached_data, missing_symbols = self._get_cached_data(symbols)
            result.update(cached_data)
            symbols = missing_symbols
        
        # 获取缺失的数据
        if symbols:
            try:
                fresh_data = self.source_manager.get_realtime_data_with_fallback(symbols)
                self.stats['api_calls'] += 1
                
                # 数据质量检查
                if self.stream_config.quality_check:
                    fresh_data = self._validate_data_quality(fresh_data)
                
                # 更新缓存
                if use_cache:
                    self._update_cache(fresh_data)
                
                result.update(fresh_data)
                
            except Exception as e:
                logger.error(f"获取实时数据失败: {e}")
                self.stats['errors'] += 1
                raise
        
        self.stats['last_update'] = datetime.now()
        return result
    
    def _get_cached_data(self, symbols: List[str]) -> tuple:
        """获取缓存数据"""
        cached_data = {}
        missing_symbols = []
        current_time = datetime.now()
        
        for symbol in symbols:
            if symbol in self.data_cache:
                cache_time = self.cache_timestamps.get(symbol)
                if cache_time and (current_time - cache_time).seconds < self.cache_ttl:
                    cached_data[symbol] = self.data_cache[symbol]
                    self.stats['cache_hits'] += 1
                else:
                    missing_symbols.append(symbol)
            else:
                missing_symbols.append(symbol)
        
        return cached_data, missing_symbols
    
    def _update_cache(self, data: Dict[str, MarketDataPoint]):
        """更新缓存"""
        current_time = datetime.now()
        for symbol, data_point in data.items():
            self.data_cache[symbol] = data_point
            self.cache_timestamps[symbol] = current_time
    
    def _validate_data_quality(self, data: Dict[str, MarketDataPoint]) -> Dict[str, MarketDataPoint]:
        """验证数据质量"""
        validated_data = {}
        
        for symbol, data_point in data.items():
            quality = DataValidator.validate_market_data(data_point)
            self.quality_metrics[symbol] = quality
            
            if quality.overall_score >= self.quality_threshold:
                validated_data[symbol] = data_point
            else:
                logger.warning(f"{symbol}数据质量不达标: {quality.overall_score:.2f}")
        
        return validated_data
    
    def get_historical_data(self, symbol: str, start_date: datetime, 
                          end_date: datetime, frequency: DataFrequency = DataFrequency.DAILY) -> pd.DataFrame:
        """获取历史数据"""
        try:
            source = self.source_manager.get_best_source()
            if source:
                return source.get_historical_data(symbol, start_date, end_date, frequency)
            else:
                raise Exception("没有可用的数据源")
                
        except Exception as e:
            logger.error(f"获取{symbol}历史数据失败: {e}")
            return pd.DataFrame()
    
    def start_data_stream(self, symbols: List[str] = None):
        """启动数据流"""
        if self.streaming_active:
            logger.warning("数据流已在运行")
            return
        
        self.stream_symbols = symbols or self.stream_config.symbols or []
        if not self.stream_symbols:
            logger.error("未指定数据流股票列表")
            return
        
        self.streaming_active = True
        self.streaming_thread = threading.Thread(target=self._data_stream_loop)
        self.streaming_thread.daemon = True
        self.streaming_thread.start()
        
        logger.info(f"数据流已启动，监控{len(self.stream_symbols)}只股票")
    
    def stop_data_stream(self):
        """停止数据流"""
        self.streaming_active = False
        if self.streaming_thread and self.streaming_thread.is_alive():
            self.streaming_thread.join(timeout=5)
        
        logger.info("数据流已停止")
    
    def _data_stream_loop(self):
        """数据流循环"""
        while self.streaming_active:
            try:
                # 获取实时数据
                data = self.get_realtime_data(self.stream_symbols)
                
                # 通知订阅者
                for callback in self.data_subscribers:
                    try:
                        callback(data)
                    except Exception as e:
                        logger.error(f"数据流回调错误: {e}")
                
                time.sleep(self.stream_config.update_interval)
                
            except Exception as e:
                logger.error(f"数据流循环错误: {e}")
                time.sleep(self.stream_config.update_interval)
    
    def subscribe_data_updates(self, callback: Callable):
        """订阅数据更新"""
        self.data_subscribers.append(callback)
        logger.info("添加数据订阅者")
    
    def unsubscribe_data_updates(self, callback: Callable):
        """取消订阅"""
        if callback in self.data_subscribers:
            self.data_subscribers.remove(callback)
            logger.info("移除数据订阅者")
    
    def add_csv_data_source(self, file_path: str, symbols: List[str] = None, 
                           name: str = None, simulate: bool = False, speed: float = 1.0):
        """添加CSV数据源"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"CSV文件不存在: {file_path}")
            
            source_name = name or f"CSV_{os.path.basename(file_path)}"
            csv_source = CSVDataSourceFactory.create_from_file(file_path, symbols, source_name)
            
            self.source_manager.register_source(source_name, csv_source)
            
            if csv_source.connect():
                logger.info(f"CSV数据源已添加: {source_name}")
                
                if simulate:
                    csv_source.start_simulation(speed)
                    logger.info(f"CSV数据模拟已启动，速度: {speed}x")
                
                return source_name
            else:
                raise Exception("CSV数据源连接失败")
                
        except Exception as e:
            logger.error(f"添加CSV数据源失败: {e}")
            raise
    
    def add_api_data_source(self, source_type: str, **kwargs):
        """添加API数据源"""
        try:
            if source_type.lower() == 'tushare':
                api_key = kwargs.get('api_key') or kwargs.get('token')
                if not api_key:
                    raise ValueError("Tushare需要API token")
                
                source = APIDataSourceFactory.create_tushare_source(api_key)
                source_name = "tushare"
                
            elif source_type.lower() == 'yahoo':
                source = APIDataSourceFactory.create_yahoo_source()
                source_name = "yahoo"
                
            else:
                raise ValueError(f"不支持的API数据源类型: {source_type}")
            
            self.source_manager.register_source(source_name, source)
            
            if source.connect():
                logger.info(f"API数据源已添加: {source_name}")
                return source_name
            else:
                raise Exception(f"{source_type}数据源连接失败")
                
        except Exception as e:
            logger.error(f"添加API数据源失败: {e}")
            raise
    
    def get_data_sources_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        return self.source_manager.get_all_sources_status()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        cache_hit_rate = (self.stats['cache_hits'] / max(self.stats['total_requests'], 1)) * 100
        
        return {
            **self.stats,
            'cache_hit_rate': f"{cache_hit_rate:.1f}%",
            'data_sources_count': len(self.source_manager.data_sources),
            'cached_symbols': len(self.data_cache),
            'streaming_active': self.streaming_active,
            'subscribers_count': len(self.data_subscribers)
        }
    
    def get_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        if not self.quality_metrics:
            return {"message": "暂无数据质量信息"}
        
        scores = [q.overall_score for q in self.quality_metrics.values()]
        
        return {
            "total_symbols": len(self.quality_metrics),
            "average_quality": np.mean(scores),
            "min_quality": min(scores),
            "max_quality": max(scores),
            "quality_threshold": self.quality_threshold,
            "high_quality_count": sum(1 for s in scores if s >= 0.8),
            "medium_quality_count": sum(1 for s in scores if 0.6 <= s < 0.8),
            "low_quality_count": sum(1 for s in scores if s < 0.6),
            "last_check": max(q.last_check for q in self.quality_metrics.values())
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.data_cache.clear()
        self.cache_timestamps.clear()
        logger.info("数据缓存已清空")
    
    def shutdown(self):
        """关闭数据管理器"""
        # 停止数据流
        self.stop_data_stream()
        
        # 断开所有数据源
        for name, source in self.source_manager.data_sources.items():
            try:
                source.disconnect()
                logger.info(f"数据源已断开: {name}")
            except Exception as e:
                logger.error(f"断开数据源失败 {name}: {e}")
        
        # 清空缓存
        self.clear_cache()
        
        logger.info("数据管理器已关闭")
