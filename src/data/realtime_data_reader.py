"""
实时数据文件读取模块
支持从外部文件读取行情、持仓和资金数据
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob
import json
from typing import Dict, List, Optional, Tuple
import logging

class RealTimeDataReader:
    """实时数据文件读取器"""
    
    def __init__(self, config_path: str = "config/data_config.json"):
        """
        初始化数据读取器
        
        Args:
            config_path: 数据配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.logger = self._setup_logger()
        
        # 数据缓存
        self.market_data_cache = {}
        self.position_data_cache = {}
        self.capital_data_cache = {}
        
        # 文件监控
        self.last_market_file = None
        self.last_position_file = None
        self.last_capital_file = None
        
    def _load_config(self) -> Dict:
        """加载数据配置"""
        default_config = {
            "market_data": {
                "directory": "data/market",
                "file_pattern": "market_*.csv",
                "encoding": "utf-8",
                "refresh_interval": 2  # 秒
            },
            "position_data": {
                "directory": "data/positions",
                "file_pattern": "positions_*.csv",
                "encoding": "utf-8",
                "refresh_interval": 5
            },
            "capital_data": {
                "directory": "data/capital",
                "file_pattern": "capital_*.csv",
                "encoding": "utf-8",
                "refresh_interval": 10
            }
        }
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                # 创建默认配置文件
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                return default_config
        except Exception as e:
            self.logger.warning(f"配置文件加载失败，使用默认配置: {e}")
            return default_config
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('RealTimeDataReader')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _get_latest_file(self, directory: str, pattern: str) -> Optional[str]:
        """获取目录中最新的文件"""
        try:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                return None
            
            files = glob.glob(os.path.join(directory, pattern))
            if not files:
                return None
            
            # 按修改时间排序，返回最新的文件
            latest_file = max(files, key=os.path.getmtime)
            return latest_file
        except Exception as e:
            self.logger.error(f"获取最新文件失败: {e}")
            return None
    
    def get_market_data(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        获取实时行情数据
        
        Args:
            force_refresh: 是否强制刷新
            
        Returns:
            行情数据DataFrame
        """
        try:
            config = self.config['market_data']
            latest_file = self._get_latest_file(config['directory'], config['file_pattern'])
            
            if not latest_file:
                self.logger.warning("未找到行情数据文件")
                return self._generate_mock_market_data()
            
            # 检查是否需要刷新
            if (not force_refresh and 
                latest_file == self.last_market_file and 
                'market_data' in self.market_data_cache):
                return self.market_data_cache['market_data']
            
            # 读取数据
            df = pd.read_csv(latest_file, encoding=config['encoding'])
            
            # 数据验证和清洗
            df = self._validate_market_data(df)
            
            # 更新缓存
            self.market_data_cache['market_data'] = df
            self.last_market_file = latest_file
            
            self.logger.info(f"成功读取行情数据: {latest_file}, 共{len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"读取行情数据失败: {e}")
            return self._generate_mock_market_data()
    
    def get_position_data(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        获取持仓数据
        
        Args:
            force_refresh: 是否强制刷新
            
        Returns:
            持仓数据DataFrame
        """
        try:
            config = self.config['position_data']
            latest_file = self._get_latest_file(config['directory'], config['file_pattern'])
            
            if not latest_file:
                self.logger.warning("未找到持仓数据文件")
                return self._generate_mock_position_data()
            
            # 检查是否需要刷新
            if (not force_refresh and 
                latest_file == self.last_position_file and 
                'position_data' in self.position_data_cache):
                return self.position_data_cache['position_data']
            
            # 读取数据
            df = pd.read_csv(latest_file, encoding=config['encoding'])
            
            # 数据验证和清洗
            df = self._validate_position_data(df)
            
            # 更新缓存
            self.position_data_cache['position_data'] = df
            self.last_position_file = latest_file
            
            self.logger.info(f"成功读取持仓数据: {latest_file}, 共{len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"读取持仓数据失败: {e}")
            return self._generate_mock_position_data()
    
    def get_capital_data(self, force_refresh: bool = False) -> Dict:
        """
        获取资金数据
        
        Args:
            force_refresh: 是否强制刷新
            
        Returns:
            资金数据字典
        """
        try:
            config = self.config['capital_data']
            latest_file = self._get_latest_file(config['directory'], config['file_pattern'])
            
            if not latest_file:
                self.logger.warning("未找到资金数据文件")
                return self._generate_mock_capital_data()
            
            # 检查是否需要刷新
            if (not force_refresh and 
                latest_file == self.last_capital_file and 
                'capital_data' in self.capital_data_cache):
                return self.capital_data_cache['capital_data']
            
            # 读取数据
            df = pd.read_csv(latest_file, encoding=config['encoding'])
            
            # 数据验证和转换
            capital_data = self._validate_capital_data(df)
            
            # 更新缓存
            self.capital_data_cache['capital_data'] = capital_data
            self.last_capital_file = latest_file
            
            self.logger.info(f"成功读取资金数据: {latest_file}")
            return capital_data
            
        except Exception as e:
            self.logger.error(f"读取资金数据失败: {e}")
            return self._generate_mock_capital_data()
    
    def calculate_position_value(self, force_refresh: bool = False) -> Dict:
        """
        计算持仓市值
        
        Args:
            force_refresh: 是否强制刷新
            
        Returns:
            持仓市值数据
        """
        try:
            # 获取行情和持仓数据
            market_data = self.get_market_data(force_refresh)
            position_data = self.get_position_data(force_refresh)
            
            if market_data.empty or position_data.empty:
                return {'total_value': 0, 'positions': []}
            
            # 合并数据计算市值
            result = []
            total_value = 0
            
            for _, position in position_data.iterrows():
                symbol = position['symbol']
                quantity = position['quantity']
                
                # 查找对应的行情数据
                market_row = market_data[market_data['symbol'] == symbol]
                if not market_row.empty:
                    current_price = market_row.iloc[0]['current_price']
                    market_value = quantity * current_price
                    
                    result.append({
                        'symbol': symbol,
                        'quantity': quantity,
                        'current_price': current_price,
                        'market_value': market_value,
                        'cost_price': position.get('cost_price', current_price),
                        'pnl': market_value - (quantity * position.get('cost_price', current_price))
                    })
                    
                    total_value += market_value
            
            return {
                'total_value': total_value,
                'positions': result,
                'update_time': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"计算持仓市值失败: {e}")
            return {'total_value': 0, 'positions': []}
    
    def _validate_market_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清洗行情数据"""
        required_columns = ['symbol', 'current_price']
        
        # 检查必需列
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"行情数据缺少必需列: {col}")
        
        # 数据类型转换
        df['current_price'] = pd.to_numeric(df['current_price'], errors='coerce')
        
        # 删除无效数据
        df = df.dropna(subset=['current_price'])
        df = df[df['current_price'] > 0]
        
        # 添加默认列
        if 'change_pct' not in df.columns:
            df['change_pct'] = np.random.normal(0, 0.02, len(df))
        
        if 'volume' not in df.columns:
            df['volume'] = np.random.randint(1000000, 10000000, len(df))
        
        return df
    
    def _validate_position_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清洗持仓数据"""
        required_columns = ['symbol', 'quantity']
        
        # 检查必需列
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"持仓数据缺少必需列: {col}")
        
        # 数据类型转换
        df['quantity'] = pd.to_numeric(df['quantity'], errors='coerce')
        
        # 删除无效数据
        df = df.dropna(subset=['quantity'])
        df = df[df['quantity'] != 0]
        
        # 添加默认成本价
        if 'cost_price' not in df.columns:
            df['cost_price'] = np.random.uniform(10, 50, len(df))
        
        return df
    
    def _validate_capital_data(self, df: pd.DataFrame) -> Dict:
        """验证和转换资金数据"""
        if df.empty:
            raise ValueError("资金数据为空")
        
        # 取最新一行数据
        latest_row = df.iloc[-1]
        
        return {
            'total_capital': float(latest_row.get('total_capital', 0)),
            'available_capital': float(latest_row.get('available_capital', 0)),
            'frozen_capital': float(latest_row.get('frozen_capital', 0)),
            'update_time': datetime.now()
        }
    
    def _generate_mock_market_data(self) -> pd.DataFrame:
        """生成模拟行情数据"""
        symbols = ['000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '600000.SH', '600036.SH']
        
        data = []
        for symbol in symbols:
            data.append({
                'symbol': symbol,
                'current_price': np.random.uniform(10, 50),
                'change_pct': np.random.normal(0, 0.02),
                'volume': np.random.randint(1000000, 10000000),
                'turnover': np.random.uniform(100000000, 1000000000)
            })
        
        return pd.DataFrame(data)
    
    def _generate_mock_position_data(self) -> pd.DataFrame:
        """生成模拟持仓数据"""
        symbols = ['000001.SZ', '000002.SZ', '600000.SH']
        
        data = []
        for symbol in symbols:
            data.append({
                'symbol': symbol,
                'quantity': np.random.randint(1000, 10000),
                'cost_price': np.random.uniform(10, 50)
            })
        
        return pd.DataFrame(data)
    
    def _generate_mock_capital_data(self) -> Dict:
        """生成模拟资金数据"""
        total = 500000
        available = np.random.uniform(100000, 300000)
        
        return {
            'total_capital': total,
            'available_capital': available,
            'frozen_capital': total - available,
            'update_time': datetime.now()
        }
    
    def get_data_status(self) -> Dict:
        """获取数据状态"""
        return {
            'market_data': {
                'last_file': self.last_market_file,
                'cached': 'market_data' in self.market_data_cache,
                'last_update': self.market_data_cache.get('last_update')
            },
            'position_data': {
                'last_file': self.last_position_file,
                'cached': 'position_data' in self.position_data_cache,
                'last_update': self.position_data_cache.get('last_update')
            },
            'capital_data': {
                'last_file': self.last_capital_file,
                'cached': 'capital_data' in self.capital_data_cache,
                'last_update': self.capital_data_cache.get('last_update')
            }
        }
