"""
交易信号数据库管理模块
使用SQLite存储历史信号和实时信号
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
import json
import os
from loguru import logger


@dataclass
class TradingSignal:
    """交易信号数据结构"""
    id: Optional[int] = None
    timestamp: datetime = None
    strategy_name: str = ""
    symbol1: str = ""
    symbol2: str = ""
    signal_type: str = ""
    confidence: float = 0.0
    expected_return: float = 0.0
    risk_level: str = ""
    entry_price1: float = 0.0
    entry_price2: float = 0.0
    target_price1: Optional[float] = None
    target_price2: Optional[float] = None
    stop_loss1: Optional[float] = None
    stop_loss2: Optional[float] = None
    performance_score: float = 0.0
    market_data: Optional[str] = None  # JSON格式存储市场数据
    is_historical: bool = False
    is_executed: bool = False
    execution_time: Optional[datetime] = None
    actual_return: Optional[float] = None
    notes: Optional[str] = None


class SignalDatabase:
    """交易信号数据库管理器"""
    
    def __init__(self, db_path: str = "data/trading_signals.db"):
        self.db_path = db_path
        self._ensure_directory_exists()
        self._init_database()
        logger.info(f"初始化信号数据库: {db_path}")
    
    def _ensure_directory_exists(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建交易信号表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    strategy_name TEXT NOT NULL,
                    symbol1 TEXT NOT NULL,
                    symbol2 TEXT,
                    signal_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    expected_return REAL NOT NULL,
                    risk_level TEXT NOT NULL,
                    entry_price1 REAL NOT NULL,
                    entry_price2 REAL,
                    target_price1 REAL,
                    target_price2 REAL,
                    stop_loss1 REAL,
                    stop_loss2 REAL,
                    performance_score REAL DEFAULT 0.0,
                    market_data TEXT,
                    is_historical BOOLEAN DEFAULT FALSE,
                    is_executed BOOLEAN DEFAULT FALSE,
                    execution_time DATETIME,
                    actual_return REAL,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建策略性能表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    date DATE NOT NULL,
                    total_return REAL NOT NULL,
                    sharpe_ratio REAL NOT NULL,
                    max_drawdown REAL NOT NULL,
                    win_rate REAL NOT NULL,
                    volatility REAL NOT NULL,
                    score REAL NOT NULL,
                    signals_count INTEGER DEFAULT 0,
                    executed_signals INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(strategy_name, date)
                )
            ''')
            
            # 创建市场数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    symbol TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume INTEGER,
                    change_pct REAL,
                    market_cap REAL,
                    industry TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_signals_timestamp ON trading_signals(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_signals_strategy ON trading_signals(strategy_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_signals_symbol ON trading_signals(symbol1)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_signals_historical ON trading_signals(is_historical)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_strategy ON strategy_performance(strategy_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_market_symbol ON market_snapshots(symbol)')
            
            conn.commit()
            logger.info("数据库表初始化完成")
    
    def save_signal(self, signal: TradingSignal) -> int:
        """保存交易信号"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 准备数据
            signal_data = asdict(signal)
            signal_data.pop('id', None)  # 移除id，让数据库自动生成
            
            # 处理datetime字段
            if signal_data['timestamp']:
                signal_data['timestamp'] = signal_data['timestamp'].isoformat()
            if signal_data['execution_time']:
                signal_data['execution_time'] = signal_data['execution_time'].isoformat()
            
            # 插入数据
            columns = ', '.join(signal_data.keys())
            placeholders = ', '.join(['?' for _ in signal_data])
            
            cursor.execute(f'''
                INSERT INTO trading_signals ({columns})
                VALUES ({placeholders})
            ''', list(signal_data.values()))
            
            signal_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"保存交易信号: ID={signal_id}, 策略={signal.strategy_name}, 类型={signal.signal_type}")
            return signal_id
    
    def get_signals(self, 
                   strategy_name: Optional[str] = None,
                   is_historical: Optional[bool] = None,
                   start_date: Optional[datetime] = None,
                   end_date: Optional[datetime] = None,
                   limit: Optional[int] = None) -> List[TradingSignal]:
        """获取交易信号"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 构建查询条件
            conditions = []
            params = []
            
            if strategy_name:
                conditions.append("strategy_name = ?")
                params.append(strategy_name)
            
            if is_historical is not None:
                conditions.append("is_historical = ?")
                params.append(is_historical)
            
            if start_date:
                conditions.append("timestamp >= ?")
                params.append(start_date.isoformat())
            
            if end_date:
                conditions.append("timestamp <= ?")
                params.append(end_date.isoformat())
            
            # 构建SQL查询
            sql = "SELECT * FROM trading_signals"
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
            sql += " ORDER BY timestamp DESC"
            if limit:
                sql += f" LIMIT {limit}"
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            
            # 获取列名
            columns = [description[0] for description in cursor.description]
            
            # 转换为TradingSignal对象
            signals = []
            for row in rows:
                signal_dict = dict(zip(columns, row))
                
                # 处理datetime字段
                if signal_dict['timestamp']:
                    signal_dict['timestamp'] = datetime.fromisoformat(signal_dict['timestamp'])
                if signal_dict['execution_time']:
                    signal_dict['execution_time'] = datetime.fromisoformat(signal_dict['execution_time'])
                
                # 移除created_at字段（不在TradingSignal中）
                signal_dict.pop('created_at', None)
                
                signals.append(TradingSignal(**signal_dict))
            
            return signals
    
    def get_realtime_signals(self, hours: int = 24) -> List[TradingSignal]:
        """获取实时信号（最近N小时）"""
        start_time = datetime.now() - timedelta(hours=hours)
        return self.get_signals(is_historical=False, start_date=start_time)
    
    def get_historical_signals(self, days: int = 30) -> List[TradingSignal]:
        """获取历史信号"""
        end_time = datetime.now() - timedelta(days=1)  # 1天前的算历史
        start_time = end_time - timedelta(days=days)
        return self.get_signals(is_historical=True, start_date=start_time, end_date=end_time)
    
    def update_signal_execution(self, signal_id: int, execution_time: datetime, actual_return: float):
        """更新信号执行状态"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE trading_signals 
                SET is_executed = TRUE, execution_time = ?, actual_return = ?
                WHERE id = ?
            ''', (execution_time.isoformat(), actual_return, signal_id))
            
            conn.commit()
            logger.info(f"更新信号执行状态: ID={signal_id}, 实际收益={actual_return:.2%}")
    
    def mark_signals_as_historical(self, cutoff_time: datetime = None):
        """将旧信号标记为历史信号"""
        if cutoff_time is None:
            cutoff_time = datetime.now() - timedelta(days=1)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE trading_signals 
                SET is_historical = TRUE
                WHERE timestamp < ? AND is_historical = FALSE
            ''', (cutoff_time.isoformat(),))
            
            updated_count = cursor.rowcount
            conn.commit()
            
            logger.info(f"标记{updated_count}个信号为历史信号")
            return updated_count
    
    def get_strategy_statistics(self, strategy_name: str = None, days: int = 30) -> Dict:
        """获取策略统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 时间范围
            start_date = datetime.now() - timedelta(days=days)
            
            # 构建查询条件
            conditions = ["timestamp >= ?"]
            params = [start_date.isoformat()]
            
            if strategy_name:
                conditions.append("strategy_name = ?")
                params.append(strategy_name)
            
            where_clause = " WHERE " + " AND ".join(conditions)
            
            # 总体统计
            cursor.execute(f'''
                SELECT 
                    COUNT(*) as total_signals,
                    COUNT(CASE WHEN is_executed = TRUE THEN 1 END) as executed_signals,
                    AVG(confidence) as avg_confidence,
                    AVG(expected_return) as avg_expected_return,
                    AVG(CASE WHEN actual_return IS NOT NULL THEN actual_return END) as avg_actual_return,
                    AVG(performance_score) as avg_performance_score
                FROM trading_signals
                {where_clause}
            ''', params)
            
            stats = cursor.fetchone()
            
            # 按策略分组统计
            cursor.execute(f'''
                SELECT 
                    strategy_name,
                    COUNT(*) as signal_count,
                    COUNT(CASE WHEN is_executed = TRUE THEN 1 END) as executed_count,
                    AVG(confidence) as avg_confidence,
                    AVG(performance_score) as avg_score
                FROM trading_signals
                {where_clause}
                GROUP BY strategy_name
                ORDER BY signal_count DESC
            ''', params)
            
            strategy_stats = cursor.fetchall()
            
            # 按信号类型统计
            cursor.execute(f'''
                SELECT 
                    signal_type,
                    COUNT(*) as count,
                    AVG(confidence) as avg_confidence
                FROM trading_signals
                {where_clause}
                GROUP BY signal_type
            ''', params)
            
            signal_type_stats = cursor.fetchall()
            
            return {
                'total_signals': stats[0] or 0,
                'executed_signals': stats[1] or 0,
                'execution_rate': (stats[1] or 0) / max(stats[0] or 1, 1),
                'avg_confidence': stats[2] or 0,
                'avg_expected_return': stats[3] or 0,
                'avg_actual_return': stats[4] or 0,
                'avg_performance_score': stats[5] or 0,
                'strategy_breakdown': [
                    {
                        'strategy_name': row[0],
                        'signal_count': row[1],
                        'executed_count': row[2],
                        'avg_confidence': row[3],
                        'avg_score': row[4]
                    }
                    for row in strategy_stats
                ],
                'signal_type_breakdown': [
                    {
                        'signal_type': row[0],
                        'count': row[1],
                        'avg_confidence': row[2]
                    }
                    for row in signal_type_stats
                ]
            }
    
    def save_market_snapshot(self, symbol: str, price: float, volume: int = None, 
                           change_pct: float = None, market_cap: float = None, 
                           industry: str = None):
        """保存市场快照数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO market_snapshots 
                (timestamp, symbol, price, volume, change_pct, market_cap, industry)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (datetime.now().isoformat(), symbol, price, volume, change_pct, market_cap, industry))
            
            conn.commit()
    
    def get_signal_performance_data(self, days: int = 30) -> pd.DataFrame:
        """获取信号性能数据用于分析"""
        with sqlite3.connect(self.db_path) as conn:
            start_date = datetime.now() - timedelta(days=days)
            
            df = pd.read_sql_query('''
                SELECT 
                    timestamp,
                    strategy_name,
                    signal_type,
                    confidence,
                    expected_return,
                    actual_return,
                    performance_score,
                    is_executed,
                    risk_level
                FROM trading_signals
                WHERE timestamp >= ?
                ORDER BY timestamp
            ''', conn, params=[start_date.isoformat()])
            
            # 转换时间戳
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            return df
    
    def cleanup_old_data(self, days: int = 90):
        """清理旧数据"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 删除旧的市场快照
            cursor.execute('''
                DELETE FROM market_snapshots 
                WHERE timestamp < ?
            ''', (cutoff_date.isoformat(),))
            
            market_deleted = cursor.rowcount
            
            # 删除旧的未执行信号
            cursor.execute('''
                DELETE FROM trading_signals 
                WHERE timestamp < ? AND is_executed = FALSE
            ''', (cutoff_date.isoformat(),))
            
            signals_deleted = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"清理旧数据: 删除{market_deleted}条市场数据, {signals_deleted}条未执行信号")
            return market_deleted, signals_deleted
    
    def export_signals_to_csv(self, filepath: str, days: int = 30):
        """导出信号数据到CSV"""
        df = self.get_signal_performance_data(days)
        df.to_csv(filepath, index=False)
        logger.info(f"导出{len(df)}条信号数据到: {filepath}")
    
    def get_database_info(self) -> Dict:
        """获取数据库信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 获取各表记录数
            table_counts = {}
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                table_counts[table] = cursor.fetchone()[0]
            
            # 获取数据库文件大小
            db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            
            return {
                'database_path': self.db_path,
                'database_size_mb': db_size / (1024 * 1024),
                'tables': tables,
                'table_counts': table_counts,
                'total_records': sum(table_counts.values())
            }


if __name__ == "__main__":
    # 测试数据库功能
    db = SignalDatabase("test_signals.db")
    
    # 创建测试信号
    test_signal = TradingSignal(
        timestamp=datetime.now(),
        strategy_name="配对交易",
        symbol1="000001.SZ",
        symbol2="000002.SZ",
        signal_type="LONG_SHORT",
        confidence=0.85,
        expected_return=0.02,
        risk_level="MEDIUM",
        entry_price1=10.5,
        entry_price2=12.3,
        performance_score=0.75,
        is_historical=False
    )
    
    # 保存信号
    signal_id = db.save_signal(test_signal)
    print(f"保存信号ID: {signal_id}")
    
    # 获取信号
    signals = db.get_realtime_signals()
    print(f"实时信号数量: {len(signals)}")
    
    # 获取统计信息
    stats = db.get_strategy_statistics()
    print(f"统计信息: {stats}")
    
    # 获取数据库信息
    db_info = db.get_database_info()
    print(f"数据库信息: {db_info}")
