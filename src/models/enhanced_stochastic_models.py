"""
增强随机微积分模型模块
实现考虑历史行情和涨跌因素的随机微积分方程
集成历史数据分析、市场状态识别和动态参数调整
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from typing import Tuple, Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime, timedelta

try:
    from .historical_stochastic_models import HistoricalDataProcessor, HistoricalFactors, MarketRegime
    from .stochastic_models import BSParameters, HestonParameters, BlackScholesModel, HestonModel
except ImportError:
    from src.models.historical_stochastic_models import HistoricalDataProcessor, HistoricalFactors, MarketRegime
    from src.models.stochastic_models import BSParameters, HestonParameters, BlackScholesModel, HestonModel


@dataclass
class EnhancedBSParameters(BSParameters):
    """增强Black-Scholes模型参数"""
    trend_adjustment: float = 0.0  # 趋势调整因子
    volatility_adjustment: float = 1.0  # 波动率调整因子
    momentum_factor: float = 0.0  # 动量因子
    mean_reversion_speed: float = 0.0  # 均值回归速度
    jump_intensity: float = 0.0  # 跳跃强度
    sentiment_impact: float = 0.0  # 情绪影响


@dataclass
class MarketConditions:
    """市场条件"""
    bull_market_prob: float  # 牛市概率
    bear_market_prob: float  # 熊市概率
    volatile_market_prob: float  # 震荡市概率
    trend_strength: float  # 趋势强度
    volatility_regime: str  # 波动率状态
    liquidity_condition: str  # 流动性状况


class HistoryAwareBlackScholesModel(BlackScholesModel):
    """历史感知Black-Scholes模型"""
    
    def __init__(self, params: EnhancedBSParameters, lookback_days: int = 252):
        super().__init__(params)
        self.enhanced_params = params
        self.lookback_days = lookback_days
        self.data_processor = HistoricalDataProcessor(lookback_days)
        self.price_history = []
        self.market_conditions = None
        
    def update_market_data(self, price: float, volume: float, timestamp: datetime, symbol: str = "default"):
        """更新市场数据"""
        self.data_processor.update_history(symbol, price, volume, timestamp)
        self.price_history.append((timestamp, price))
        
        # 保持历史数据长度
        if len(self.price_history) > self.lookback_days:
            self.price_history = self.price_history[-self.lookback_days:]
        
        # 更新市场条件
        self._update_market_conditions(symbol)
        
        # 动态调整模型参数
        self._adjust_parameters(symbol)
    
    def _update_market_conditions(self, symbol: str):
        """更新市场条件"""
        if len(self.price_history) < 20:
            return
        
        # 获取历史因素
        historical_factors = self.data_processor.calculate_historical_factors(symbol)
        market_regime = self.data_processor.detect_market_regime(symbol)
        
        # 计算市场状态概率
        if market_regime.regime_type == "bull":
            bull_prob, bear_prob, volatile_prob = 0.7, 0.1, 0.2
        elif market_regime.regime_type == "bear":
            bull_prob, bear_prob, volatile_prob = 0.1, 0.7, 0.2
        elif market_regime.regime_type == "volatile":
            bull_prob, bear_prob, volatile_prob = 0.2, 0.2, 0.6
        else:  # sideways
            bull_prob, bear_prob, volatile_prob = 0.3, 0.3, 0.4
        
        self.market_conditions = MarketConditions(
            bull_market_prob=bull_prob,
            bear_market_prob=bear_prob,
            volatile_market_prob=volatile_prob,
            trend_strength=abs(historical_factors.trend_factor),
            volatility_regime=market_regime.volatility_level,
            liquidity_condition="normal"  # 简化处理
        )
    
    def _adjust_parameters(self, symbol: str):
        """动态调整模型参数"""
        if len(self.price_history) < 20:
            return
        
        historical_factors = self.data_processor.calculate_historical_factors(symbol)
        
        # 调整漂移率 (考虑趋势和动量)
        trend_adjustment = historical_factors.trend_factor * 0.1  # 趋势影响
        momentum_adjustment = historical_factors.momentum_factor * 0.05  # 动量影响
        sentiment_adjustment = historical_factors.sentiment_factor * 0.02  # 情绪影响
        
        self.enhanced_params.mu = (self.params.mu + 
                                  trend_adjustment + 
                                  momentum_adjustment + 
                                  sentiment_adjustment)
        
        # 调整波动率 (考虑波动率聚集和市场状态)
        vol_adjustment = historical_factors.volatility_factor
        seasonal_adjustment = historical_factors.seasonal_factor
        
        self.enhanced_params.sigma = (self.params.sigma * 
                                     vol_adjustment * 
                                     seasonal_adjustment)
        
        # 均值回归调整
        if abs(historical_factors.mean_reversion_factor) > 0.1:
            self.enhanced_params.mean_reversion_speed = abs(historical_factors.mean_reversion_factor)
        
        # 跳跃强度调整 (基于市场波动性)
        if historical_factors.volatility_factor > 1.5:
            self.enhanced_params.jump_intensity = (historical_factors.volatility_factor - 1) * 0.1
    
    def simulate_with_history(self, n_paths: int, n_steps: int, 
                            consider_jumps: bool = True,
                            consider_mean_reversion: bool = True) -> np.ndarray:
        """
        考虑历史因素的路径模拟
        """
        dt = self.enhanced_params.T / n_steps
        paths = np.zeros((n_paths, n_steps + 1))
        paths[:, 0] = self.enhanced_params.S0
        
        # 获取当前市场条件
        if self.market_conditions is None:
            return self.simulate(n_paths, n_steps)
        
        for i in range(1, n_steps + 1):
            # 基础布朗运动
            dW = np.random.normal(0, np.sqrt(dt), n_paths)
            
            # 动态调整的漂移率和波动率
            current_mu = self.enhanced_params.mu
            current_sigma = self.enhanced_params.sigma
            
            # 市场状态影响
            if self.market_conditions.volatile_market_prob > 0.5:
                current_sigma *= 1.2  # 震荡市场增加波动率
            elif self.market_conditions.bull_market_prob > 0.6:
                current_mu += 0.02  # 牛市增加漂移率
            elif self.market_conditions.bear_market_prob > 0.6:
                current_mu -= 0.02  # 熊市减少漂移率
            
            # 均值回归效应
            if consider_mean_reversion and self.enhanced_params.mean_reversion_speed > 0:
                long_term_mean = self.enhanced_params.S0
                reversion_force = (self.enhanced_params.mean_reversion_speed * 
                                 (long_term_mean - paths[:, i-1]) / paths[:, i-1])
                current_mu += reversion_force
            
            # 基础价格更新
            price_change = ((current_mu - 0.5 * current_sigma**2) * dt + 
                           current_sigma * dW)
            
            # 跳跃过程
            if consider_jumps and self.enhanced_params.jump_intensity > 0:
                jump_occurs = np.random.poisson(self.enhanced_params.jump_intensity * dt, n_paths)
                jump_sizes = np.where(jump_occurs > 0, 
                                    np.random.normal(-0.02, 0.05, n_paths), 0)
                price_change += jump_sizes
            
            # 更新价格
            paths[:, i] = paths[:, i-1] * np.exp(price_change)
            
            # 确保价格为正
            paths[:, i] = np.maximum(paths[:, i], 0.01)
        
        return paths


if __name__ == "__main__":
    # 测试增强模型
    params = EnhancedBSParameters(S0=100, mu=0.05, sigma=0.2, r=0.03, T=1.0)
    model = HistoryAwareBlackScholesModel(params)
    
    # 模拟历史数据
    import random
    for i in range(100):
        timestamp = datetime.now() - timedelta(days=100-i)
        price = 100 + random.gauss(0, 5)
        volume = 1000000
        model.update_market_data(price, volume, timestamp)
    
    # 生成路径
    paths = model.simulate_with_history(1000, 252)
    print(f"增强模型路径形状: {paths.shape}")
    print(f"最终价格范围: {np.min(paths[:, -1]):.2f} - {np.max(paths[:, -1]):.2f}")
