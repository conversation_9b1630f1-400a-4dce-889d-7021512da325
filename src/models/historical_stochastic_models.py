"""
历史感知随机微积分模型模块
实现考虑历史行情信息和近期涨跌因素的增强随机微积分方程
包括历史趋势、波动率聚集、均值回归等特性
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import minimize
import matplotlib.pyplot as plt
from typing import Tuple, List, Dict, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from loguru import logger


@dataclass
class HistoricalFactors:
    """历史因素数据结构"""
    trend_factor: float  # 趋势因子 (-1到1)
    momentum_factor: float  # 动量因子
    volatility_factor: float  # 波动率因子
    mean_reversion_factor: float  # 均值回归因子
    volume_factor: float  # 成交量因子
    sentiment_factor: float  # 市场情绪因子
    seasonal_factor: float  # 季节性因子
    correlation_factor: float  # 相关性因子


@dataclass
class MarketRegime:
    """市场状态"""
    regime_type: str  # 'bull', 'bear', 'sideways', 'volatile'
    confidence: float  # 状态置信度
    duration: int  # 持续天数
    volatility_level: str  # 'low', 'medium', 'high'


class HistoricalDataProcessor:
    """历史数据处理器"""
    
    def __init__(self, lookback_days: int = 252):
        self.lookback_days = lookback_days
        self.price_history = {}
        self.volume_history = {}
        self.return_history = {}
        
    def update_history(self, symbol: str, price: float, volume: float, timestamp: datetime):
        """更新历史数据"""
        if symbol not in self.price_history:
            self.price_history[symbol] = []
            self.volume_history[symbol] = []
            self.return_history[symbol] = []
        
        self.price_history[symbol].append((timestamp, price))
        self.volume_history[symbol].append((timestamp, volume))
        
        # 计算收益率
        if len(self.price_history[symbol]) > 1:
            prev_price = self.price_history[symbol][-2][1]
            return_rate = (price - prev_price) / prev_price
            self.return_history[symbol].append((timestamp, return_rate))
        
        # 保持历史数据长度
        if len(self.price_history[symbol]) > self.lookback_days:
            self.price_history[symbol] = self.price_history[symbol][-self.lookback_days:]
            self.volume_history[symbol] = self.volume_history[symbol][-self.lookback_days:]
            self.return_history[symbol] = self.return_history[symbol][-self.lookback_days:]
    
    def calculate_historical_factors(self, symbol: str) -> HistoricalFactors:
        """计算历史因素"""
        if symbol not in self.price_history or len(self.price_history[symbol]) < 20:
            return HistoricalFactors(0, 0, 1, 0, 1, 0, 1, 0)
        
        prices = [p[1] for p in self.price_history[symbol]]
        volumes = [v[1] for v in self.volume_history[symbol]]
        returns = [r[1] for r in self.return_history[symbol]]
        
        # 1. 趋势因子 (基于移动平均线)
        trend_factor = self._calculate_trend_factor(prices)
        
        # 2. 动量因子 (基于近期收益率)
        momentum_factor = self._calculate_momentum_factor(returns)
        
        # 3. 波动率因子 (基于历史波动率)
        volatility_factor = self._calculate_volatility_factor(returns)
        
        # 4. 均值回归因子 (基于价格偏离)
        mean_reversion_factor = self._calculate_mean_reversion_factor(prices)
        
        # 5. 成交量因子 (基于成交量变化)
        volume_factor = self._calculate_volume_factor(volumes)
        
        # 6. 市场情绪因子 (基于连续涨跌)
        sentiment_factor = self._calculate_sentiment_factor(returns)
        
        # 7. 季节性因子 (基于时间)
        seasonal_factor = self._calculate_seasonal_factor()
        
        # 8. 相关性因子 (基于市场相关性)
        correlation_factor = self._calculate_correlation_factor(returns)
        
        return HistoricalFactors(
            trend_factor=trend_factor,
            momentum_factor=momentum_factor,
            volatility_factor=volatility_factor,
            mean_reversion_factor=mean_reversion_factor,
            volume_factor=volume_factor,
            sentiment_factor=sentiment_factor,
            seasonal_factor=seasonal_factor,
            correlation_factor=correlation_factor
        )
    
    def _calculate_trend_factor(self, prices: List[float]) -> float:
        """计算趋势因子"""
        if len(prices) < 20:
            return 0
        
        # 短期和长期移动平均
        short_ma = np.mean(prices[-5:])
        long_ma = np.mean(prices[-20:])
        
        # 趋势强度
        trend_strength = (short_ma - long_ma) / long_ma
        
        # 标准化到[-1, 1]
        return np.tanh(trend_strength * 10)
    
    def _calculate_momentum_factor(self, returns: List[float]) -> float:
        """计算动量因子"""
        if len(returns) < 10:
            return 0
        
        # 近期收益率加权平均
        recent_returns = returns[-10:]
        weights = np.exp(np.linspace(-1, 0, len(recent_returns)))
        weights = weights / np.sum(weights)
        
        momentum = np.sum(recent_returns * weights)
        
        # 标准化
        return np.tanh(momentum * 50)
    
    def _calculate_volatility_factor(self, returns: List[float]) -> float:
        """计算波动率因子"""
        if len(returns) < 20:
            return 1
        
        # 短期和长期波动率
        short_vol = np.std(returns[-5:]) if len(returns) >= 5 else 0
        long_vol = np.std(returns[-20:])
        
        if long_vol == 0:
            return 1
        
        # 波动率比率
        vol_ratio = short_vol / long_vol
        
        # 调整到合理范围
        return max(0.5, min(3.0, vol_ratio))
    
    def _calculate_mean_reversion_factor(self, prices: List[float]) -> float:
        """计算均值回归因子"""
        if len(prices) < 20:
            return 0
        
        current_price = prices[-1]
        long_mean = np.mean(prices[-20:])
        
        # 价格偏离程度
        deviation = (current_price - long_mean) / long_mean
        
        # 均值回归强度 (偏离越大，回归力量越强)
        return -np.tanh(deviation * 5)
    
    def _calculate_volume_factor(self, volumes: List[float]) -> float:
        """计算成交量因子"""
        if len(volumes) < 10:
            return 1
        
        recent_volume = np.mean(volumes[-3:])
        avg_volume = np.mean(volumes[-10:])
        
        if avg_volume == 0:
            return 1
        
        volume_ratio = recent_volume / avg_volume
        
        # 成交量放大系数
        return max(0.5, min(2.0, volume_ratio))
    
    def _calculate_sentiment_factor(self, returns: List[float]) -> float:
        """计算市场情绪因子"""
        if len(returns) < 10:
            return 0
        
        recent_returns = returns[-10:]
        
        # 连续上涨/下跌天数
        consecutive_up = 0
        consecutive_down = 0
        
        for r in reversed(recent_returns):
            if r > 0:
                consecutive_up += 1
                break
            elif r < 0:
                consecutive_down += 1
            else:
                break
        
        # 情绪指标
        if consecutive_up > 3:
            return min(0.8, consecutive_up * 0.2)  # 过度乐观
        elif consecutive_down > 3:
            return max(-0.8, -consecutive_down * 0.2)  # 过度悲观
        else:
            return 0
    
    def _calculate_seasonal_factor(self) -> float:
        """计算季节性因子"""
        now = datetime.now()
        
        # 简单的季节性模式
        month = now.month
        
        # 春季行情 (2-4月)
        if 2 <= month <= 4:
            return 1.1
        # 夏季调整 (6-8月)
        elif 6 <= month <= 8:
            return 0.9
        # 秋季反弹 (9-11月)
        elif 9 <= month <= 11:
            return 1.05
        # 年末效应 (12-1月)
        else:
            return 0.95
    
    def _calculate_correlation_factor(self, returns: List[float]) -> float:
        """计算相关性因子"""
        if len(returns) < 20:
            return 0
        
        # 简化的市场相关性计算
        # 这里可以与市场指数收益率计算相关性
        recent_volatility = np.std(returns[-10:])
        historical_volatility = np.std(returns[-20:])
        
        if historical_volatility == 0:
            return 0
        
        # 相对波动率变化
        vol_change = (recent_volatility - historical_volatility) / historical_volatility
        
        return np.tanh(vol_change * 3)
    
    def detect_market_regime(self, symbol: str) -> MarketRegime:
        """检测市场状态"""
        if symbol not in self.return_history or len(self.return_history[symbol]) < 20:
            return MarketRegime("sideways", 0.5, 0, "medium")
        
        returns = [r[1] for r in self.return_history[symbol][-20:]]
        
        # 计算趋势和波动率
        cumulative_return = np.sum(returns)
        volatility = np.std(returns)
        
        # 判断市场状态
        if cumulative_return > 0.05 and volatility < 0.02:
            regime_type = "bull"
            confidence = min(0.9, cumulative_return * 10)
        elif cumulative_return < -0.05 and volatility < 0.02:
            regime_type = "bear"
            confidence = min(0.9, abs(cumulative_return) * 10)
        elif volatility > 0.03:
            regime_type = "volatile"
            confidence = min(0.9, volatility * 20)
        else:
            regime_type = "sideways"
            confidence = 0.6
        
        # 波动率水平
        if volatility < 0.015:
            volatility_level = "low"
        elif volatility > 0.025:
            volatility_level = "high"
        else:
            volatility_level = "medium"
        
        return MarketRegime(
            regime_type=regime_type,
            confidence=confidence,
            duration=len(returns),
            volatility_level=volatility_level
        )
