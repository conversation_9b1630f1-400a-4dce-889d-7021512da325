"""
模型校准器
提供统一的模型参数校准和优化功能
"""

import numpy as np
import pandas as pd
from scipy.optimize import minimize, differential_evolution
from scipy import stats
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings

try:
    from .stochastic_models import BSParameters, HestonParameters
    from .unified_model import UnifiedModelParameters, UnifiedStochasticModel
except ImportError:
    from src.models.stochastic_models import BSParameters, HestonParameters
    from src.models.unified_model import UnifiedModelParameters, UnifiedStochasticModel


@dataclass
class CalibrationResult:
    """校准结果"""
    success: bool
    parameters: Dict[str, float]
    objective_value: float
    iterations: int
    method: str
    convergence_info: Dict[str, Any]
    execution_time: float


class ModelCalibrator:
    """模型校准器"""
    
    def __init__(self, market_data: pd.DataFrame, option_data: Optional[pd.DataFrame] = None):
        """
        初始化校准器
        
        Args:
            market_data: 历史市场数据 (包含 'close', 'volume' 等列)
            option_data: 期权市场数据 (可选)
        """
        self.market_data = market_data.copy()
        self.option_data = option_data
        
        # 预处理数据
        self._preprocess_data()
        
        # 校准配置
        self.calibration_config = {
            'max_iterations': 1000,
            'tolerance': 1e-6,
            'method': 'L-BFGS-B',
            'use_bounds': True,
            'parallel': False
        }
    
    def _preprocess_data(self):
        """预处理市场数据"""
        # 计算收益率
        self.market_data['returns'] = self.market_data['close'].pct_change()
        self.market_data['log_returns'] = np.log(self.market_data['close'] / self.market_data['close'].shift(1))
        
        # 删除缺失值
        self.market_data = self.market_data.dropna()
        
        # 计算基础统计量
        self.returns = self.market_data['returns'].values
        self.log_returns = self.market_data['log_returns'].values
        self.prices = self.market_data['close'].values
        
        # 年化参数
        self.trading_days = 252
        
    def calibrate_black_scholes(self, method: str = "mle") -> CalibrationResult:
        """
        校准Black-Scholes模型
        
        Args:
            method: 校准方法 ("mle", "moments", "least_squares")
        """
        start_time = datetime.now()
        
        if method == "mle":
            result = self._calibrate_bs_mle()
        elif method == "moments":
            result = self._calibrate_bs_moments()
        elif method == "least_squares":
            result = self._calibrate_bs_least_squares()
        else:
            raise ValueError(f"未知的校准方法: {method}")
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return CalibrationResult(
            success=result['success'],
            parameters=result['parameters'],
            objective_value=result['objective_value'],
            iterations=result.get('iterations', 0),
            method=f"black_scholes_{method}",
            convergence_info=result.get('convergence_info', {}),
            execution_time=execution_time
        )
    
    def _calibrate_bs_mle(self) -> Dict[str, Any]:
        """最大似然估计校准Black-Scholes"""
        def negative_log_likelihood(params):
            mu, sigma = params
            if sigma <= 0:
                return 1e10
            
            # 计算对数似然
            n = len(self.log_returns)
            log_likelihood = (-n/2 * np.log(2 * np.pi * sigma**2) - 
                            np.sum((self.log_returns - mu)**2) / (2 * sigma**2))
            return -log_likelihood
        
        # 初始参数估计
        mu_init = np.mean(self.log_returns) * self.trading_days
        sigma_init = np.std(self.log_returns) * np.sqrt(self.trading_days)
        
        # 参数边界
        bounds = [(-1.0, 1.0), (0.01, 2.0)]
        
        try:
            result = minimize(
                negative_log_likelihood,
                x0=[mu_init, sigma_init],
                bounds=bounds,
                method='L-BFGS-B'
            )
            
            return {
                'success': result.success,
                'parameters': {
                    'mu': result.x[0],
                    'sigma': result.x[1],
                    'S0': self.prices[-1]
                },
                'objective_value': result.fun,
                'iterations': result.nit,
                'convergence_info': {'message': result.message}
            }
        except Exception as e:
            return {
                'success': False,
                'parameters': {'mu': mu_init, 'sigma': sigma_init, 'S0': self.prices[-1]},
                'objective_value': float('inf'),
                'iterations': 0,
                'convergence_info': {'error': str(e)}
            }
    
    def _calibrate_bs_moments(self) -> Dict[str, Any]:
        """矩匹配校准Black-Scholes"""
        try:
            # 直接使用样本矩
            mu = np.mean(self.log_returns) * self.trading_days
            sigma = np.std(self.log_returns, ddof=1) * np.sqrt(self.trading_days)
            
            return {
                'success': True,
                'parameters': {
                    'mu': mu,
                    'sigma': sigma,
                    'S0': self.prices[-1]
                },
                'objective_value': 0.0,
                'iterations': 1,
                'convergence_info': {'method': 'analytical_moments'}
            }
        except Exception as e:
            return {
                'success': False,
                'parameters': {'mu': 0.05, 'sigma': 0.2, 'S0': self.prices[-1]},
                'objective_value': float('inf'),
                'iterations': 0,
                'convergence_info': {'error': str(e)}
            }
    
    def _calibrate_bs_least_squares(self) -> Dict[str, Any]:
        """最小二乘校准Black-Scholes"""
        if self.option_data is None:
            # 如果没有期权数据，回退到矩匹配
            return self._calibrate_bs_moments()
        
        # 这里可以实现基于期权价格的最小二乘校准
        # 暂时返回矩匹配结果
        return self._calibrate_bs_moments()
    
    def calibrate_heston(self, method: str = "characteristic_function") -> CalibrationResult:
        """
        校准Heston模型
        
        Args:
            method: 校准方法 ("characteristic_function", "moments", "particle_swarm")
        """
        start_time = datetime.now()
        
        if method == "moments":
            result = self._calibrate_heston_moments()
        elif method == "particle_swarm":
            result = self._calibrate_heston_pso()
        else:
            result = self._calibrate_heston_moments()  # 默认使用矩匹配
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return CalibrationResult(
            success=result['success'],
            parameters=result['parameters'],
            objective_value=result['objective_value'],
            iterations=result.get('iterations', 0),
            method=f"heston_{method}",
            convergence_info=result.get('convergence_info', {}),
            execution_time=execution_time
        )
    
    def _calibrate_heston_moments(self) -> Dict[str, Any]:
        """基于矩的Heston模型校准"""
        try:
            # 计算样本统计量
            returns_mean = np.mean(self.returns)
            returns_var = np.var(self.returns, ddof=1)
            returns_skew = stats.skew(self.returns)
            returns_kurt = stats.kurtosis(self.returns)
            
            # 简化的参数估计
            mu = returns_mean * self.trading_days
            V0 = returns_var * self.trading_days
            theta = V0  # 长期方差等于当前方差
            kappa = 2.0  # 默认回归速度
            sigma_v = 0.3  # 默认方差的波动率
            rho = -0.7  # 默认负相关
            
            return {
                'success': True,
                'parameters': {
                    'mu': mu,
                    'V0': V0,
                    'kappa': kappa,
                    'theta': theta,
                    'sigma_v': sigma_v,
                    'rho': rho,
                    'S0': self.prices[-1]
                },
                'objective_value': 0.0,
                'iterations': 1,
                'convergence_info': {'method': 'simplified_moments'}
            }
        except Exception as e:
            return {
                'success': False,
                'parameters': {
                    'mu': 0.05, 'V0': 0.04, 'kappa': 2.0, 'theta': 0.04,
                    'sigma_v': 0.3, 'rho': -0.7, 'S0': self.prices[-1]
                },
                'objective_value': float('inf'),
                'iterations': 0,
                'convergence_info': {'error': str(e)}
            }
    
    def _calibrate_heston_pso(self) -> Dict[str, Any]:
        """粒子群优化校准Heston模型"""
        def objective_function(params):
            mu, V0, kappa, theta, sigma_v, rho = params
            
            # 参数约束检查
            if (V0 <= 0 or kappa <= 0 or theta <= 0 or sigma_v <= 0 or 
                abs(rho) >= 1 or 2*kappa*theta <= sigma_v**2):
                return 1e10
            
            # 简化的目标函数：匹配收益率的前四阶矩
            try:
                # 这里应该使用Heston模型模拟或特征函数
                # 暂时使用简化的目标函数
                sample_mean = np.mean(self.returns) * self.trading_days
                sample_var = np.var(self.returns) * self.trading_days
                
                error = (mu - sample_mean)**2 + (V0 - sample_var)**2
                return error
            except:
                return 1e10
        
        # 参数边界
        bounds = [
            (-0.5, 0.5),   # mu
            (0.01, 1.0),   # V0
            (0.1, 10.0),   # kappa
            (0.01, 1.0),   # theta
            (0.1, 2.0),    # sigma_v
            (-0.99, 0.99)  # rho
        ]
        
        try:
            result = differential_evolution(
                objective_function,
                bounds,
                maxiter=100,
                seed=42
            )
            
            return {
                'success': result.success,
                'parameters': {
                    'mu': result.x[0],
                    'V0': result.x[1],
                    'kappa': result.x[2],
                    'theta': result.x[3],
                    'sigma_v': result.x[4],
                    'rho': result.x[5],
                    'S0': self.prices[-1]
                },
                'objective_value': result.fun,
                'iterations': result.nit,
                'convergence_info': {'message': result.message}
            }
        except Exception as e:
            return self._calibrate_heston_moments()
    
    def calibrate_unified_model(self, target_metrics: Optional[Dict[str, float]] = None) -> CalibrationResult:
        """
        校准统一模型
        
        Args:
            target_metrics: 目标指标字典
        """
        start_time = datetime.now()
        
        try:
            # 首先校准各个子模型
            bs_result = self.calibrate_black_scholes("mle")
            heston_result = self.calibrate_heston("moments")
            
            # 创建统一模型参数
            unified_params = UnifiedModelParameters(
                S0=self.prices[-1],
                mu=bs_result.parameters['mu'],
                sigma=bs_result.parameters['sigma'],
                V0=heston_result.parameters['V0'],
                kappa=heston_result.parameters['kappa'],
                theta=heston_result.parameters['theta'],
                sigma_v=heston_result.parameters['sigma_v'],
                rho=heston_result.parameters['rho']
            )
            
            # 优化模型权重
            optimal_weights = self._optimize_model_weights(unified_params)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return CalibrationResult(
                success=True,
                parameters={
                    **bs_result.parameters,
                    **heston_result.parameters,
                    **optimal_weights
                },
                objective_value=0.0,
                iterations=bs_result.iterations + heston_result.iterations,
                method="unified_multi_stage",
                convergence_info={
                    'bs_success': bs_result.success,
                    'heston_success': heston_result.success
                },
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return CalibrationResult(
                success=False,
                parameters={},
                objective_value=float('inf'),
                iterations=0,
                method="unified_multi_stage",
                convergence_info={'error': str(e)},
                execution_time=execution_time
            )
    
    def _optimize_model_weights(self, params: UnifiedModelParameters) -> Dict[str, float]:
        """优化模型权重"""
        # 简化的权重优化
        # 基于市场状态调整权重
        
        recent_volatility = np.std(self.returns[-20:]) * np.sqrt(self.trading_days)
        
        if recent_volatility > 0.3:  # 高波动
            return {
                'classical_weight': 0.2,
                'enhanced_weight': 0.5,
                'deterministic_weight': 0.3
            }
        elif recent_volatility < 0.15:  # 低波动
            return {
                'classical_weight': 0.5,
                'enhanced_weight': 0.3,
                'deterministic_weight': 0.2
            }
        else:  # 中等波动
            return {
                'classical_weight': 0.3,
                'enhanced_weight': 0.4,
                'deterministic_weight': 0.3
            }
    
    def validate_calibration(self, calibration_result: CalibrationResult, 
                           validation_data: Optional[pd.DataFrame] = None) -> Dict[str, float]:
        """验证校准结果"""
        if not calibration_result.success:
            return {'validation_score': 0.0, 'error': 'calibration_failed'}
        
        try:
            # 使用验证数据或保留的测试数据
            if validation_data is not None:
                test_data = validation_data
            else:
                # 使用最后20%的数据作为验证集
                split_point = int(len(self.market_data) * 0.8)
                test_data = self.market_data.iloc[split_point:]
            
            # 计算验证指标
            test_returns = test_data['returns'].values
            
            # 简化的验证：比较统计量
            actual_mean = np.mean(test_returns) * self.trading_days
            actual_vol = np.std(test_returns) * np.sqrt(self.trading_days)
            
            predicted_mean = calibration_result.parameters.get('mu', 0)
            predicted_vol = calibration_result.parameters.get('sigma', 0)
            
            mean_error = abs(actual_mean - predicted_mean) / max(abs(actual_mean), 0.01)
            vol_error = abs(actual_vol - predicted_vol) / max(actual_vol, 0.01)
            
            validation_score = 1.0 / (1.0 + mean_error + vol_error)
            
            return {
                'validation_score': validation_score,
                'mean_error': mean_error,
                'volatility_error': vol_error,
                'actual_mean': actual_mean,
                'predicted_mean': predicted_mean,
                'actual_volatility': actual_vol,
                'predicted_volatility': predicted_vol
            }
            
        except Exception as e:
            return {'validation_score': 0.0, 'error': str(e)}
