"""
统一随机微积分模型
整合Black-Scholes、<PERSON><PERSON>、C<PERSON>等经典模型
以及历史感知和确定性增强的随机微积分方程
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Tuple, Optional, Dict, Any, List, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

try:
    from .stochastic_models import BlackScholesModel, HestonModel, CIRModel, BSParameters, HestonParameters
    from .enhanced_stochastic_models import HistoryAwareBlackScholesModel, EnhancedBSParameters
    from .deterministic_enhanced_models import DeterministicEnhancedModel, EnhancedDeterministicParameters
    from .historical_stochastic_models import HistoricalDataProcessor, HistoricalFactors, MarketRegime
except ImportError:
    from src.models.stochastic_models import BlackScholesModel, HestonModel, CIRModel, BSParameters, HestonParameters
    from src.models.enhanced_stochastic_models import HistoryAwareBlackScholesModel, EnhancedBSParameters
    from src.models.deterministic_enhanced_models import DeterministicEnhancedModel, EnhancedDeterministicParameters
    from src.models.historical_stochastic_models import HistoricalDataProcessor, HistoricalFactors, MarketRegime


class ModelType(Enum):
    """模型类型枚举"""
    BLACK_SCHOLES = "black_scholes"
    HESTON = "heston"
    CIR = "cir"
    HISTORY_AWARE_BS = "history_aware_bs"
    DETERMINISTIC_ENHANCED = "deterministic_enhanced"
    UNIFIED = "unified"


@dataclass
class UnifiedModelParameters:
    """统一模型参数"""
    # 基础参数
    S0: float = 100.0           # 初始股价
    mu: float = 0.05            # 漂移率
    sigma: float = 0.2          # 波动率
    r: float = 0.03             # 无风险利率
    T: float = 1.0              # 到期时间
    
    # 模型选择
    primary_model: ModelType = ModelType.UNIFIED
    fallback_model: ModelType = ModelType.BLACK_SCHOLES
    
    # 权重配置
    classical_weight: float = 0.3      # 经典模型权重
    enhanced_weight: float = 0.4       # 增强模型权重
    deterministic_weight: float = 0.3  # 确定性模型权重
    
    # 历史数据配置
    lookback_days: int = 252
    calibration_window: int = 60
    update_frequency: int = 5
    
    # 高级参数
    enable_jumps: bool = True
    enable_mean_reversion: bool = True
    enable_volatility_clustering: bool = True
    
    # Heston参数
    V0: float = 0.04            # 初始方差
    kappa: float = 2.0          # 方差回归速度
    theta: float = 0.04         # 长期方差
    sigma_v: float = 0.3        # 方差的波动率
    rho: float = -0.7           # 相关系数


class UnifiedStochasticModel:
    """统一随机微积分模型"""
    
    def __init__(self, params: UnifiedModelParameters):
        self.params = params
        
        # 初始化各个子模型
        self._initialize_models()
        
        # 历史数据处理器
        self.data_processor = HistoricalDataProcessor(params.lookback_days)
        
        # 模型状态
        self.price_history = []
        self.model_weights = {
            'classical': params.classical_weight,
            'enhanced': params.enhanced_weight,
            'deterministic': params.deterministic_weight
        }
        
        # 性能跟踪
        self.model_performance = {
            'classical': [],
            'enhanced': [],
            'deterministic': []
        }
        
        # 校准状态
        self.last_calibration = None
        self.calibration_count = 0
    
    def _initialize_models(self):
        """初始化各个子模型"""
        # 经典Black-Scholes模型
        bs_params = BSParameters(
            S0=self.params.S0,
            mu=self.params.mu,
            sigma=self.params.sigma,
            r=self.params.r,
            T=self.params.T
        )
        self.bs_model = BlackScholesModel(bs_params)
        
        # Heston模型
        heston_params = HestonParameters(
            S0=self.params.S0,
            V0=self.params.V0,
            mu=self.params.mu,
            kappa=self.params.kappa,
            theta=self.params.theta,
            sigma_v=self.params.sigma_v,
            rho=self.params.rho,
            r=self.params.r,
            T=self.params.T
        )
        self.heston_model = HestonModel(heston_params)
        
        # 历史感知增强模型
        enhanced_params = EnhancedBSParameters(
            S0=self.params.S0,
            mu=self.params.mu,
            sigma=self.params.sigma,
            r=self.params.r,
            T=self.params.T
        )
        self.enhanced_model = HistoryAwareBlackScholesModel(enhanced_params, self.params.lookback_days)
        
        # 确定性增强模型
        det_params = EnhancedDeterministicParameters(
            S0=self.params.S0,
            mu=self.params.mu,
            sigma=self.params.sigma,
            r=self.params.r,
            T=self.params.T,
            deterministic_weight=0.7,
            stochastic_weight=0.3,
            calibration_window=self.params.calibration_window,
            update_frequency=self.params.update_frequency
        )
        self.deterministic_model = DeterministicEnhancedModel(det_params)
    
    def update_market_data(self, price: float, volume: float, timestamp: datetime, symbol: str = "default"):
        """更新市场数据"""
        self.price_history.append((timestamp, price))
        
        # 保持历史数据长度
        if len(self.price_history) > self.params.lookback_days * 2:
            self.price_history = self.price_history[-self.params.lookback_days:]
        
        # 更新各个子模型
        self.enhanced_model.update_market_data(price, volume, timestamp, symbol)
        self.deterministic_model.update_market_data(price, volume, timestamp)
        self.data_processor.update_history(symbol, price, volume, timestamp)
        
        # 定期重新校准和调整权重
        if len(self.price_history) % self.params.update_frequency == 0:
            self._recalibrate_models()
            self._adjust_model_weights()
    
    def _recalibrate_models(self):
        """重新校准所有模型"""
        if len(self.price_history) < 20:
            return
        
        # 准备历史数据
        df = pd.DataFrame(self.price_history, columns=['timestamp', 'close'])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp').sort_index()
        
        try:
            # 校准经典模型
            self.bs_model.params = self.bs_model.calibrate(df)
            
            # 校准Heston模型
            self.heston_model.params = self.heston_model.calibrate(df)
            
            self.last_calibration = datetime.now()
            self.calibration_count += 1
            
        except Exception as e:
            print(f"模型校准失败: {e}")
    
    def _adjust_model_weights(self):
        """动态调整模型权重"""
        if len(self.price_history) < 50:
            return
        
        # 计算各模型的预测准确性
        recent_prices = [p[1] for p in self.price_history[-20:]]
        
        # 简化的权重调整逻辑
        # 基于市场状态调整权重
        try:
            market_regime = self.data_processor.detect_market_regime("default")
            
            if market_regime.regime_type == "bull":
                # 牛市：增加经典模型权重
                self.model_weights['classical'] = 0.4
                self.model_weights['enhanced'] = 0.3
                self.model_weights['deterministic'] = 0.3
            elif market_regime.regime_type == "bear":
                # 熊市：增加确定性模型权重
                self.model_weights['classical'] = 0.2
                self.model_weights['enhanced'] = 0.3
                self.model_weights['deterministic'] = 0.5
            elif market_regime.regime_type == "volatile":
                # 震荡市：增加增强模型权重
                self.model_weights['classical'] = 0.2
                self.model_weights['enhanced'] = 0.5
                self.model_weights['deterministic'] = 0.3
            else:  # sideways
                # 横盘：平衡权重
                self.model_weights['classical'] = 0.3
                self.model_weights['enhanced'] = 0.4
                self.model_weights['deterministic'] = 0.3
                
        except Exception as e:
            print(f"权重调整失败: {e}")
    
    def simulate(self, n_paths: int, n_steps: int, model_type: Optional[ModelType] = None) -> np.ndarray:
        """
        统一的路径模拟
        
        Args:
            n_paths: 路径数量
            n_steps: 时间步数
            model_type: 指定模型类型，None表示使用组合模型
            
        Returns:
            模拟路径数组
        """
        if model_type is None:
            return self._simulate_combined(n_paths, n_steps)
        
        # 使用指定模型
        if model_type == ModelType.BLACK_SCHOLES:
            return self.bs_model.simulate(n_paths, n_steps)
        elif model_type == ModelType.HESTON:
            paths, _ = self.heston_model.simulate(n_paths, n_steps)
            return paths
        elif model_type == ModelType.HISTORY_AWARE_BS:
            return self.enhanced_model.simulate_with_history(n_paths, n_steps)
        elif model_type == ModelType.DETERMINISTIC_ENHANCED:
            return self.deterministic_model.simulate_deterministic_enhanced(n_paths, n_steps)
        else:
            return self._simulate_combined(n_paths, n_steps)
    
    def _simulate_combined(self, n_paths: int, n_steps: int) -> np.ndarray:
        """组合模型模拟"""
        # 分配路径数量
        n_classical = int(n_paths * self.model_weights['classical'])
        n_enhanced = int(n_paths * self.model_weights['enhanced'])
        n_deterministic = n_paths - n_classical - n_enhanced
        
        combined_paths = []
        
        # 经典模型路径
        if n_classical > 0:
            classical_paths = self.bs_model.simulate(n_classical, n_steps)
            combined_paths.append(classical_paths)
        
        # 增强模型路径
        if n_enhanced > 0:
            enhanced_paths = self.enhanced_model.simulate_with_history(n_enhanced, n_steps)
            combined_paths.append(enhanced_paths)
        
        # 确定性模型路径
        if n_deterministic > 0:
            det_paths = self.deterministic_model.simulate_deterministic_enhanced(n_deterministic, n_steps)
            combined_paths.append(det_paths)
        
        # 合并所有路径
        if combined_paths:
            return np.vstack(combined_paths)
        else:
            # 回退到经典模型
            return self.bs_model.simulate(n_paths, n_steps)
    
    def option_price(self, K: float, option_type: str = "call", method: str = "analytical") -> Dict[str, float]:
        """
        期权定价
        
        Args:
            K: 行权价
            option_type: 期权类型 ("call" 或 "put")
            method: 定价方法 ("analytical" 或 "monte_carlo")
            
        Returns:
            包含价格和其他信息的字典
        """
        if method == "analytical":
            # 使用Black-Scholes解析解
            bs_price = self.bs_model.option_price(K, option_type)
            
            # 应用模型调整
            adjustment_factor = self._calculate_price_adjustment()
            adjusted_price = bs_price * adjustment_factor
            
            return {
                'price': adjusted_price,
                'bs_price': bs_price,
                'adjustment_factor': adjustment_factor,
                'method': 'analytical_adjusted'
            }
        else:
            # 使用蒙特卡洛方法
            from .stochastic_models import monte_carlo_option_pricing
            
            # 使用组合模型进行蒙特卡洛定价
            paths = self.simulate(10000, 252)
            
            # 计算期权收益
            S_T = paths[:, -1]
            if option_type.lower() == "call":
                payoffs = np.maximum(S_T - K, 0)
            else:
                payoffs = np.maximum(K - S_T, 0)
            
            # 折现到现值
            option_price = np.exp(-self.params.r * self.params.T) * np.mean(payoffs)
            std_error = np.exp(-self.params.r * self.params.T) * np.std(payoffs) / np.sqrt(len(payoffs))
            
            return {
                'price': option_price,
                'std_error': std_error,
                'confidence_interval': [option_price - 1.96*std_error, option_price + 1.96*std_error],
                'method': 'monte_carlo_unified'
            }
    
    def _calculate_price_adjustment(self) -> float:
        """计算价格调整因子"""
        if len(self.price_history) < 20:
            return 1.0
        
        try:
            # 基于历史因素计算调整
            historical_factors = self.data_processor.calculate_historical_factors("default")
            
            # 综合调整因子
            trend_adj = 1 + historical_factors.trend_factor * 0.1
            vol_adj = 1 + (historical_factors.volatility_factor - 1) * 0.05
            sentiment_adj = 1 + historical_factors.sentiment_factor * 0.02
            
            return trend_adj * vol_adj * sentiment_adj
        except:
            return 1.0
    
    def get_model_diagnostics(self) -> Dict[str, Any]:
        """获取模型诊断信息"""
        diagnostics = {
            'unified_model': {
                'calibration_count': self.calibration_count,
                'last_calibration': self.last_calibration,
                'model_weights': self.model_weights.copy(),
                'price_history_length': len(self.price_history)
            },
            'classical_model': {
                'mu': self.bs_model.params.mu,
                'sigma': self.bs_model.params.sigma
            }
        }
        
        # 添加确定性模型诊断
        try:
            det_diagnostics = self.deterministic_model.get_model_diagnostics()
            diagnostics['deterministic_model'] = det_diagnostics
        except:
            pass
        
        return diagnostics
