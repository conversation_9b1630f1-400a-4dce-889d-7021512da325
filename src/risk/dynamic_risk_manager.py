"""
动态风险管理模块
支持动态资金池管理和临时套利策略
初始20万，后续根据总资金池70%控制，临时套利85%
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from loguru import logger


class TradingMode(Enum):
    """交易模式"""
    NORMAL = "normal"  # 正常模式 70%
    EMERGENCY_ARBITRAGE = "emergency"  # 临时套利模式 85%


class MarketCondition(Enum):
    """市场状况"""
    NORMAL = "normal"
    CRASH = "crash"  # 爆跌
    SURGE = "surge"   # 爆涨
    VOLATILE = "volatile"  # 高波动


@dataclass
class DynamicPositionLimits:
    """动态仓位限制配置"""
    initial_capital: float = 200000  # 初始资金20万
    normal_position_ratio: float = 0.70  # 正常模式70%
    emergency_position_ratio: float = 0.85  # 临时套利85%
    max_stock_count: int = 10  # 最多持仓10只股票
    min_position_value: float = 5000  # 单只股票最小仓位5000元
    max_single_position_ratio: float = 0.15  # 单只股票最大仓位15%
    normal_cash_reserve: float = 0.30  # 正常现金储备30%
    emergency_cash_reserve: float = 0.15  # 临时套利现金储备15%
    
    # 临时套利触发条件
    crash_threshold: float = -0.05  # 5%跌幅触发爆跌套利
    surge_threshold: float = 0.05   # 5%涨幅触发爆涨套利
    volatility_threshold: float = 0.03  # 3%波动率触发高频套利


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int
    avg_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    position_ratio: float
    entry_time: datetime
    is_emergency_position: bool = False  # 是否为临时套利仓位


@dataclass
class EmergencyArbitrageSignal:
    """临时套利信号"""
    signal_id: str
    trigger_time: datetime
    market_condition: MarketCondition
    trigger_symbol: str
    trigger_price: float
    trigger_change: float
    suggested_action: str
    confidence: float
    expected_duration: int  # 预期持续时间（分钟）
    max_position_ratio: float


class DynamicRiskManager:
    """动态风险管理器"""
    
    def __init__(self, limits: DynamicPositionLimits = None):
        self.limits = limits or DynamicPositionLimits()
        self.positions: Dict[str, Position] = {}
        self.available_cash = self.limits.initial_capital
        self.total_capital = self.limits.initial_capital  # 动态总资金池
        self.initial_capital = self.limits.initial_capital  # 初始资金（固定）
        
        # 交易模式管理
        self.current_mode = TradingMode.NORMAL
        self.emergency_start_time: Optional[datetime] = None
        self.emergency_signals: List[EmergencyArbitrageSignal] = []
        
        # 性能追踪
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_value = self.limits.initial_capital
        self.total_return = 0.0
        
        # 市场监控
        self.market_data_history = {}
        self.last_market_check = datetime.now()
        
        logger.info(f"初始化动态风险管理器: 初始资金={self.limits.initial_capital:,.0f}元")
        logger.info(f"正常模式仓位限制: {self.limits.normal_position_ratio:.0%}")
        logger.info(f"临时套利仓位限制: {self.limits.emergency_position_ratio:.0%}")
    
    def get_current_position_limit(self) -> float:
        """获取当前仓位限制"""
        if self.current_mode == TradingMode.EMERGENCY_ARBITRAGE:
            return self.limits.emergency_position_ratio
        return self.limits.normal_position_ratio
    
    def update_total_capital(self):
        """更新总资金池"""
        # 计算当前总价值
        position_value = sum(pos.market_value for pos in self.positions.values())
        self.total_capital = self.available_cash + position_value
        
        # 更新收益率
        self.total_return = (self.total_capital - self.initial_capital) / self.initial_capital
        
        # 更新最大回撤
        if self.total_capital > self.peak_value:
            self.peak_value = self.total_capital
        
        current_drawdown = (self.peak_value - self.total_capital) / self.peak_value
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
    
    def detect_market_condition(self, market_data: Dict[str, Dict]) -> MarketCondition:
        """检测市场状况"""
        if not market_data:
            return MarketCondition.NORMAL
        
        # 计算市场整体变化
        changes = []
        for symbol, data in market_data.items():
            if 'change' in data and data['change'] is not None:
                changes.append(data['change'])
        
        if not changes:
            return MarketCondition.NORMAL
        
        avg_change = np.mean(changes)
        volatility = np.std(changes)
        
        # 判断市场状况
        if avg_change <= self.limits.crash_threshold:
            return MarketCondition.CRASH
        elif avg_change >= self.limits.surge_threshold:
            return MarketCondition.SURGE
        elif volatility >= self.limits.volatility_threshold:
            return MarketCondition.VOLATILE
        else:
            return MarketCondition.NORMAL
    
    def should_trigger_emergency_arbitrage(self, market_data: Dict[str, Dict]) -> Tuple[bool, Optional[EmergencyArbitrageSignal]]:
        """判断是否应该触发临时套利"""
        market_condition = self.detect_market_condition(market_data)
        
        if market_condition == MarketCondition.NORMAL:
            return False, None
        
        # 如果已经在临时套利模式，检查是否应该继续
        if self.current_mode == TradingMode.EMERGENCY_ARBITRAGE:
            # 检查是否超过最大持续时间（30分钟）
            if (datetime.now() - self.emergency_start_time).total_seconds() > 1800:
                logger.info("临时套利超时，准备退出")
                return False, None
        
        # 生成临时套利信号
        signal = self._generate_emergency_signal(market_condition, market_data)
        return True, signal
    
    def _generate_emergency_signal(self, condition: MarketCondition, market_data: Dict[str, Dict]) -> EmergencyArbitrageSignal:
        """生成临时套利信号"""
        # 找到触发条件的股票
        trigger_symbol = None
        trigger_change = 0
        trigger_price = 0
        
        for symbol, data in market_data.items():
            change = data.get('change', 0)
            if abs(change) >= max(abs(self.limits.crash_threshold), abs(self.limits.surge_threshold)):
                trigger_symbol = symbol
                trigger_change = change
                trigger_price = data.get('price', 0)
                break
        
        # 确定建议操作
        if condition == MarketCondition.CRASH:
            suggested_action = "BUY_DIP"  # 抄底
            confidence = min(abs(trigger_change) / abs(self.limits.crash_threshold), 1.0)
        elif condition == MarketCondition.SURGE:
            suggested_action = "SELL_HIGH"  # 高抛
            confidence = min(abs(trigger_change) / abs(self.limits.surge_threshold), 1.0)
        else:
            suggested_action = "HEDGE"  # 对冲
            confidence = 0.7
        
        signal = EmergencyArbitrageSignal(
            signal_id=f"EMRG_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            trigger_time=datetime.now(),
            market_condition=condition,
            trigger_symbol=trigger_symbol or "MARKET",
            trigger_price=trigger_price,
            trigger_change=trigger_change,
            suggested_action=suggested_action,
            confidence=confidence,
            expected_duration=15,  # 15分钟
            max_position_ratio=self.limits.emergency_position_ratio
        )
        
        return signal
    
    def enter_emergency_mode(self, signal: EmergencyArbitrageSignal):
        """进入临时套利模式"""
        self.current_mode = TradingMode.EMERGENCY_ARBITRAGE
        self.emergency_start_time = datetime.now()
        self.emergency_signals.append(signal)
        
        logger.warning(f"🚨 进入临时套利模式: {signal.market_condition.value}")
        logger.warning(f"触发条件: {signal.trigger_symbol} 变化 {signal.trigger_change:.2%}")
        logger.warning(f"建议操作: {signal.suggested_action}")
        logger.warning(f"仓位限制提升至: {self.limits.emergency_position_ratio:.0%}")
    
    def exit_emergency_mode(self):
        """退出临时套利模式"""
        if self.current_mode == TradingMode.EMERGENCY_ARBITRAGE:
            duration = (datetime.now() - self.emergency_start_time).total_seconds() / 60
            
            # 标记临时套利仓位
            emergency_positions = [pos for pos in self.positions.values() if pos.is_emergency_position]
            
            self.current_mode = TradingMode.NORMAL
            self.emergency_start_time = None
            
            logger.info(f"✅ 退出临时套利模式，持续时间: {duration:.1f}分钟")
            logger.info(f"临时套利仓位数量: {len(emergency_positions)}")
            logger.info(f"仓位限制恢复至: {self.limits.normal_position_ratio:.0%}")
            
            # 检查是否需要减仓以符合正常模式限制
            self._check_position_compliance()
    
    def _check_position_compliance(self):
        """检查仓位合规性"""
        self.update_total_capital()
        current_position_value = sum(pos.market_value for pos in self.positions.values())
        current_ratio = current_position_value / self.total_capital
        max_allowed_ratio = self.get_current_position_limit()
        
        if current_ratio > max_allowed_ratio:
            excess_ratio = current_ratio - max_allowed_ratio
            excess_value = excess_ratio * self.total_capital
            
            logger.warning(f"⚠️ 仓位超限: 当前{current_ratio:.1%} > 限制{max_allowed_ratio:.1%}")
            logger.warning(f"需要减仓: ¥{excess_value:,.0f}")
            
            return False, f"需要减仓¥{excess_value:,.0f}以符合{max_allowed_ratio:.0%}限制"
        
        return True, "仓位合规"
    
    def get_portfolio_summary(self) -> Dict:
        """获取投资组合摘要"""
        self.update_total_capital()
        total_market_value = sum(pos.market_value for pos in self.positions.values())
        total_position_ratio = total_market_value / self.total_capital
        
        # 分别统计正常仓位和临时套利仓位
        normal_positions = [pos for pos in self.positions.values() if not pos.is_emergency_position]
        emergency_positions = [pos for pos in self.positions.values() if pos.is_emergency_position]
        
        normal_value = sum(pos.market_value for pos in normal_positions)
        emergency_value = sum(pos.market_value for pos in emergency_positions)
        
        return {
            'initial_capital': self.initial_capital,
            'total_capital': self.total_capital,
            'available_cash': self.available_cash,
            'total_market_value': total_market_value,
            'total_position_ratio': total_position_ratio,
            'normal_position_value': normal_value,
            'emergency_position_value': emergency_value,
            'position_count': len(self.positions),
            'normal_position_count': len(normal_positions),
            'emergency_position_count': len(emergency_positions),
            'cash_ratio': self.available_cash / self.total_capital,
            'total_pnl': sum(pos.unrealized_pnl for pos in self.positions.values()),
            'total_return': self.total_return,
            'daily_pnl': self.daily_pnl,
            'max_drawdown': self.max_drawdown,
            'current_mode': self.current_mode.value,
            'current_position_limit': self.get_current_position_limit(),
            'emergency_signals_count': len(self.emergency_signals)
        }
