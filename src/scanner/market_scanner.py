"""
沪深A全市场套利机会扫描器
支持多线程并行计算，最多80线程
"""

import pandas as pd
import numpy as np
import asyncio
import aiohttp
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List, Tuple, Optional, Any
import json
import os
from dataclasses import dataclass
from queue import Queue
import multiprocessing

@dataclass
class ArbitrageOpportunity:
    """套利机会数据结构"""
    strategy_type: str
    symbols: List[str]
    confidence: float
    expected_return: float
    risk_level: str
    entry_price: Dict[str, float]
    target_price: Dict[str, float]
    stop_loss: Dict[str, float]
    position_size: float
    scan_time: datetime
    market_data: Dict[str, Any]
    technical_indicators: Dict[str, Any]
    
class MarketScanner:
    """沪深A全市场扫描器"""
    
    def __init__(self, max_threads: int = 80):
        """
        初始化市场扫描器
        
        Args:
            max_threads: 最大线程数，默认80
        """
        self.max_threads = min(max_threads, 80)  # 限制最大80线程
        self.logger = self._setup_logger()
        
        # 扫描配置
        self.scan_config = self._load_scan_config()
        
        # 股票池
        self.stock_pool = []
        self.market_data_cache = {}
        
        # 扫描状态
        self.is_scanning = False
        self.scan_progress = 0
        self.scan_results = []
        self.scan_statistics = {}
        
        # 线程池
        self.thread_pool = None
        
        # 策略配置
        self.strategies = {
            'pair_trading': self._scan_pair_trading,
            'statistical_arbitrage': self._scan_statistical_arbitrage,
            'momentum_arbitrage': self._scan_momentum_arbitrage,
            'mean_reversion': self._scan_mean_reversion,
            'volatility_arbitrage': self._scan_volatility_arbitrage,
            'cross_market': self._scan_cross_market,
            'sector_rotation': self._scan_sector_rotation,
            'event_driven': self._scan_event_driven
        }
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('MarketScanner')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_scan_config(self) -> Dict:
        """加载扫描配置"""
        default_config = {
            "scan_strategies": [
                "pair_trading",
                "statistical_arbitrage", 
                "momentum_arbitrage",
                "mean_reversion",
                "volatility_arbitrage"
            ],
            "filters": {
                "min_market_cap": 1000000000,  # 10亿市值
                "min_volume": 1000000,         # 100万成交量
                "max_price": 1000,             # 最高价格
                "min_price": 1,                # 最低价格
                "exclude_st": True,            # 排除ST股票
                "exclude_suspended": True      # 排除停牌股票
            },
            "thresholds": {
                "min_confidence": 0.6,
                "min_expected_return": 0.01,
                "max_risk_level": "HIGH"
            },
            "technical_indicators": {
                "rsi_period": 14,
                "ma_periods": [5, 10, 20, 60],
                "bollinger_period": 20,
                "macd_periods": [12, 26, 9]
            }
        }
        
        config_path = "config/scan_config.json"
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                # 创建默认配置文件
                os.makedirs(os.path.dirname(config_path), exist_ok=True)
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                return default_config
        except Exception as e:
            self.logger.warning(f"配置文件加载失败，使用默认配置: {e}")
            return default_config
    
    def load_stock_pool(self, data_source: str = "file") -> List[str]:
        """
        加载股票池
        
        Args:
            data_source: 数据源类型 ("file", "api", "database")
            
        Returns:
            股票代码列表
        """
        try:
            if data_source == "file":
                return self._load_stock_pool_from_file()
            elif data_source == "api":
                return self._load_stock_pool_from_api()
            else:
                return self._generate_mock_stock_pool()
        except Exception as e:
            self.logger.error(f"加载股票池失败: {e}")
            return self._generate_mock_stock_pool()
    
    def _load_stock_pool_from_file(self) -> List[str]:
        """从文件加载股票池"""
        file_path = "data/stock_pool/a_stock_list.csv"
        
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            return df['symbol'].tolist()
        else:
            # 创建示例股票池文件
            self._create_sample_stock_pool()
            return self._generate_mock_stock_pool()
    
    def _create_sample_stock_pool(self):
        """创建示例股票池文件"""
        os.makedirs("data/stock_pool", exist_ok=True)
        
        # 生成示例股票池（沪深A股主要股票）
        sample_stocks = []
        
        # 沪市主板
        for i in range(600001, 600100):
            sample_stocks.append(f"{i}.SH")
        
        # 深市主板
        for i in range(1, 100):
            sample_stocks.append(f"{i:06d}.SZ")
        
        # 创业板
        for i in range(300001, 300100):
            sample_stocks.append(f"{i}.SZ")
        
        # 科创板
        for i in range(688001, 688100):
            sample_stocks.append(f"{i}.SH")
        
        df = pd.DataFrame({
            'symbol': sample_stocks,
            'name': [f"股票{i}" for i in range(len(sample_stocks))],
            'market': ['SH' if '.SH' in s else 'SZ' for s in sample_stocks]
        })
        
        df.to_csv("data/stock_pool/a_stock_list.csv", index=False)
        self.logger.info(f"创建示例股票池文件，包含{len(sample_stocks)}只股票")
    
    def _generate_mock_stock_pool(self) -> List[str]:
        """生成模拟股票池"""
        mock_stocks = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '002594.SZ',
            '300750.SZ', '600000.SH', '600036.SH', '600519.SH', '688001.SH'
        ]
        
        # 扩展到更多股票用于测试
        extended_stocks = []
        for i in range(1, 201):  # 200只股票
            if i <= 100:
                extended_stocks.append(f"{i:06d}.SZ")
            else:
                extended_stocks.append(f"{600000 + i - 100}.SH")
        
        return mock_stocks + extended_stocks
    
    async def scan_market_async(self, strategies: List[str] = None) -> List[ArbitrageOpportunity]:
        """
        异步扫描市场套利机会
        
        Args:
            strategies: 要使用的策略列表
            
        Returns:
            套利机会列表
        """
        if self.is_scanning:
            self.logger.warning("扫描正在进行中，请等待完成")
            return []
        
        self.is_scanning = True
        self.scan_progress = 0
        self.scan_results = []
        
        try:
            # 加载股票池
            if not self.stock_pool:
                self.stock_pool = self.load_stock_pool()
            
            # 使用指定策略或默认策略
            if strategies is None:
                strategies = self.scan_config['scan_strategies']
            
            self.logger.info(f"开始扫描市场，股票池大小: {len(self.stock_pool)}, 策略: {strategies}")
            
            # 分批处理股票
            batch_size = max(1, len(self.stock_pool) // self.max_threads)
            stock_batches = [
                self.stock_pool[i:i + batch_size] 
                for i in range(0, len(self.stock_pool), batch_size)
            ]
            
            # 创建线程池
            with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
                # 提交扫描任务
                futures = []
                for batch_idx, stock_batch in enumerate(stock_batches):
                    for strategy in strategies:
                        future = executor.submit(
                            self._scan_batch,
                            stock_batch,
                            strategy,
                            batch_idx
                        )
                        futures.append(future)
                
                # 收集结果
                completed_tasks = 0
                total_tasks = len(futures)
                
                for future in as_completed(futures):
                    try:
                        batch_results = future.result()
                        self.scan_results.extend(batch_results)
                        
                        completed_tasks += 1
                        self.scan_progress = (completed_tasks / total_tasks) * 100
                        
                        if completed_tasks % 10 == 0:
                            self.logger.info(f"扫描进度: {self.scan_progress:.1f}%")
                            
                    except Exception as e:
                        self.logger.error(f"批次扫描失败: {e}")
            
            # 过滤和排序结果
            filtered_results = self._filter_and_rank_results(self.scan_results)
            
            # 更新统计信息
            self._update_scan_statistics(filtered_results)
            
            self.logger.info(f"扫描完成，发现{len(filtered_results)}个套利机会")
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"市场扫描失败: {e}")
            return []
        finally:
            self.is_scanning = False
    
    def _scan_batch(self, stock_batch: List[str], strategy: str, batch_idx: int) -> List[ArbitrageOpportunity]:
        """
        扫描股票批次
        
        Args:
            stock_batch: 股票批次
            strategy: 策略名称
            batch_idx: 批次索引
            
        Returns:
            套利机会列表
        """
        try:
            thread_id = threading.current_thread().ident
            self.logger.debug(f"线程{thread_id}开始扫描批次{batch_idx}，策略{strategy}，股票数{len(stock_batch)}")
            
            batch_results = []
            
            # 获取策略函数
            strategy_func = self.strategies.get(strategy)
            if not strategy_func:
                self.logger.warning(f"未知策略: {strategy}")
                return []
            
            # 扫描每只股票
            for symbol in stock_batch:
                try:
                    # 获取市场数据
                    market_data = self._get_market_data(symbol)
                    if not market_data:
                        continue
                    
                    # 应用策略
                    opportunities = strategy_func(symbol, market_data)
                    batch_results.extend(opportunities)
                    
                except Exception as e:
                    self.logger.debug(f"扫描股票{symbol}失败: {e}")
                    continue
            
            self.logger.debug(f"线程{thread_id}完成批次{batch_idx}，发现{len(batch_results)}个机会")
            return batch_results
            
        except Exception as e:
            self.logger.error(f"批次扫描失败: {e}")
            return []
    
    def _get_market_data(self, symbol: str) -> Optional[Dict]:
        """获取市场数据"""
        try:
            # 检查缓存
            if symbol in self.market_data_cache:
                cache_time = self.market_data_cache[symbol].get('timestamp', datetime.min)
                if (datetime.now() - cache_time).seconds < 60:  # 1分钟缓存
                    return self.market_data_cache[symbol]
            
            # 生成模拟市场数据
            market_data = self._generate_mock_market_data(symbol)
            
            # 更新缓存
            market_data['timestamp'] = datetime.now()
            self.market_data_cache[symbol] = market_data
            
            return market_data
            
        except Exception as e:
            self.logger.debug(f"获取{symbol}市场数据失败: {e}")
            return None
    
    def _generate_mock_market_data(self, symbol: str) -> Dict:
        """生成模拟市场数据"""
        np.random.seed(hash(symbol) % 2**32)  # 基于股票代码的固定随机种子
        
        base_price = np.random.uniform(5, 100)
        
        return {
            'symbol': symbol,
            'current_price': base_price,
            'open_price': base_price * np.random.uniform(0.98, 1.02),
            'high_price': base_price * np.random.uniform(1.0, 1.05),
            'low_price': base_price * np.random.uniform(0.95, 1.0),
            'volume': np.random.randint(100000, 10000000),
            'turnover': base_price * np.random.randint(100000, 10000000),
            'change_pct': np.random.normal(0, 0.03),
            'market_cap': base_price * np.random.randint(100000000, 1000000000),
            'pe_ratio': np.random.uniform(10, 50),
            'pb_ratio': np.random.uniform(1, 10),
            'rsi': np.random.uniform(20, 80),
            'ma5': base_price * np.random.uniform(0.98, 1.02),
            'ma20': base_price * np.random.uniform(0.95, 1.05),
            'bollinger_upper': base_price * 1.1,
            'bollinger_lower': base_price * 0.9,
            'macd': np.random.normal(0, 0.5),
            'volatility': np.random.uniform(0.1, 0.5)
        }
    
    def _scan_pair_trading(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """配对交易策略扫描"""
        opportunities = []
        
        try:
            # 寻找相关股票进行配对
            correlation_threshold = 0.7
            price_divergence_threshold = 0.05
            
            # 模拟配对逻辑
            if market_data['rsi'] > 70 and market_data['change_pct'] > 0.03:
                # 寻找相关但表现不同的股票
                pair_symbol = self._find_pair_symbol(symbol, market_data)
                if pair_symbol:
                    confidence = min(0.95, 0.6 + abs(market_data['change_pct']) * 10)
                    expected_return = abs(market_data['change_pct']) * 0.5
                    
                    opportunity = ArbitrageOpportunity(
                        strategy_type="pair_trading",
                        symbols=[symbol, pair_symbol],
                        confidence=confidence,
                        expected_return=expected_return,
                        risk_level="MEDIUM",
                        entry_price={symbol: market_data['current_price']},
                        target_price={symbol: market_data['current_price'] * (1 + expected_return)},
                        stop_loss={symbol: market_data['current_price'] * 0.98},
                        position_size=0.05,
                        scan_time=datetime.now(),
                        market_data=market_data,
                        technical_indicators={'rsi': market_data['rsi']}
                    )
                    opportunities.append(opportunity)
            
        except Exception as e:
            self.logger.debug(f"配对交易扫描失败 {symbol}: {e}")
        
        return opportunities
    
    def _scan_statistical_arbitrage(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """统计套利策略扫描"""
        opportunities = []
        
        try:
            # 统计套利信号
            if (market_data['current_price'] < market_data['bollinger_lower'] and 
                market_data['rsi'] < 30):
                
                confidence = 0.6 + (30 - market_data['rsi']) / 100
                expected_return = 0.02 + abs(market_data['change_pct']) * 0.5
                
                opportunity = ArbitrageOpportunity(
                    strategy_type="statistical_arbitrage",
                    symbols=[symbol],
                    confidence=confidence,
                    expected_return=expected_return,
                    risk_level="LOW",
                    entry_price={symbol: market_data['current_price']},
                    target_price={symbol: market_data['bollinger_upper']},
                    stop_loss={symbol: market_data['current_price'] * 0.95},
                    position_size=0.03,
                    scan_time=datetime.now(),
                    market_data=market_data,
                    technical_indicators={'rsi': market_data['rsi'], 'bollinger': True}
                )
                opportunities.append(opportunity)
                
        except Exception as e:
            self.logger.debug(f"统计套利扫描失败 {symbol}: {e}")
        
        return opportunities
    
    def _scan_momentum_arbitrage(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """动量套利策略扫描"""
        opportunities = []
        
        try:
            # 动量信号
            if (market_data['change_pct'] > 0.05 and 
                market_data['volume'] > 5000000 and
                market_data['current_price'] > market_data['ma20']):
                
                confidence = min(0.9, 0.7 + market_data['change_pct'] * 5)
                expected_return = market_data['change_pct'] * 0.3
                
                opportunity = ArbitrageOpportunity(
                    strategy_type="momentum_arbitrage",
                    symbols=[symbol],
                    confidence=confidence,
                    expected_return=expected_return,
                    risk_level="HIGH",
                    entry_price={symbol: market_data['current_price']},
                    target_price={symbol: market_data['current_price'] * 1.08},
                    stop_loss={symbol: market_data['current_price'] * 0.96},
                    position_size=0.04,
                    scan_time=datetime.now(),
                    market_data=market_data,
                    technical_indicators={'momentum': True}
                )
                opportunities.append(opportunity)
                
        except Exception as e:
            self.logger.debug(f"动量套利扫描失败 {symbol}: {e}")
        
        return opportunities
    
    def _scan_mean_reversion(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """均值回归策略扫描"""
        opportunities = []
        
        try:
            # 均值回归信号
            price_deviation = (market_data['current_price'] - market_data['ma20']) / market_data['ma20']
            
            if abs(price_deviation) > 0.1:
                confidence = min(0.85, 0.6 + abs(price_deviation) * 2)
                expected_return = abs(price_deviation) * 0.5
                
                opportunity = ArbitrageOpportunity(
                    strategy_type="mean_reversion",
                    symbols=[symbol],
                    confidence=confidence,
                    expected_return=expected_return,
                    risk_level="MEDIUM",
                    entry_price={symbol: market_data['current_price']},
                    target_price={symbol: market_data['ma20']},
                    stop_loss={symbol: market_data['current_price'] * (0.97 if price_deviation > 0 else 1.03)},
                    position_size=0.03,
                    scan_time=datetime.now(),
                    market_data=market_data,
                    technical_indicators={'mean_reversion': True, 'deviation': price_deviation}
                )
                opportunities.append(opportunity)
                
        except Exception as e:
            self.logger.debug(f"均值回归扫描失败 {symbol}: {e}")
        
        return opportunities
    
    def _scan_volatility_arbitrage(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """波动率套利策略扫描"""
        opportunities = []
        
        try:
            # 波动率套利信号
            if market_data['volatility'] > 0.3 and abs(market_data['change_pct']) > 0.04:
                confidence = min(0.8, 0.5 + market_data['volatility'])
                expected_return = market_data['volatility'] * 0.1
                
                opportunity = ArbitrageOpportunity(
                    strategy_type="volatility_arbitrage",
                    symbols=[symbol],
                    confidence=confidence,
                    expected_return=expected_return,
                    risk_level="HIGH",
                    entry_price={symbol: market_data['current_price']},
                    target_price={symbol: market_data['current_price'] * (1 + expected_return)},
                    stop_loss={symbol: market_data['current_price'] * 0.95},
                    position_size=0.02,
                    scan_time=datetime.now(),
                    market_data=market_data,
                    technical_indicators={'volatility': market_data['volatility']}
                )
                opportunities.append(opportunity)
                
        except Exception as e:
            self.logger.debug(f"波动率套利扫描失败 {symbol}: {e}")
        
        return opportunities
    
    def _scan_cross_market(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """跨市场套利策略扫描"""
        # 简化实现
        return []
    
    def _scan_sector_rotation(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """板块轮动策略扫描"""
        # 简化实现
        return []
    
    def _scan_event_driven(self, symbol: str, market_data: Dict) -> List[ArbitrageOpportunity]:
        """事件驱动策略扫描"""
        # 简化实现
        return []
    
    def _find_pair_symbol(self, symbol: str, market_data: Dict) -> Optional[str]:
        """寻找配对股票"""
        # 简化实现：随机选择一个相关股票
        if len(self.stock_pool) > 1:
            candidates = [s for s in self.stock_pool if s != symbol]
            return np.random.choice(candidates) if candidates else None
        return None
    
    def _filter_and_rank_results(self, results: List[ArbitrageOpportunity]) -> List[ArbitrageOpportunity]:
        """过滤和排序结果"""
        try:
            # 应用过滤条件
            filtered = []
            thresholds = self.scan_config['thresholds']
            
            for opportunity in results:
                if (opportunity.confidence >= thresholds['min_confidence'] and
                    opportunity.expected_return >= thresholds['min_expected_return']):
                    filtered.append(opportunity)
            
            # 按置信度和预期收益排序
            filtered.sort(key=lambda x: (x.confidence, x.expected_return), reverse=True)
            
            # 限制结果数量
            return filtered[:100]  # 返回前100个机会
            
        except Exception as e:
            self.logger.error(f"结果过滤失败: {e}")
            return results
    
    def _update_scan_statistics(self, results: List[ArbitrageOpportunity]):
        """更新扫描统计信息"""
        try:
            self.scan_statistics = {
                'total_opportunities': len(results),
                'strategies': {},
                'risk_levels': {},
                'avg_confidence': np.mean([r.confidence for r in results]) if results else 0,
                'avg_expected_return': np.mean([r.expected_return for r in results]) if results else 0,
                'scan_time': datetime.now(),
                'stocks_scanned': len(self.stock_pool),
                'threads_used': self.max_threads
            }
            
            # 按策略统计
            for result in results:
                strategy = result.strategy_type
                if strategy not in self.scan_statistics['strategies']:
                    self.scan_statistics['strategies'][strategy] = 0
                self.scan_statistics['strategies'][strategy] += 1
            
            # 按风险等级统计
            for result in results:
                risk = result.risk_level
                if risk not in self.scan_statistics['risk_levels']:
                    self.scan_statistics['risk_levels'][risk] = 0
                self.scan_statistics['risk_levels'][risk] += 1
                
        except Exception as e:
            self.logger.error(f"统计信息更新失败: {e}")
    
    def get_scan_status(self) -> Dict:
        """获取扫描状态"""
        return {
            'is_scanning': self.is_scanning,
            'progress': self.scan_progress,
            'results_count': len(self.scan_results),
            'statistics': self.scan_statistics,
            'max_threads': self.max_threads,
            'stock_pool_size': len(self.stock_pool)
        }
    
    def stop_scan(self):
        """停止扫描"""
        self.is_scanning = False
        self.logger.info("扫描已停止")
    
    def clear_cache(self):
        """清空缓存"""
        self.market_data_cache.clear()
        self.logger.info("缓存已清空")
