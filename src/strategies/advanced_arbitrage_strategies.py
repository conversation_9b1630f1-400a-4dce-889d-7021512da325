"""
高级套利策略模块
实现复杂的套利算法，包括机器学习增强策略、多因子模型等
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import itertools
from scipy import stats
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

# 简化的基类定义，避免循环导入
class BaseArbitrageStrategy:
    """套利策略基类"""
    def __init__(self, name: str, strategy_type):
        self.name = name
        self.strategy_type = strategy_type

    def scan_opportunities(self, market_data):
        raise NotImplementedError

    def _is_valid_pair(self, symbol1: str, symbol2: str) -> bool:
        """检查是否为有效的交易对"""
        return symbol1 != symbol2 and symbol1 and symbol2

    def _calculate_signal_strength(self, score: float):
        """计算信号强度"""
        if score > 0.8:
            return "VERY_STRONG"
        elif score > 0.6:
            return "STRONG"
        elif score > 0.4:
            return "MODERATE"
        else:
            return "WEAK"

@dataclass
class ArbitrageOpportunity:
    """套利机会"""
    strategy_type: Any
    timestamp: datetime
    symbols: List[str]
    signal_strength: Any
    confidence: float
    expected_return: float
    expected_risk: float
    holding_period: int
    entry_prices: Dict[str, float]
    target_weights: Dict[str, float]
    stop_loss: float
    take_profit: float
    market_conditions: Dict[str, Any]
    strategy_params: Dict[str, Any]


class AdvancedArbitrageType(Enum):
    """高级套利类型"""
    ML_ENHANCED_PAIRS = "ml_enhanced_pairs"         # 机器学习增强配对交易
    MULTI_FACTOR_ARBITRAGE = "multi_factor_arb"     # 多因子套利
    REGIME_SWITCHING = "regime_switching"           # 市场状态切换套利
    COINTEGRATION_ENHANCED = "coint_enhanced"       # 增强协整套利
    VOLATILITY_SURFACE = "vol_surface_arb"          # 波动率曲面套利
    MOMENTUM_REVERSAL = "momentum_reversal"         # 动量反转套利
    CROSS_SECTIONAL = "cross_sectional"             # 横截面套利
    INTRADAY_PATTERNS = "intraday_patterns"         # 日内模式套利


@dataclass
class MLFeatures:
    """机器学习特征"""
    price_ratio: float
    volume_ratio: float
    volatility_ratio: float
    correlation_5d: float
    correlation_20d: float
    rsi_diff: float
    macd_diff: float
    bollinger_position: float
    momentum_5d: float
    momentum_20d: float
    sector_performance: float
    market_beta_diff: float


@dataclass
class MarketRegime:
    """市场状态"""
    regime_type: str  # "bull", "bear", "sideways", "volatile"
    confidence: float
    volatility_level: float
    trend_strength: float
    last_update: datetime


class MLEnhancedPairsStrategy(BaseArbitrageStrategy):
    """机器学习增强配对交易策略"""

    def __init__(self, lookback_period: int = 120, retrain_frequency: int = 30):
        super().__init__("MLEnhancedPairs", AdvancedArbitrageType.ML_ENHANCED_PAIRS)
        self.lookback_period = lookback_period
        self.retrain_frequency = retrain_frequency

        # 机器学习模型
        self.price_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.direction_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()

        # 模型状态
        self.is_trained = False
        self.last_training = None
        self.feature_importance = {}

        # 历史数据存储
        self.price_history = {}
        self.feature_history = []
        self.target_history = []

        logger.info("初始化机器学习增强配对交易策略")

    def scan_opportunities(self, market_data: Dict[str, Any]) -> List[ArbitrageOpportunity]:
        """扫描套利机会"""
        opportunities = []

        # 更新历史数据
        self._update_price_history(market_data)

        # 检查是否需要重新训练模型
        if self._should_retrain():
            self._train_models()

        if not self.is_trained:
            logger.warning("ML模型未训练，使用传统方法")
            return self._fallback_traditional_pairs(market_data)

        # 生成特征并预测
        symbols = list(market_data.keys())
        for symbol1, symbol2 in itertools.combinations(symbols, 2):
            if self._is_valid_pair(symbol1, symbol2):
                opportunity = self._analyze_ml_pair(symbol1, symbol2, market_data)
                if opportunity:
                    opportunities.append(opportunity)

        return opportunities

    def _update_price_history(self, market_data: Dict[str, Any]):
        """更新价格历史"""
        current_time = datetime.now()

        for symbol, data in market_data.items():
            if symbol not in self.price_history:
                self.price_history[symbol] = []

            price_record = {
                'timestamp': current_time,
                'price': data.get('price', 0),
                'volume': data.get('volume', 0),
                'change': data.get('change', 0)
            }

            self.price_history[symbol].append(price_record)

            # 保持历史数据在合理范围内
            if len(self.price_history[symbol]) > self.lookback_period * 2:
                self.price_history[symbol] = self.price_history[symbol][-self.lookback_period:]

    def _should_retrain(self) -> bool:
        """判断是否需要重新训练"""
        if not self.is_trained:
            return True

        if self.last_training is None:
            return True

        days_since_training = (datetime.now() - self.last_training).days
        return days_since_training >= self.retrain_frequency

    def _extract_features(self, symbol1: str, symbol2: str) -> Optional[MLFeatures]:
        """提取机器学习特征"""
        if (symbol1 not in self.price_history or symbol2 not in self.price_history or
            len(self.price_history[symbol1]) < 30 or len(self.price_history[symbol2]) < 30):
            return None

        try:
            # 获取价格序列
            prices1 = [record['price'] for record in self.price_history[symbol1][-30:]]
            prices2 = [record['price'] for record in self.price_history[symbol2][-30:]]
            volumes1 = [record['volume'] for record in self.price_history[symbol1][-30:]]
            volumes2 = [record['volume'] for record in self.price_history[symbol2][-30:]]

            # 计算特征
            price_ratio = prices1[-1] / prices2[-1] if prices2[-1] != 0 else 1.0
            volume_ratio = np.mean(volumes1[-5:]) / np.mean(volumes2[-5:]) if np.mean(volumes2[-5:]) != 0 else 1.0

            # 波动率比率
            vol1 = np.std(prices1[-20:]) / np.mean(prices1[-20:]) if np.mean(prices1[-20:]) != 0 else 0
            vol2 = np.std(prices2[-20:]) / np.mean(prices2[-20:]) if np.mean(prices2[-20:]) != 0 else 0
            volatility_ratio = vol1 / vol2 if vol2 != 0 else 1.0

            # 相关性
            correlation_5d = np.corrcoef(prices1[-5:], prices2[-5:])[0, 1] if len(prices1) >= 5 else 0
            correlation_20d = np.corrcoef(prices1[-20:], prices2[-20:])[0, 1] if len(prices1) >= 20 else 0

            # RSI差异（简化计算）
            rsi1 = self._calculate_rsi(prices1)
            rsi2 = self._calculate_rsi(prices2)
            rsi_diff = rsi1 - rsi2

            # 动量
            momentum_5d = (prices1[-1] / prices1[-6] - 1) if len(prices1) >= 6 else 0
            momentum_20d = (prices1[-1] / prices1[-21] - 1) if len(prices1) >= 21 else 0

            return MLFeatures(
                price_ratio=price_ratio,
                volume_ratio=volume_ratio,
                volatility_ratio=volatility_ratio,
                correlation_5d=correlation_5d,
                correlation_20d=correlation_20d,
                rsi_diff=rsi_diff,
                macd_diff=0.0,  # 简化
                bollinger_position=0.0,  # 简化
                momentum_5d=momentum_5d,
                momentum_20d=momentum_20d,
                sector_performance=0.0,  # 简化
                market_beta_diff=0.0  # 简化
            )

        except Exception as e:
            logger.warning(f"特征提取失败: {e}")
            return None

    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _train_models(self):
        """训练机器学习模型"""
        try:
            # 准备训练数据
            X, y_price, y_direction = self._prepare_training_data()

            if len(X) < 50:  # 需要足够的训练数据
                logger.warning("训练数据不足，跳过模型训练")
                return

            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)

            # 训练价格预测模型
            X_train, X_test, y_train, y_test = train_test_split(X_scaled, y_price, test_size=0.2, random_state=42)
            self.price_model.fit(X_train, y_train)

            # 训练方向预测模型
            self.direction_model.fit(X_train, y_direction)

            # 计算特征重要性
            feature_names = [
                'price_ratio', 'volume_ratio', 'volatility_ratio', 'correlation_5d',
                'correlation_20d', 'rsi_diff', 'momentum_5d', 'momentum_20d'
            ]

            self.feature_importance = dict(zip(feature_names, self.price_model.feature_importances_))

            self.is_trained = True
            self.last_training = datetime.now()

            logger.info(f"ML模型训练完成，训练样本数: {len(X)}")
            logger.info(f"特征重要性: {self.feature_importance}")

        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            self.is_trained = False

    def _prepare_training_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """准备训练数据"""
        X = []
        y_price = []
        y_direction = []

        # 从历史数据中构建训练样本
        symbols = list(self.price_history.keys())

        for symbol1, symbol2 in itertools.combinations(symbols, 2):
            if (len(self.price_history[symbol1]) >= 50 and
                len(self.price_history[symbol2]) >= 50):

                # 生成时间序列样本
                for i in range(30, min(len(self.price_history[symbol1]), len(self.price_history[symbol2])) - 5):
                    # 构建特征（使用历史数据）
                    hist_data1 = self.price_history[symbol1][i-30:i]
                    hist_data2 = self.price_history[symbol2][i-30:i]

                    features = self._extract_historical_features(hist_data1, hist_data2)
                    if features is None:
                        continue

                    # 构建目标变量（未来5期的价格变化）
                    future_data1 = self.price_history[symbol1][i:i+5]
                    future_data2 = self.price_history[symbol2][i:i+5]

                    if len(future_data1) < 5 or len(future_data2) < 5:
                        continue

                    # 价格比率变化
                    current_ratio = hist_data1[-1]['price'] / hist_data2[-1]['price']
                    future_ratio = future_data1[-1]['price'] / future_data2[-1]['price']
                    price_change = (future_ratio - current_ratio) / current_ratio

                    # 方向（1: 上涨, 0: 下跌）
                    direction = 1 if price_change > 0 else 0

                    X.append([
                        features.price_ratio, features.volume_ratio, features.volatility_ratio,
                        features.correlation_5d, features.correlation_20d, features.rsi_diff,
                        features.momentum_5d, features.momentum_20d
                    ])
                    y_price.append(price_change)
                    y_direction.append(direction)

        return np.array(X), np.array(y_price), np.array(y_direction)

    def _extract_historical_features(self, hist_data1: List[Dict], hist_data2: List[Dict]) -> Optional[MLFeatures]:
        """从历史数据提取特征"""
        try:
            prices1 = [record['price'] for record in hist_data1]
            prices2 = [record['price'] for record in hist_data2]
            volumes1 = [record['volume'] for record in hist_data1]
            volumes2 = [record['volume'] for record in hist_data2]

            price_ratio = prices1[-1] / prices2[-1] if prices2[-1] != 0 else 1.0
            volume_ratio = np.mean(volumes1[-5:]) / np.mean(volumes2[-5:]) if np.mean(volumes2[-5:]) != 0 else 1.0

            vol1 = np.std(prices1[-20:]) / np.mean(prices1[-20:]) if np.mean(prices1[-20:]) != 0 else 0
            vol2 = np.std(prices2[-20:]) / np.mean(prices2[-20:]) if np.mean(prices2[-20:]) != 0 else 0
            volatility_ratio = vol1 / vol2 if vol2 != 0 else 1.0

            correlation_5d = np.corrcoef(prices1[-5:], prices2[-5:])[0, 1] if len(prices1) >= 5 else 0
            correlation_20d = np.corrcoef(prices1[-20:], prices2[-20:])[0, 1] if len(prices1) >= 20 else 0

            rsi1 = self._calculate_rsi(prices1)
            rsi2 = self._calculate_rsi(prices2)
            rsi_diff = rsi1 - rsi2

            momentum_5d = (prices1[-1] / prices1[-6] - 1) if len(prices1) >= 6 else 0
            momentum_20d = (prices1[-1] / prices1[-21] - 1) if len(prices1) >= 21 else 0

            return MLFeatures(
                price_ratio=price_ratio,
                volume_ratio=volume_ratio,
                volatility_ratio=volatility_ratio,
                correlation_5d=correlation_5d,
                correlation_20d=correlation_20d,
                rsi_diff=rsi_diff,
                macd_diff=0.0,
                bollinger_position=0.0,
                momentum_5d=momentum_5d,
                momentum_20d=momentum_20d,
                sector_performance=0.0,
                market_beta_diff=0.0
            )

        except Exception as e:
            return None

    def _analyze_ml_pair(self, symbol1: str, symbol2: str, market_data: Dict[str, Any]) -> Optional[ArbitrageOpportunity]:
        """使用机器学习分析股票对"""
        features = self._extract_features(symbol1, symbol2)
        if features is None:
            return None

        try:
            # 准备特征向量
            feature_vector = np.array([[
                features.price_ratio, features.volume_ratio, features.volatility_ratio,
                features.correlation_5d, features.correlation_20d, features.rsi_diff,
                features.momentum_5d, features.momentum_20d
            ]])

            # 标准化特征
            feature_vector_scaled = self.scaler.transform(feature_vector)

            # 预测价格变化和方向
            predicted_change = self.price_model.predict(feature_vector_scaled)[0]
            predicted_direction = self.direction_model.predict(feature_vector_scaled)[0]

            # 计算置信度
            confidence = min(abs(predicted_change) * 10, 0.95)  # 基于预测变化幅度

            # 判断是否生成信号
            if abs(predicted_change) > 0.01 and confidence > 0.6:  # 1%以上变化且置信度>60%
                price1 = market_data[symbol1]['price']
                price2 = market_data[symbol2]['price']

                # 确定交易方向
                if predicted_change > 0:  # 比率上升，做多symbol1，做空symbol2
                    weights = {symbol1: 0.5, symbol2: -0.5}
                    signal_type = "LONG_SHORT"
                else:  # 比率下降，做空symbol1，做多symbol2
                    weights = {symbol1: -0.5, symbol2: 0.5}
                    signal_type = "SHORT_LONG"

                return ArbitrageOpportunity(
                    strategy_type=AdvancedArbitrageType.ML_ENHANCED_PAIRS,
                    timestamp=datetime.now(),
                    symbols=[symbol1, symbol2],
                    signal_strength=self._calculate_signal_strength(abs(predicted_change)),
                    confidence=confidence,
                    expected_return=abs(predicted_change),
                    expected_risk=0.02,
                    holding_period=5,
                    entry_prices={symbol1: price1, symbol2: price2},
                    target_weights=weights,
                    stop_loss=0.03,
                    take_profit=abs(predicted_change) * 0.8,
                    market_conditions={
                        'predicted_change': predicted_change,
                        'ml_confidence': confidence,
                        'correlation_5d': features.correlation_5d
                    },
                    strategy_params={'model_type': 'RandomForest+GradientBoosting'}
                )

        except Exception as e:
            logger.warning(f"ML分析失败: {e}")

        return None

    def _fallback_traditional_pairs(self, market_data: Dict[str, Any]) -> List[ArbitrageOpportunity]:
        """传统配对交易回退方法"""
        opportunities = []
        symbols = list(market_data.keys())

        for symbol1, symbol2 in itertools.combinations(symbols, 2):
            if self._is_valid_pair(symbol1, symbol2):
                # 简化的传统配对交易逻辑
                price1 = market_data[symbol1]['price']
                price2 = market_data[symbol2]['price']
                ratio = price1 / price2

                # 模拟历史均值和标准差
                mean_ratio = 1.0
                std_ratio = 0.1
                z_score = (ratio - mean_ratio) / std_ratio

                if abs(z_score) > 2.0:
                    weights = {symbol1: -0.5, symbol2: 0.5} if z_score > 0 else {symbol1: 0.5, symbol2: -0.5}

                    opportunity = ArbitrageOpportunity(
                        strategy_type=AdvancedArbitrageType.ML_ENHANCED_PAIRS,
                        timestamp=datetime.now(),
                        symbols=[symbol1, symbol2],
                        signal_strength=SignalStrength.MODERATE,
                        confidence=min(abs(z_score) / 2.0 * 0.7, 0.9),
                        expected_return=abs(z_score) * 0.01,
                        expected_risk=0.02,
                        holding_period=10,
                        entry_prices={symbol1: price1, symbol2: price2},
                        target_weights=weights,
                        stop_loss=0.03,
                        take_profit=0.02,
                        market_conditions={'z_score': z_score, 'method': 'traditional_fallback'},
                        strategy_params={'entry_threshold': 2.0}
                    )
                    opportunities.append(opportunity)

        return opportunities


class MultiFactorArbitrageStrategy(BaseArbitrageStrategy):
    """多因子套利策略"""

    def __init__(self, factor_weights: Dict[str, float] = None):
        super().__init__("MultiFactorArbitrage", AdvancedArbitrageType.MULTI_FACTOR_ARBITRAGE)

        # 默认因子权重
        self.factor_weights = factor_weights or {
            'value': 0.25,      # 价值因子
            'momentum': 0.20,   # 动量因子
            'quality': 0.20,    # 质量因子
            'volatility': 0.15, # 波动率因子
            'size': 0.10,       # 规模因子
            'liquidity': 0.10   # 流动性因子
        }

        # 因子计算历史
        self.factor_history = {}
        self.factor_scores = {}

        logger.info(f"初始化多因子套利策略，因子权重: {self.factor_weights}")

    def scan_opportunities(self, market_data: Dict[str, Any]) -> List[ArbitrageOpportunity]:
        """扫描多因子套利机会"""
        opportunities = []

        # 计算各股票的因子得分
        factor_scores = self._calculate_factor_scores(market_data)

        if len(factor_scores) < 2:
            return opportunities

        # 找出因子得分差异较大的股票对
        symbols = list(factor_scores.keys())
        for symbol1, symbol2 in itertools.combinations(symbols, 2):
            opportunity = self._analyze_factor_pair(symbol1, symbol2, factor_scores, market_data)
            if opportunity:
                opportunities.append(opportunity)

        return opportunities

    def _calculate_factor_scores(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """计算因子得分"""
        factor_scores = {}

        for symbol, data in market_data.items():
            try:
                # 简化的因子计算
                price = data.get('price', 0)
                volume = data.get('volume', 0)
                change = data.get('change', 0)

                # 价值因子（简化为价格倒数）
                value_score = 1.0 / max(price, 1.0) * 100

                # 动量因子
                momentum_score = change * 100

                # 质量因子（简化为价格稳定性）
                quality_score = max(0, 1.0 - abs(change)) * 100

                # 波动率因子（简化）
                volatility_score = max(0, 1.0 - abs(change) * 2) * 100

                # 规模因子（简化为成交量）
                size_score = min(volume / 1000000, 100)  # 标准化到0-100

                # 流动性因子
                liquidity_score = min(volume / 500000, 100)

                # 加权综合得分
                composite_score = (
                    self.factor_weights['value'] * value_score +
                    self.factor_weights['momentum'] * momentum_score +
                    self.factor_weights['quality'] * quality_score +
                    self.factor_weights['volatility'] * volatility_score +
                    self.factor_weights['size'] * size_score +
                    self.factor_weights['liquidity'] * liquidity_score
                )

                factor_scores[symbol] = composite_score

            except Exception as e:
                logger.warning(f"计算{symbol}因子得分失败: {e}")
                continue

        return factor_scores