"""
策略组合器模块
实现多策略组合、权重分配和动态调整
支持基于风险平价、最大夏普比率等方法的策略组合
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import itertools
from scipy.optimize import minimize

try:
    from .strategy_optimizer import StrategySignal, StrategyPerformance
except ImportError:
    from strategy_optimizer import StrategySignal, StrategyPerformance


class CombinationMethod(Enum):
    """策略组合方法"""
    EQUAL_WEIGHT = "equal_weight"           # 等权重
    RISK_PARITY = "risk_parity"            # 风险平价
    MAX_SHARPE = "max_sharpe"              # 最大夏普比率
    MIN_VARIANCE = "min_variance"          # 最小方差
    DYNAMIC_WEIGHT = "dynamic_weight"      # 动态权重
    PERFORMANCE_BASED = "performance_based" # 基于历史表现


@dataclass
class StrategyWeight:
    """策略权重"""
    strategy_name: str
    weight: float
    confidence: float
    last_update: datetime
    performance_score: float
    risk_score: float


@dataclass
class CombinedSignal:
    """组合信号"""
    timestamp: datetime
    combined_strategy_name: str
    component_signals: List[StrategySignal]
    weights: Dict[str, float]
    combined_confidence: float
    combined_expected_return: float
    combined_risk_level: str
    diversification_benefit: float
    correlation_matrix: Optional[np.ndarray] = None


class StrategyCombiner:
    """策略组合器"""

    def __init__(self, combination_method: CombinationMethod = CombinationMethod.DYNAMIC_WEIGHT):
        self.combination_method = combination_method
        self.strategy_weights: Dict[str, StrategyWeight] = {}
        self.performance_history: Dict[str, List[StrategyPerformance]] = {}
        self.correlation_matrix: Optional[np.ndarray] = None
        self.strategy_names: List[str] = []

        # 组合参数
        self.min_weight = 0.05  # 最小权重5%
        self.max_weight = 0.4   # 最大权重40%
        self.rebalance_threshold = 0.1  # 权重调整阈值
        self.lookback_periods = 60  # 回看期数

        # 风险参数
        self.max_correlation = 0.8  # 最大相关性
        self.target_volatility = 0.15  # 目标波动率

        logger.info(f"初始化策略组合器: {combination_method.value}")

    def add_strategy_performance(self, strategy_name: str, performance: StrategyPerformance):
        """添加策略表现数据"""
        if strategy_name not in self.performance_history:
            self.performance_history[strategy_name] = []
            self.strategy_names.append(strategy_name)

        self.performance_history[strategy_name].append(performance)

        # 保持历史数据在合理范围内
        if len(self.performance_history[strategy_name]) > self.lookback_periods:
            self.performance_history[strategy_name] = self.performance_history[strategy_name][-self.lookback_periods:]

    def calculate_strategy_weights(self) -> Dict[str, float]:
        """计算策略权重"""
        if not self.strategy_names:
            return {}

        if self.combination_method == CombinationMethod.EQUAL_WEIGHT:
            return self._equal_weight()
        elif self.combination_method == CombinationMethod.RISK_PARITY:
            return self._risk_parity_weight()
        elif self.combination_method == CombinationMethod.MAX_SHARPE:
            return self._max_sharpe_weight()
        elif self.combination_method == CombinationMethod.MIN_VARIANCE:
            return self._min_variance_weight()
        elif self.combination_method == CombinationMethod.DYNAMIC_WEIGHT:
            return self._dynamic_weight()
        elif self.combination_method == CombinationMethod.PERFORMANCE_BASED:
            return self._performance_based_weight()
        else:
            return self._equal_weight()

    def _equal_weight(self) -> Dict[str, float]:
        """等权重分配"""
        n_strategies = len(self.strategy_names)
        weight = 1.0 / n_strategies
        return {name: weight for name in self.strategy_names}

    def _risk_parity_weight(self) -> Dict[str, float]:
        """风险平价权重"""
        if len(self.strategy_names) < 2:
            return self._equal_weight()

        # 计算各策略的风险（波动率）
        risks = {}
        for strategy_name in self.strategy_names:
            if strategy_name in self.performance_history:
                performances = self.performance_history[strategy_name]
                if len(performances) >= 5:
                    returns = [p.total_return for p in performances[-20:]]
                    risks[strategy_name] = np.std(returns) if len(returns) > 1 else 0.1
                else:
                    risks[strategy_name] = 0.1
            else:
                risks[strategy_name] = 0.1

        # 风险平价：权重与风险成反比
        inv_risks = {name: 1.0 / max(risk, 0.001) for name, risk in risks.items()}
        total_inv_risk = sum(inv_risks.values())

        weights = {name: inv_risk / total_inv_risk for name, inv_risk in inv_risks.items()}
        return self._apply_weight_constraints(weights)

    def _max_sharpe_weight(self) -> Dict[str, float]:
        """最大夏普比率权重"""
        if len(self.strategy_names) < 2:
            return self._equal_weight()

        # 计算各策略的收益率和夏普比率
        sharpe_ratios = {}
        for strategy_name in self.strategy_names:
            if strategy_name in self.performance_history:
                performances = self.performance_history[strategy_name]
                if len(performances) >= 5:
                    avg_sharpe = np.mean([p.sharpe_ratio for p in performances[-10:]])
                    sharpe_ratios[strategy_name] = max(avg_sharpe, 0.1)
                else:
                    sharpe_ratios[strategy_name] = 0.5
            else:
                sharpe_ratios[strategy_name] = 0.5

        # 权重与夏普比率成正比
        total_sharpe = sum(sharpe_ratios.values())
        weights = {name: sharpe / total_sharpe for name, sharpe in sharpe_ratios.items()}
        return self._apply_weight_constraints(weights)

    def _min_variance_weight(self) -> Dict[str, float]:
        """最小方差权重"""
        if len(self.strategy_names) < 2:
            return self._equal_weight()

        # 构建协方差矩阵
        returns_matrix = self._build_returns_matrix()
        if returns_matrix is None or returns_matrix.shape[1] < 2:
            return self._equal_weight()

        try:
            cov_matrix = np.cov(returns_matrix.T)
            n = len(self.strategy_names)

            # 最小方差优化
            def objective(weights):
                return np.dot(weights, np.dot(cov_matrix, weights))

            constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]
            bounds = [(self.min_weight, self.max_weight) for _ in range(n)]

            result = minimize(objective, np.ones(n) / n, method='SLSQP',
                            bounds=bounds, constraints=constraints)

            if result.success:
                weights_dict = {name: weight for name, weight in zip(self.strategy_names, result.x)}
                return weights_dict
            else:
                return self._equal_weight()

        except Exception as e:
            logger.warning(f"最小方差优化失败: {e}")
            return self._equal_weight()

    def _dynamic_weight(self) -> Dict[str, float]:
        """动态权重分配"""
        # 综合考虑多个因素
        base_weights = self._performance_based_weight()
        risk_weights = self._risk_parity_weight()
        sharpe_weights = self._max_sharpe_weight()

        # 加权平均
        combined_weights = {}
        for name in self.strategy_names:
            combined_weights[name] = (
                0.4 * base_weights.get(name, 0) +
                0.3 * risk_weights.get(name, 0) +
                0.3 * sharpe_weights.get(name, 0)
            )

        # 归一化
        total_weight = sum(combined_weights.values())
        if total_weight > 0:
            combined_weights = {name: weight / total_weight for name, weight in combined_weights.items()}

        return self._apply_weight_constraints(combined_weights)

    def _performance_based_weight(self) -> Dict[str, float]:
        """基于历史表现的权重"""
        if not self.strategy_names:
            return {}

        # 计算各策略的综合得分
        scores = {}
        for strategy_name in self.strategy_names:
            if strategy_name in self.performance_history:
                performances = self.performance_history[strategy_name]
                if len(performances) >= 3:
                    # 最近表现权重更高
                    recent_performances = performances[-5:]
                    weights = np.exp(np.linspace(-1, 0, len(recent_performances)))
                    weights = weights / weights.sum()

                    weighted_score = sum(p.score * w for p, w in zip(recent_performances, weights))
                    scores[strategy_name] = max(weighted_score, 0.1)
                else:
                    scores[strategy_name] = 0.5
            else:
                scores[strategy_name] = 0.5

        # 转换为权重
        total_score = sum(scores.values())
        weights = {name: score / total_score for name, score in scores.items()}
        return self._apply_weight_constraints(weights)

    def _apply_weight_constraints(self, weights: Dict[str, float]) -> Dict[str, float]:
        """应用权重约束"""
        # 确保权重在合理范围内
        constrained_weights = {}
        for name, weight in weights.items():
            constrained_weights[name] = max(self.min_weight, min(self.max_weight, weight))

        # 重新归一化
        total_weight = sum(constrained_weights.values())
        if total_weight > 0:
            constrained_weights = {name: weight / total_weight for name, weight in constrained_weights.items()}

        return constrained_weights

    def _build_returns_matrix(self) -> Optional[np.ndarray]:
        """构建收益率矩阵"""
        if not self.performance_history:
            return None

        # 找到最短的历史长度
        min_length = min(len(performances) for performances in self.performance_history.values())
        if min_length < 5:
            return None

        # 构建矩阵
        returns_data = []
        for strategy_name in self.strategy_names:
            if strategy_name in self.performance_history:
                performances = self.performance_history[strategy_name][-min_length:]
                returns = [p.total_return for p in performances]
                returns_data.append(returns)

        return np.array(returns_data).T if returns_data else None

    def combine_signals(self, signals: List[StrategySignal]) -> Optional[CombinedSignal]:
        """组合多个策略信号"""
        if not signals:
            return None

        # 更新策略权重
        current_weights = self.calculate_strategy_weights()

        # 过滤有效信号
        valid_signals = [s for s in signals if s.strategy_name in current_weights]
        if not valid_signals:
            return None

        # 计算组合信号
        combined_confidence = 0.0
        combined_expected_return = 0.0
        total_weight = 0.0

        signal_weights = {}
        for signal in valid_signals:
            weight = current_weights.get(signal.strategy_name, 0.0)
            signal_weights[signal.strategy_name] = weight

            combined_confidence += signal.confidence * weight
            combined_expected_return += signal.expected_return * weight
            total_weight += weight

        # 归一化
        if total_weight > 0:
            combined_confidence /= total_weight
            combined_expected_return /= total_weight
            signal_weights = {name: weight / total_weight for name, weight in signal_weights.items()}

        # 计算分散化收益
        diversification_benefit = self._calculate_diversification_benefit(valid_signals, signal_weights)

        # 确定组合风险等级
        risk_levels = [s.risk_level for s in valid_signals]
        combined_risk_level = self._determine_combined_risk_level(risk_levels, signal_weights)

        return CombinedSignal(
            timestamp=datetime.now(),
            combined_strategy_name=f"Combined_{len(valid_signals)}Strategies",
            component_signals=valid_signals,
            weights=signal_weights,
            combined_confidence=combined_confidence,
            combined_expected_return=combined_expected_return,
            combined_risk_level=combined_risk_level,
            diversification_benefit=diversification_benefit
        )

    def _calculate_diversification_benefit(self, signals: List[StrategySignal],
                                         weights: Dict[str, float]) -> float:
        """计算分散化收益"""
        if len(signals) < 2:
            return 0.0

        # 简化的分散化收益计算
        # 基于策略类型的多样性
        strategy_types = set()
        for signal in signals:
            # 从策略名称推断类型
            if "pair" in signal.strategy_name.lower():
                strategy_types.add("pair_trading")
            elif "stat" in signal.strategy_name.lower():
                strategy_types.add("statistical")
            elif "vol" in signal.strategy_name.lower():
                strategy_types.add("volatility")
            else:
                strategy_types.add("other")

        # 分散化收益与策略类型数量正相关
        diversity_score = len(strategy_types) / len(signals)

        # 考虑权重分布的均匀性
        weight_values = list(weights.values())
        weight_entropy = -sum(w * np.log(w + 1e-10) for w in weight_values if w > 0)
        max_entropy = np.log(len(weight_values))
        entropy_score = weight_entropy / max_entropy if max_entropy > 0 else 0

        return diversity_score * entropy_score * 0.1  # 最大10%的分散化收益

    def _determine_combined_risk_level(self, risk_levels: List[str],
                                     weights: Dict[str, float]) -> str:
        """确定组合风险等级"""
        risk_scores = {"LOW": 1, "MEDIUM": 2, "HIGH": 3, "EXTREME": 4}

        weighted_risk = 0.0
        total_weight = 0.0

        for i, risk_level in enumerate(risk_levels):
            weight = list(weights.values())[i] if i < len(weights) else 0
            risk_score = risk_scores.get(risk_level.upper(), 2)
            weighted_risk += risk_score * weight
            total_weight += weight

        if total_weight > 0:
            avg_risk = weighted_risk / total_weight
        else:
            avg_risk = 2

        if avg_risk <= 1.5:
            return "LOW"
        elif avg_risk <= 2.5:
            return "MEDIUM"
        elif avg_risk <= 3.5:
            return "HIGH"
        else:
            return "EXTREME"

    def update_strategy_weights(self, force_update: bool = False):
        """更新策略权重"""
        current_time = datetime.now()

        # 检查是否需要更新
        if not force_update and self.strategy_weights:
            last_update = max(sw.last_update for sw in self.strategy_weights.values())
            if (current_time - last_update).total_seconds() < 300:  # 5分钟内不重复更新
                return

        # 计算新权重
        new_weights = self.calculate_strategy_weights()

        # 更新权重记录
        for strategy_name, weight in new_weights.items():
            performance_score = 0.5
            risk_score = 0.5

            if strategy_name in self.performance_history:
                recent_performances = self.performance_history[strategy_name][-5:]
                if recent_performances:
                    performance_score = np.mean([p.score for p in recent_performances])
                    # 风险得分基于波动率（越低越好）
                    returns = [p.total_return for p in recent_performances]
                    if len(returns) > 1:
                        volatility = np.std(returns)
                        risk_score = max(0.1, min(1.0, 1.0 - volatility * 5))

            self.strategy_weights[strategy_name] = StrategyWeight(
                strategy_name=strategy_name,
                weight=weight,
                confidence=0.8,  # 默认置信度
                last_update=current_time,
                performance_score=performance_score,
                risk_score=risk_score
            )

        logger.info(f"更新策略权重: {[(name, f'{sw.weight:.2%}') for name, sw in self.strategy_weights.items()]}")

    def get_strategy_allocation_summary(self) -> Dict[str, Any]:
        """获取策略配置摘要"""
        if not self.strategy_weights:
            return {"total_strategies": 0, "weights": {}, "last_update": None}

        return {
            "total_strategies": len(self.strategy_weights),
            "combination_method": self.combination_method.value,
            "weights": {name: sw.weight for name, sw in self.strategy_weights.items()},
            "performance_scores": {name: sw.performance_score for name, sw in self.strategy_weights.items()},
            "risk_scores": {name: sw.risk_score for name, sw in self.strategy_weights.items()},
            "last_update": max(sw.last_update for sw in self.strategy_weights.values()),
            "diversification_level": len(set(sw.strategy_name.split('_')[0] for sw in self.strategy_weights.values()))
        }