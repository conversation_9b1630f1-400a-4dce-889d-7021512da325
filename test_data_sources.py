"""
数据源接入测试脚本
测试各种数据源的连接和数据获取功能
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_mock_data_source():
    """测试模拟数据源"""
    print("\n🤖 测试模拟数据源")
    print("=" * 50)
    
    try:
        from data.mock_data_source import MockDataSource
        
        # 创建模拟数据源
        mock_source = MockDataSource()
        print("✅ 模拟数据源创建成功")
        
        # 连接
        if mock_source.connect():
            print("✅ 模拟数据源连接成功")
        else:
            print("❌ 模拟数据源连接失败")
            return False
        
        # 测试实时数据
        symbols = ['000001.SZ', '000002.SZ', '600000.SH']
        realtime_data = mock_source.get_realtime_data(symbols)
        
        print(f"📊 获取实时数据: {len(realtime_data)} 只股票")
        for symbol, data in realtime_data.items():
            print(f"  {symbol}: 价格={data.close:.2f}, 涨跌幅={data.change_pct:.2%}, 成交量={data.volume:,}")
        
        # 测试历史数据
        from data.data_source_interface import DataFrequency
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        hist_data = mock_source.get_historical_data('000001.SZ', start_date, end_date, DataFrequency.DAILY)
        print(f"📈 获取历史数据: {len(hist_data)} 条记录")
        
        if not hist_data.empty:
            print(f"  时间范围: {hist_data['timestamp'].min()} 到 {hist_data['timestamp'].max()}")
            print(f"  价格范围: {hist_data['close'].min():.2f} - {hist_data['close'].max():.2f}")
        
        # 测试股票信息
        symbol_info = mock_source.get_symbol_info('000001.SZ')
        print(f"ℹ️  股票信息: {symbol_info}")
        
        # 测试市场事件模拟
        print("\n🎭 测试市场事件模拟:")
        mock_source.simulate_market_event("crash", ['000001.SZ'])
        crash_data = mock_source.get_realtime_data(['000001.SZ'])
        print(f"  暴跌后价格: {crash_data['000001.SZ'].close:.2f}")
        
        mock_source.simulate_market_event("rally", ['000001.SZ'])
        rally_data = mock_source.get_realtime_data(['000001.SZ'])
        print(f"  暴涨后价格: {rally_data['000001.SZ'].close:.2f}")
        
        # 断开连接
        mock_source.disconnect()
        print("✅ 模拟数据源测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟数据源测试失败: {e}")
        return False

def test_csv_data_source():
    """测试CSV数据源"""
    print("\n📄 测试CSV数据源")
    print("=" * 50)
    
    try:
        from data.csv_data_source import CSVDataSourceFactory
        
        # 检查示例CSV文件
        csv_file = "data/sample_stock_data.csv"
        if not os.path.exists(csv_file):
            print(f"❌ CSV文件不存在: {csv_file}")
            return False
        
        # 创建CSV数据源
        csv_source = CSVDataSourceFactory.create_from_file(
            file_path=csv_file,
            symbols=['000001.SZ', '000002.SZ'],
            name="示例CSV数据"
        )
        print("✅ CSV数据源创建成功")
        
        # 连接
        if csv_source.connect():
            print("✅ CSV数据源连接成功")
        else:
            print("❌ CSV数据源连接失败")
            return False
        
        # 测试实时数据（模拟）
        symbols = ['000001.SZ', '000002.SZ']
        realtime_data = csv_source.get_realtime_data(symbols)
        
        print(f"📊 获取实时数据: {len(realtime_data)} 只股票")
        for symbol, data in realtime_data.items():
            print(f"  {symbol}: 价格={data.close:.2f}, 涨跌幅={data.change_pct:.2%}, 成交量={data.volume:,}")
        
        # 测试历史数据
        from data.data_source_interface import DataFrequency
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        
        hist_data = csv_source.get_historical_data('000001.SZ', start_date, end_date, DataFrequency.MINUTE)
        print(f"📈 获取历史数据: {len(hist_data)} 条记录")
        
        if not hist_data.empty:
            print(f"  时间范围: {hist_data['timestamp'].min()} 到 {hist_data['timestamp'].max()}")
            print(f"  价格范围: {hist_data['close'].min():.2f} - {hist_data['close'].max():.2f}")
        
        # 测试股票信息
        symbol_info = csv_source.get_symbol_info('000001.SZ')
        print(f"ℹ️  股票信息: {symbol_info}")
        
        # 测试数据模拟
        print("\n🎬 测试数据模拟:")
        csv_source.start_simulation(speed=2.0)
        print("  模拟已启动，速度: 2.0x")
        
        # 获取几次数据看变化
        for i in range(3):
            time.sleep(1)
            data = csv_source.get_realtime_data(['000001.SZ'])
            print(f"  第{i+1}次: 价格={data['000001.SZ'].close:.2f}")
        
        csv_source.stop_simulation()
        print("  模拟已停止")
        
        # 获取模拟进度
        progress = csv_source.get_simulation_progress()
        print(f"📊 模拟进度: {progress}")
        
        # 断开连接
        csv_source.disconnect()
        print("✅ CSV数据源测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV数据源测试失败: {e}")
        return False

def test_enhanced_data_manager():
    """测试增强数据管理器"""
    print("\n🚀 测试增强数据管理器")
    print("=" * 50)
    
    try:
        from data.enhanced_data_manager import EnhancedDataManager
        
        # 创建数据管理器
        config_file = "config/data_sources.json"
        data_manager = EnhancedDataManager(config_file)
        print("✅ 增强数据管理器创建成功")
        
        # 获取数据源状态
        sources_status = data_manager.get_data_sources_status()
        print(f"📊 数据源状态: {len(sources_status)} 个数据源")
        for name, status in sources_status.items():
            print(f"  {name}: {'✅' if status['connected'] else '❌'} {status['type']}")
        
        # 测试实时数据获取
        symbols = ['000001.SZ', '000002.SZ', '600000.SH']
        realtime_data = data_manager.get_realtime_data(symbols)
        
        print(f"📊 获取实时数据: {len(realtime_data)} 只股票")
        for symbol, data in realtime_data.items():
            print(f"  {symbol}: 价格={data.close:.2f}, 涨跌幅={data.change_pct:.2%}")
        
        # 测试缓存功能
        print("\n💾 测试缓存功能:")
        start_time = time.time()
        cached_data = data_manager.get_realtime_data(symbols, use_cache=True)
        cache_time = time.time() - start_time
        print(f"  缓存查询耗时: {cache_time:.3f}秒")
        
        # 测试历史数据
        from data.data_source_interface import DataFrequency
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        hist_data = data_manager.get_historical_data('000001.SZ', start_date, end_date, DataFrequency.DAILY)
        print(f"📈 获取历史数据: {len(hist_data)} 条记录")
        
        # 添加CSV数据源
        print("\n📄 测试添加CSV数据源:")
        csv_file = "data/sample_stock_data.csv"
        if os.path.exists(csv_file):
            source_name = data_manager.add_csv_data_source(
                file_path=csv_file,
                symbols=['000001.SZ', '000002.SZ'],
                name="测试CSV",
                simulate=True,
                speed=1.0
            )
            print(f"  ✅ CSV数据源已添加: {source_name}")
        
        # 测试数据流
        print("\n🌊 测试数据流:")
        
        received_data = []
        def data_callback(data):
            received_data.append(data)
            print(f"  📡 收到数据: {len(data)} 只股票")
        
        data_manager.subscribe_data_updates(data_callback)
        data_manager.start_data_stream(symbols)
        
        # 等待几秒收集数据
        time.sleep(5)
        
        data_manager.stop_data_stream()
        print(f"  📊 共收到 {len(received_data)} 次数据更新")
        
        # 获取统计信息
        stats = data_manager.get_statistics()
        print(f"\n📊 统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 获取质量报告
        quality_report = data_manager.get_quality_report()
        print(f"\n📋 数据质量报告:")
        for key, value in quality_report.items():
            print(f"  {key}: {value}")
        
        # 关闭数据管理器
        data_manager.shutdown()
        print("✅ 增强数据管理器测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强数据管理器测试失败: {e}")
        return False

def test_api_data_sources():
    """测试API数据源（需要配置）"""
    print("\n🌐 测试API数据源")
    print("=" * 50)
    
    try:
        from data.api_data_source import APIDataSourceFactory
        
        # 测试Yahoo Finance（不需要API key）
        print("📈 测试Yahoo Finance数据源:")
        try:
            yahoo_source = APIDataSourceFactory.create_yahoo_source()
            
            if yahoo_source.connect():
                print("  ✅ Yahoo Finance连接成功")
                
                # 测试美股数据
                us_symbols = ['AAPL', 'GOOGL']
                us_data = yahoo_source.get_realtime_data(us_symbols)
                
                if us_data:
                    print(f"  📊 获取美股数据: {len(us_data)} 只股票")
                    for symbol, data in us_data.items():
                        print(f"    {symbol}: ${data.close:.2f}")
                else:
                    print("  ⚠️  未获取到美股数据")
                
                yahoo_source.disconnect()
            else:
                print("  ❌ Yahoo Finance连接失败")
                
        except Exception as e:
            print(f"  ⚠️  Yahoo Finance测试跳过: {e}")
        
        # 测试Tushare（需要token）
        print("\n📊 测试Tushare数据源:")
        tushare_token = os.getenv('TUSHARE_TOKEN')
        
        if tushare_token:
            try:
                tushare_source = APIDataSourceFactory.create_tushare_source(tushare_token)
                
                if tushare_source.connect():
                    print("  ✅ Tushare连接成功")
                    
                    # 测试A股数据
                    a_symbols = ['000001.SZ', '600000.SH']
                    a_data = tushare_source.get_realtime_data(a_symbols)
                    
                    if a_data:
                        print(f"  📊 获取A股数据: {len(a_data)} 只股票")
                        for symbol, data in a_data.items():
                            print(f"    {symbol}: ¥{data.close:.2f}")
                    else:
                        print("  ⚠️  未获取到A股数据")
                    
                    tushare_source.disconnect()
                else:
                    print("  ❌ Tushare连接失败")
                    
            except Exception as e:
                print(f"  ⚠️  Tushare测试失败: {e}")
        else:
            print("  ⚠️  Tushare token未配置，跳过测试")
            print("    设置方法: export TUSHARE_TOKEN=your_token")
        
        return True
        
    except Exception as e:
        print(f"❌ API数据源测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 数据源接入模块测试")
    print("=" * 60)
    
    # 创建必要目录
    os.makedirs("data", exist_ok=True)
    os.makedirs("config", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("模拟数据源", test_mock_data_source),
        ("CSV数据源", test_csv_data_source),
        ("增强数据管理器", test_enhanced_data_manager),
        ("API数据源", test_api_data_sources)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            result = test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: 测试通过")
            else:
                print(f"❌ {test_name}: 测试失败")
                
        except Exception as e:
            print(f"💥 {test_name}: 测试异常 - {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n📋 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据接入模块运行正常！")
        print("\n📚 使用指南:")
        print("1. 模拟数据源: 用于测试和演示")
        print("2. CSV数据源: 从CSV文件读取历史数据")
        print("3. API数据源: 连接Tushare、Yahoo Finance等")
        print("4. 增强数据管理器: 统一管理多种数据源")
        print("\n🔧 配置文件: config/data_sources.json")
        print("📄 示例数据: data/sample_stock_data.csv")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过，系统基本可用！")
        print("💡 建议检查失败的测试项并优化")
    else:
        print("⚠️ 多个测试失败，需要进一步调试")
        print("🔧 建议检查依赖安装和配置文件")

if __name__ == "__main__":
    main()
