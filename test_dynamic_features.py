#!/usr/bin/env python3
"""
动态功能测试脚本
测试动态资金池管理和临时套利策略
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_dynamic_risk_management():
    """测试动态风险管理"""
    print("💰 测试动态风险管理功能")
    print("=" * 50)
    
    try:
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits, TradingMode, MarketCondition
        
        # 创建动态风险管理器
        limits = DynamicPositionLimits(
            initial_capital=200000,  # 20万初始资金
            normal_position_ratio=0.70,  # 正常70%
            emergency_position_ratio=0.85,  # 临时套利85%
            max_stock_count=10
        )
        
        risk_manager = DynamicRiskManager(limits)
        
        print(f"✅ 初始资金: ¥{risk_manager.initial_capital:,.0f}")
        print(f"✅ 当前资金池: ¥{risk_manager.total_capital:,.0f}")
        print(f"✅ 正常仓位限制: {risk_manager.limits.normal_position_ratio:.0%}")
        print(f"✅ 临时套利限制: {risk_manager.limits.emergency_position_ratio:.0%}")
        print(f"✅ 当前模式: {risk_manager.current_mode.value}")
        
        # 测试市场状况检测
        print("\n🔍 测试市场状况检测:")
        
        # 正常市场
        normal_data = {
            '000001.SZ': {'price': 10.0, 'change': 0.01},
            '000002.SZ': {'price': 12.0, 'change': -0.005}
        }
        condition = risk_manager.detect_market_condition(normal_data)
        print(f"  正常市场: {condition.value}")
        
        # 爆跌市场
        crash_data = {
            '000001.SZ': {'price': 10.0, 'change': -0.08},
            '000002.SZ': {'price': 12.0, 'change': -0.06}
        }
        condition = risk_manager.detect_market_condition(crash_data)
        print(f"  爆跌市场: {condition.value}")
        
        # 测试临时套利触发
        should_trigger, signal = risk_manager.should_trigger_emergency_arbitrage(crash_data)
        print(f"  是否触发临时套利: {should_trigger}")
        
        if should_trigger and signal:
            print(f"  触发条件: {signal.market_condition.value}")
            print(f"  建议操作: {signal.suggested_action}")
            
            # 进入临时套利模式
            risk_manager.enter_emergency_mode(signal)
            print(f"✅ 已进入临时套利模式")
            print(f"  当前仓位限制: {risk_manager.get_current_position_limit():.0%}")
        
        # 测试开仓检查（临时套利模式）
        can_open, msg, info = risk_manager.can_open_position("000001.SZ", 10.0, 1000, is_emergency=True)
        print(f"\n📊 临时套利开仓检查: {can_open}")
        print(f"  消息: {msg}")
        if info:
            print(f"  建议仓位: {info.get('suggested_quantity', 0)}股")
            print(f"  当前模式: {info.get('current_mode', 'unknown')}")
        
        # 退出临时套利模式
        risk_manager.exit_emergency_mode()
        print(f"✅ 已退出临时套利模式")
        print(f"  当前仓位限制: {risk_manager.get_current_position_limit():.0%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 动态风险管理测试失败: {e}")
        return False

def test_emergency_arbitrage():
    """测试临时套利策略"""
    print("\n🚨 测试临时套利策略")
    print("=" * 50)
    
    try:
        from strategies.emergency_arbitrage import EmergencyArbitrageEngine, EmergencyStrategy
        
        # 创建临时套利引擎
        engine = EmergencyArbitrageEngine()
        print("✅ 临时套利引擎创建成功")
        
        # 模拟极端市场数据
        extreme_market_data = {
            '000001.SZ': {'price': 10.0, 'change': -0.08, 'volume': 2000000},  # 爆跌8%
            '000002.SZ': {'price': 12.0, 'change': 0.06, 'volume': 1500000},   # 爆涨6%
            '600000.SH': {'price': 8.0, 'change': -0.02, 'volume': 800000},
            '600036.SH': {'price': 45.0, 'change': 0.01, 'volume': 600000}
        }
        
        print("📊 模拟极端市场数据:")
        for symbol, data in extreme_market_data.items():
            print(f"  {symbol}: 价格¥{data['price']:.2f}, 变化{data['change']:+.1%}")
        
        # 扫描临时套利机会
        signals = engine.scan_emergency_opportunities(extreme_market_data)
        print(f"\n🔍 检测到 {len(signals)} 个临时套利机会:")
        
        for i, signal in enumerate(signals[:5], 1):  # 显示前5个
            print(f"  {i}. {signal.strategy_type.value}")
            print(f"     目标: {signal.trigger_symbol}, 操作: {signal.action}")
            print(f"     紧急程度: {signal.urgency_level}/5, 置信度: {signal.confidence:.2f}")
            print(f"     预期收益: {signal.expected_return:.2%}")
        
        # 选择最佳信号
        best_signal = engine.select_best_emergency_signal(signals)
        if best_signal:
            print(f"\n🎯 最佳临时套利信号:")
            print(f"  策略: {best_signal.strategy_type.value}")
            print(f"  目标: {best_signal.trigger_symbol}")
            print(f"  操作: {best_signal.action}")
            print(f"  入场价: ¥{best_signal.entry_price:.2f}")
            print(f"  最大持有: {best_signal.max_hold_time}分钟")
            
            # 执行信号
            success = engine.execute_emergency_signal(best_signal)
            print(f"  执行结果: {'成功' if success else '失败'}")
        
        # 获取状态
        status = engine.get_emergency_status()
        print(f"\n📈 临时套利状态:")
        print(f"  活跃信号: {status['active_signals_count']}")
        print(f"  总信号数: {status['total_signals']}")
        print(f"  成功率: {status['success_rate']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 临时套利策略测试失败: {e}")
        return False

def test_integration():
    """测试系统集成"""
    print("\n🔗 测试动态系统集成")
    print("=" * 50)
    
    try:
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
        from strategies.emergency_arbitrage import EmergencyArbitrageEngine
        
        # 创建组件
        limits = DynamicPositionLimits(initial_capital=200000)
        risk_manager = DynamicRiskManager(limits)
        emergency_engine = EmergencyArbitrageEngine()
        
        print("✅ 所有组件创建成功")
        
        # 模拟完整的临时套利流程
        print("\n🔄 模拟完整临时套利流程:")
        
        # 1. 检测极端市场
        market_data = {
            '000001.SZ': {'price': 10.0, 'change': -0.07, 'volume': 3000000},  # 爆跌7%
            '000002.SZ': {'price': 12.0, 'change': -0.05, 'volume': 2000000}
        }
        
        print("1️⃣ 检测市场状况...")
        condition = risk_manager.detect_market_condition(market_data)
        print(f"   市场状况: {condition.value}")
        
        # 2. 判断是否触发临时套利
        should_trigger, signal = risk_manager.should_trigger_emergency_arbitrage(market_data)
        print(f"2️⃣ 是否触发临时套利: {should_trigger}")
        
        if should_trigger:
            # 3. 进入临时套利模式
            print("3️⃣ 进入临时套利模式...")
            risk_manager.enter_emergency_mode(signal)
            print(f"   仓位限制提升至: {risk_manager.get_current_position_limit():.0%}")
            
            # 4. 扫描套利机会
            print("4️⃣ 扫描临时套利机会...")
            emergency_signals = emergency_engine.scan_emergency_opportunities(market_data)
            print(f"   发现 {len(emergency_signals)} 个机会")
            
            # 5. 选择最佳机会
            if emergency_signals:
                best_signal = emergency_engine.select_best_emergency_signal(emergency_signals)
                print(f"5️⃣ 选择最佳机会: {best_signal.strategy_type.value}")
                
                # 6. 检查仓位
                can_open, msg, info = risk_manager.can_open_position(
                    best_signal.trigger_symbol, 
                    best_signal.entry_price, 
                    1000, 
                    is_emergency=True
                )
                print(f"6️⃣ 仓位检查: {can_open}")
                
                if can_open:
                    # 7. 执行交易
                    success = risk_manager.open_position(
                        best_signal.trigger_symbol,
                        best_signal.entry_price,
                        1000,
                        is_emergency=True
                    )
                    print(f"7️⃣ 执行交易: {'成功' if success else '失败'}")
                    
                    # 8. 更新资金池
                    risk_manager.update_total_capital()
                    portfolio = risk_manager.get_portfolio_summary()
                    print(f"8️⃣ 资金池更新:")
                    print(f"   总资金: ¥{portfolio['total_capital']:,.0f}")
                    print(f"   总仓位: {portfolio['total_position_ratio']:.1%}")
                    print(f"   临时套利仓位: {portfolio['emergency_position_count']}")
            
            # 9. 退出临时套利模式
            print("9️⃣ 退出临时套利模式...")
            risk_manager.exit_emergency_mode()
            print(f"   仓位限制恢复至: {risk_manager.get_current_position_limit():.0%}")
        
        print("✅ 动态系统集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False

def test_capital_pool_dynamics():
    """测试资金池动态变化"""
    print("\n💰 测试资金池动态变化")
    print("=" * 50)
    
    try:
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
        
        # 创建风险管理器
        limits = DynamicPositionLimits(initial_capital=200000)
        risk_manager = DynamicRiskManager(limits)
        
        print(f"初始状态:")
        print(f"  初始资金: ¥{risk_manager.initial_capital:,.0f}")
        print(f"  当前资金池: ¥{risk_manager.total_capital:,.0f}")
        
        # 模拟盈利后资金池增长
        print(f"\n📈 模拟盈利情况:")
        
        # 开仓
        risk_manager.open_position("000001.SZ", 10.0, 1000)
        print(f"  开仓后可用现金: ¥{risk_manager.available_cash:,.0f}")
        
        # 模拟价格上涨
        new_prices = {"000001.SZ": 11.0}  # 上涨10%
        risk_manager.update_prices(new_prices)
        risk_manager.update_total_capital()
        
        portfolio = risk_manager.get_portfolio_summary()
        print(f"  价格上涨后:")
        print(f"    总资金池: ¥{portfolio['total_capital']:,.0f}")
        print(f"    总收益率: {portfolio['total_return']:+.2%}")
        print(f"    可投资额度(70%): ¥{portfolio['total_capital'] * 0.7:,.0f}")
        print(f"    可投资额度(85%): ¥{portfolio['total_capital'] * 0.85:,.0f}")
        
        # 验证动态仓位控制
        print(f"\n🔍 验证动态仓位控制:")
        normal_limit = portfolio['total_capital'] * 0.7
        emergency_limit = portfolio['total_capital'] * 0.85
        
        print(f"  正常模式最大投资: ¥{normal_limit:,.0f}")
        print(f"  临时套利最大投资: ¥{emergency_limit:,.0f}")
        print(f"  额外可用资金: ¥{emergency_limit - normal_limit:,.0f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 资金池动态测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("⚡ 动态量化套利系统功能测试")
    print("=" * 60)
    print("💰 动态资金池管理 | 🚨 临时套利策略")
    print("📊 初始20万 → 正常70% → 临时套利85%")
    print("=" * 60)
    
    tests = [
        ("动态风险管理", test_dynamic_risk_management),
        ("临时套利策略", test_emergency_arbitrage),
        ("资金池动态变化", test_capital_pool_dynamics),
        ("系统集成", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！动态套利系统功能正常")
        print("\n🚀 可以运行以下命令启动系统:")
        print("   streamlit run dynamic_visual_app.py")
        print("\n💡 系统特性:")
        print("   • 20万初始资金，动态资金池管理")
        print("   • 正常模式70%仓位控制")
        print("   • 临时套利模式85%仓位控制")
        print("   • 爆跌爆涨自动检测和响应")
        print("   • 智能模式切换和风险控制")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
