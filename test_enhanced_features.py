#!/usr/bin/env python3
"""
增强功能测试脚本
测试20万资金控制、策略比较、数据库功能
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_risk_management():
    """测试风险管理功能"""
    print("🛡️  测试风险管理功能")
    print("=" * 50)
    
    try:
        from risk.enhanced_risk_manager import EnhancedRiskManager, PositionLimits
        
        # 创建20万资金的风险管理器
        limits = PositionLimits(
            initial_capital=200000,  # 20万
            max_total_position_ratio=0.70,  # 70%
            max_stock_count=10,  # 10只股票
            max_single_position_ratio=0.15  # 单股15%
        )
        
        risk_manager = EnhancedRiskManager(limits)
        
        print(f"✅ 初始资金: ¥{risk_manager.limits.initial_capital:,.0f}")
        print(f"✅ 最大总仓位: {risk_manager.limits.max_total_position_ratio:.0%}")
        print(f"✅ 最大股票数: {risk_manager.limits.max_stock_count}")
        print(f"✅ 单股最大仓位: {risk_manager.limits.max_single_position_ratio:.0%}")
        
        # 测试开仓检查
        can_open, msg, info = risk_manager.can_open_position("000001.SZ", 10.0, 1000)
        print(f"✅ 开仓检查: {can_open} - {msg}")
        
        if info:
            print(f"✅ 建议仓位: {info.get('suggested_quantity', 0)}股")
            print(f"✅ 建议金额: ¥{info.get('suggested_value', 0):,.0f}")
        
        # 获取投资组合摘要
        summary = risk_manager.get_portfolio_summary()
        print(f"✅ 可用现金: ¥{summary['available_cash']:,.0f}")
        print(f"✅ 现金比例: {summary['cash_ratio']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险管理测试失败: {e}")
        return False

def test_strategy_comparison():
    """测试策略比较功能"""
    print("\n🧠 测试策略比较功能")
    print("=" * 50)
    
    try:
        from strategies.strategy_optimizer import StrategyOptimizer
        
        # 创建策略优化器
        optimizer = StrategyOptimizer()
        
        # 模拟市场数据
        market_data = {
            '000001.SZ': {'price': 10.5, 'change': 0.02},
            '000002.SZ': {'price': 12.3, 'change': -0.01},
            '600000.SH': {'price': 8.7, 'change': 0.03}
        }
        
        print("✅ 策略优化器创建成功")
        print(f"✅ 可用策略数: {len(optimizer.strategies)}")
        
        # 评估所有策略
        evaluation_results = optimizer.evaluate_strategies(market_data)
        print(f"✅ 策略评估完成，评估了 {len(evaluation_results)} 个策略")
        
        for strategy, performance, signal in evaluation_results:
            print(f"  📊 {strategy.name}: 评分={performance.score:.3f}, 有信号={'是' if signal else '否'}")
        
        # 选择最优策略
        best_signal, comparison = optimizer.select_best_strategy(market_data)
        
        if best_signal:
            print(f"✅ 最优策略: {best_signal.strategy_name}")
            print(f"✅ 信号类型: {best_signal.signal_type}")
            print(f"✅ 置信度: {best_signal.confidence:.2f}")
        else:
            print("✅ 未生成信号（正常情况）")
        
        print(f"✅ 策略比较数据可用于可视化")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略比较测试失败: {e}")
        return False

def test_database_functionality():
    """测试数据库功能"""
    print("\n💾 测试数据库功能")
    print("=" * 50)
    
    try:
        from database.signal_database import SignalDatabase, TradingSignal
        
        # 创建测试数据库
        db = SignalDatabase("test_enhanced.db")
        print("✅ 数据库创建成功")
        
        # 创建测试信号
        test_signal = TradingSignal(
            timestamp=datetime.now(),
            strategy_name="配对交易",
            symbol1="000001.SZ",
            symbol2="000002.SZ",
            signal_type="LONG_SHORT",
            confidence=0.85,
            expected_return=0.02,
            risk_level="MEDIUM",
            entry_price1=10.5,
            entry_price2=12.3,
            performance_score=0.75,
            is_historical=False
        )
        
        # 保存信号
        signal_id = db.save_signal(test_signal)
        print(f"✅ 信号保存成功，ID: {signal_id}")
        
        # 获取实时信号
        realtime_signals = db.get_realtime_signals(hours=24)
        print(f"✅ 获取实时信号: {len(realtime_signals)} 条")
        
        # 获取统计信息
        stats = db.get_strategy_statistics()
        print(f"✅ 总信号数: {stats['total_signals']}")
        print(f"✅ 平均置信度: {stats['avg_confidence']:.2f}")
        
        # 获取数据库信息
        db_info = db.get_database_info()
        print(f"✅ 数据库大小: {db_info['database_size_mb']:.2f} MB")
        print(f"✅ 总记录数: {db_info['total_records']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    print("\n🔗 测试系统集成")
    print("=" * 50)
    
    try:
        # 导入所有模块
        from risk.enhanced_risk_manager import EnhancedRiskManager, PositionLimits
        from strategies.strategy_optimizer import StrategyOptimizer
        from database.signal_database import SignalDatabase, TradingSignal
        
        # 创建组件
        limits = PositionLimits(initial_capital=200000, max_total_position_ratio=0.70, max_stock_count=10)
        risk_manager = EnhancedRiskManager(limits)
        optimizer = StrategyOptimizer()
        db = SignalDatabase("integration_test.db")
        
        print("✅ 所有组件创建成功")
        
        # 模拟完整流程
        market_data = {
            '000001.SZ': {'price': 10.5, 'change': 0.02},
            '000002.SZ': {'price': 12.3, 'change': -0.01}
        }
        
        # 1. 策略比较选择最优信号
        best_signal, comparison = optimizer.select_best_strategy(market_data)
        
        if best_signal:
            print("✅ 生成最优交易信号")
            
            # 2. 风险检查
            can_open, msg, info = risk_manager.can_open_position(
                best_signal.symbol1, 
                best_signal.entry_price1, 
                1000
            )
            print(f"✅ 风险检查: {can_open}")
            
            # 3. 保存到数据库
            db_signal = TradingSignal(
                timestamp=datetime.now(),
                strategy_name=best_signal.strategy_name,
                symbol1=best_signal.symbol1,
                symbol2=best_signal.symbol2,
                signal_type=best_signal.signal_type,
                confidence=best_signal.confidence,
                expected_return=best_signal.expected_return,
                risk_level=best_signal.risk_level,
                entry_price1=best_signal.entry_price1,
                entry_price2=best_signal.entry_price2,
                performance_score=best_signal.performance_score,
                is_historical=False
            )
            
            signal_id = db.save_signal(db_signal)
            print(f"✅ 信号保存到数据库，ID: {signal_id}")
        
        print("✅ 系统集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 增强版量化套利系统功能测试")
    print("=" * 60)
    print("💰 20万资金 | 70%仓位控制 | 10只股票限制")
    print("🧠 策略智能比较 | 💾 SQLite数据库存储")
    print("=" * 60)
    
    tests = [
        ("风险管理", test_risk_management),
        ("策略比较", test_strategy_comparison),
        ("数据库功能", test_database_functionality),
        ("系统集成", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强版系统功能正常")
        print("\n🚀 可以运行以下命令启动系统:")
        print("   python3 run_enhanced_system.py")
        print("   或")
        print("   streamlit run enhanced_visual_app.py")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
