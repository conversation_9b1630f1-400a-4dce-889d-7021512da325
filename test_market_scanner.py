#!/usr/bin/env python3
"""
市场扫描器测试脚本
测试80线程并行扫描功能
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from scanner.market_scanner import MarketScanner, ArbitrageOpportunity
    print("✅ 市场扫描模块导入成功")
except ImportError as e:
    print(f"❌ 市场扫描模块导入失败: {e}")
    sys.exit(1)

def test_scanner_basic():
    """测试扫描器基本功能"""
    print("\n🔍 测试扫描器基本功能...")
    
    # 创建扫描器
    scanner = MarketScanner(max_threads=10)  # 使用较少线程进行测试
    print(f"✅ 扫描器创建成功，最大线程数: {scanner.max_threads}")
    
    # 加载股票池
    stock_pool = scanner.load_stock_pool()
    print(f"✅ 股票池加载成功，包含 {len(stock_pool)} 只股票")
    
    # 显示前10只股票
    print("📊 股票池示例:")
    for i, symbol in enumerate(stock_pool[:10]):
        print(f"  {i+1}. {symbol}")
    
    return scanner

def test_market_data():
    """测试市场数据获取"""
    print("\n📊 测试市场数据获取...")
    
    scanner = MarketScanner(max_threads=5)
    
    # 测试获取单只股票数据
    test_symbol = "000001.SZ"
    market_data = scanner._get_market_data(test_symbol)
    
    if market_data:
        print(f"✅ {test_symbol} 市场数据获取成功:")
        print(f"  当前价格: ¥{market_data['current_price']:.2f}")
        print(f"  涨跌幅: {market_data['change_pct']:+.2%}")
        print(f"  成交量: {market_data['volume']:,}")
        print(f"  RSI: {market_data['rsi']:.1f}")
        print(f"  市值: ¥{market_data['market_cap']:,.0f}")
    else:
        print(f"❌ {test_symbol} 市场数据获取失败")

def test_strategy_scanning():
    """测试策略扫描"""
    print("\n🎯 测试策略扫描...")
    
    scanner = MarketScanner(max_threads=5)
    
    # 测试单个策略
    test_symbol = "000001.SZ"
    market_data = scanner._get_market_data(test_symbol)
    
    if market_data:
        strategies = [
            'pair_trading',
            'statistical_arbitrage',
            'momentum_arbitrage',
            'mean_reversion',
            'volatility_arbitrage'
        ]
        
        for strategy in strategies:
            strategy_func = scanner.strategies.get(strategy)
            if strategy_func:
                opportunities = strategy_func(test_symbol, market_data)
                print(f"  {strategy}: 发现 {len(opportunities)} 个机会")
                
                for opp in opportunities:
                    print(f"    - 置信度: {opp.confidence:.1%}, 预期收益: {opp.expected_return:.2%}")

async def test_full_market_scan():
    """测试全市场扫描"""
    print("\n🚀 测试全市场扫描...")
    
    # 创建扫描器，使用较多线程
    scanner = MarketScanner(max_threads=20)
    
    print(f"📊 开始扫描，使用 {scanner.max_threads} 个线程...")
    start_time = time.time()
    
    try:
        # 执行异步扫描
        results = await scanner.scan_market_async()
        
        end_time = time.time()
        scan_duration = end_time - start_time
        
        print(f"✅ 扫描完成！")
        print(f"⏱️  扫描用时: {scan_duration:.2f} 秒")
        print(f"📈 发现机会: {len(results)} 个")
        
        if results:
            # 统计分析
            strategies = {}
            risk_levels = {}
            total_confidence = 0
            total_return = 0
            
            for result in results:
                # 策略统计
                strategy = result.strategy_type
                strategies[strategy] = strategies.get(strategy, 0) + 1
                
                # 风险等级统计
                risk = result.risk_level
                risk_levels[risk] = risk_levels.get(risk, 0) + 1
                
                # 累计指标
                total_confidence += result.confidence
                total_return += result.expected_return
            
            print(f"\n📊 扫描统计:")
            print(f"  平均置信度: {total_confidence/len(results):.1%}")
            print(f"  平均预期收益: {total_return/len(results):.2%}")
            
            print(f"\n🎯 策略分布:")
            for strategy, count in strategies.items():
                print(f"  {strategy}: {count} 个机会")
            
            print(f"\n⚠️  风险等级分布:")
            for risk, count in risk_levels.items():
                print(f"  {risk}: {count} 个机会")
            
            # 显示前5个最佳机会
            print(f"\n⭐ 前5个最佳机会:")
            sorted_results = sorted(results, key=lambda x: (x.confidence, x.expected_return), reverse=True)
            
            for i, result in enumerate(sorted_results[:5]):
                print(f"  {i+1}. {result.strategy_type} - {'/'.join(result.symbols)}")
                print(f"     置信度: {result.confidence:.1%}, 预期收益: {result.expected_return:.2%}, 风险: {result.risk_level}")
        
        # 获取扫描状态
        status = scanner.get_scan_status()
        print(f"\n📋 扫描状态:")
        print(f"  股票池大小: {status.get('stock_pool_size', 0)}")
        print(f"  使用线程数: {status.get('max_threads', 0)}")
        print(f"  扫描进度: {status.get('progress', 0):.1f}%")
        
    except Exception as e:
        print(f"❌ 扫描失败: {e}")
        import traceback
        traceback.print_exc()

def test_performance():
    """测试性能对比"""
    print("\n⚡ 测试性能对比...")
    
    thread_counts = [1, 5, 10, 20, 40]
    
    for thread_count in thread_counts:
        print(f"\n🔧 测试 {thread_count} 线程...")
        
        scanner = MarketScanner(max_threads=thread_count)
        
        start_time = time.time()
        
        try:
            # 运行异步扫描
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            results = loop.run_until_complete(scanner.scan_market_async(['statistical_arbitrage']))
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"  ✅ {thread_count} 线程: {duration:.2f}秒, 发现 {len(results)} 个机会")
            
        except Exception as e:
            print(f"  ❌ {thread_count} 线程测试失败: {e}")
        finally:
            loop.close()

def main():
    """主测试函数"""
    print("🚀 市场扫描器测试开始")
    print("=" * 50)
    
    try:
        # 基本功能测试
        scanner = test_scanner_basic()
        
        # 市场数据测试
        test_market_data()
        
        # 策略扫描测试
        test_strategy_scanning()
        
        # 全市场扫描测试
        print("\n⚠️  即将开始全市场扫描测试，这可能需要一些时间...")
        input("按回车键继续...")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(test_full_market_scan())
        loop.close()
        
        # 性能测试
        print("\n⚠️  即将开始性能对比测试...")
        choice = input("是否进行性能测试？(y/n): ")
        if choice.lower() == 'y':
            test_performance()
        
        print("\n✅ 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
