"""
实时集成系统测试
验证所有模块间的联动和自动化功能
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_realtime_coordinator():
    """测试实时协调器"""
    print("\n⚡ 测试实时系统协调器")
    print("=" * 50)
    
    try:
        from core.realtime_coordinator import RealTimeCoordinator, OpportunityAlert
        
        # 创建协调器
        coordinator = RealTimeCoordinator(initial_capital=200000)
        print("✅ 实时协调器创建成功")
        
        # 测试回调函数
        ui_updates = []
        signals_received = []
        alerts_received = []
        
        def ui_callback(data):
            ui_updates.append(data)
            print(f"📊 UI更新: 市场数据{len(data.get('market_data', {}))}只股票")
        
        def signal_callback(signal):
            signals_received.append(signal)
            print(f"🎯 新信号: {signal.strategy_name} - {signal.symbol1}/{signal.symbol2}")
        
        def alert_callback(alert):
            alerts_received.append(alert)
            print(f"🚨 新警报: {alert.strategy_name} - 置信度{alert.confidence:.1%}")
        
        # 注册回调
        coordinator.add_ui_callback(ui_callback)
        coordinator.add_signal_callback(signal_callback)
        coordinator.add_alert_callback(alert_callback)
        
        print("✅ 回调函数注册成功")
        
        # 启动协调器
        coordinator.start()
        print("🚀 协调器已启动，开始监控...")
        
        # 运行15秒观察
        start_time = time.time()
        while time.time() - start_time < 15:
            time.sleep(1)
            
            # 每5秒显示状态
            if int(time.time() - start_time) % 5 == 0:
                status = coordinator.get_system_status()
                print(f"💓 系统状态: 运行中, 活跃机会: {status['active_opportunities']}")
        
        # 停止协调器
        coordinator.stop()
        print("⏹️ 协调器已停止")
        
        # 显示结果
        print(f"\n📊 测试结果:")
        print(f"  UI更新次数: {len(ui_updates)}")
        print(f"  接收信号数: {len(signals_received)}")
        print(f"  接收警报数: {len(alerts_received)}")
        
        # 强制扫描测试
        print(f"\n🔍 测试强制扫描:")
        coordinator.start()
        time.sleep(2)  # 等待系统稳定
        opportunities = coordinator.force_strategy_scan()
        print(f"  强制扫描发现: {len(opportunities)} 个机会")
        coordinator.stop()
        
        return len(ui_updates) > 0 and len(signals_received) > 0
        
    except Exception as e:
        print(f"❌ 实时协调器测试失败: {e}")
        return False

def test_strategy_auto_discovery():
    """测试策略自动发现"""
    print("\n🤖 测试策略自动发现")
    print("=" * 50)
    
    try:
        from core.realtime_coordinator import RealTimeCoordinator
        
        coordinator = RealTimeCoordinator()
        
        # 模拟市场数据
        market_data = {
            '000001.SZ': {'price': 10.5, 'change': -0.06, 'volume': 5000000},  # 爆跌
            '000002.SZ': {'price': 12.3, 'change': -0.05, 'volume': 4000000},
            '600000.SH': {'price': 8.9, 'change': 0.08, 'volume': 3000000},   # 爆涨
            '600036.SH': {'price': 15.6, 'change': 0.07, 'volume': 2500000}
        }
        
        # 更新市场数据
        coordinator.current_market_data = market_data
        print(f"📊 设置市场数据: {len(market_data)} 只股票")
        
        # 运行策略分析
        opportunities = coordinator._run_all_strategies()
        print(f"🎯 策略分析结果: 发现 {len(opportunities)} 个机会")
        
        # 显示机会详情
        for i, opp in enumerate(opportunities):
            print(f"  机会 {i+1}: {opp.strategy_name}")
            print(f"    交易对: {' / '.join(opp.symbols)}")
            print(f"    置信度: {opp.confidence:.1%}")
            print(f"    预期收益: {opp.expected_return:.2%}")
            print(f"    是否紧急: {'是' if opp.is_emergency else '否'}")
        
        # 测试风险检测
        print(f"\n⚠️ 测试风险检测:")
        should_emergency, signal = coordinator.risk_manager.should_trigger_emergency_arbitrage(market_data)
        print(f"  触发临时套利: {'是' if should_emergency else '否'}")
        if signal:
            print(f"  建议操作: {signal.suggested_action}")
            print(f"  置信度: {signal.confidence:.1%}")
        
        return len(opportunities) > 0
        
    except Exception as e:
        print(f"❌ 策略自动发现测试失败: {e}")
        return False

def test_database_integration():
    """测试数据库集成"""
    print("\n📊 测试数据库集成")
    print("=" * 50)
    
    try:
        from database.signal_database import SignalDatabase, TradingSignal
        from datetime import datetime
        
        # 创建数据库
        db = SignalDatabase("test_realtime_integration.db")
        print("✅ 数据库创建成功")
        
        # 模拟保存多个信号
        strategies = ['配对交易', 'ML增强', '多因子', '临时套利']
        saved_count = 0
        
        for i in range(10):
            signal = TradingSignal(
                timestamp=datetime.now(),
                strategy_name=strategies[i % len(strategies)],
                symbol1=f"00000{i%5+1}.SZ",
                symbol2=f"60000{i%5+1}.SH",
                signal_type="LONG_SHORT",
                confidence=0.6 + (i % 4) * 0.1,
                expected_return=0.01 + (i % 3) * 0.01,
                risk_level=["LOW", "MEDIUM", "HIGH"][i % 3],
                entry_price1=10 + i,
                entry_price2=12 + i,
                performance_score=0.7 + (i % 3) * 0.1,
                is_historical=False
            )
            
            signal_id = db.save_signal(signal)
            saved_count += 1
            
            if i % 3 == 0:  # 每3个显示一次
                print(f"💾 保存信号 {signal_id}: {signal.strategy_name}")
        
        print(f"✅ 总共保存 {saved_count} 个信号")
        
        # 测试查询功能
        realtime_signals = db.get_realtime_signals(hours=1)
        print(f"📈 查询到 {len(realtime_signals)} 个实时信号")
        
        # 测试统计功能
        try:
            stats = db.get_strategy_statistics()
            print(f"📊 策略统计:")
            for strategy, stat in stats.items():
                if isinstance(stat, dict):
                    print(f"  {strategy}: {stat.get('count', 0)}个信号")
        except Exception as e:
            print(f"⚠️ 统计查询警告: {e}")
        
        # 测试数据库信息
        db_info = db.get_database_info()
        print(f"🗄️ 数据库信息: {db_info.get('total_records', 0)} 条记录")
        
        return saved_count > 0 and len(realtime_signals) > 0
        
    except Exception as e:
        print(f"❌ 数据库集成测试失败: {e}")
        return False

def test_ui_integration():
    """测试UI集成"""
    print("\n🖥️ 测试UI集成")
    print("=" * 50)
    
    try:
        # 模拟UI数据结构
        ui_data = {
            'market_data': {
                '000001.SZ': {'price': 10.5, 'change': 0.02, 'volume': 3000000},
                '000002.SZ': {'price': 12.3, 'change': -0.01, 'volume': 2500000}
            },
            'recent_signals': [],
            'active_opportunities': [],
            'system_metrics': {
                'total_signals_today': 5,
                'current_pnl': 1200.0,
                'active_positions': 3
            },
            'risk_status': {
                'current_mode': 'normal',
                'position_limit': 0.7,
                'total_capital': 201200,
                'available_cash': 60000
            },
            'timestamp': datetime.now()
        }
        
        print("✅ UI数据结构验证成功")
        print(f"📊 市场数据: {len(ui_data['market_data'])} 只股票")
        print(f"💰 总资产: ¥{ui_data['risk_status']['total_capital']:,.0f}")
        print(f"📈 今日信号: {ui_data['system_metrics']['total_signals_today']} 个")
        print(f"⚠️ 仓位限制: {ui_data['risk_status']['position_limit']:.0%}")
        
        # 测试数据格式化
        market_data = ui_data['market_data']
        formatted_data = []
        
        for symbol, data in market_data.items():
            formatted_data.append({
                '股票代码': symbol,
                '当前价格': f"¥{data['price']:.2f}",
                '涨跌幅': f"{data['change']:+.2%}",
                '成交量': f"{data['volume']:,}"
            })
        
        print(f"✅ 数据格式化成功: {len(formatted_data)} 条记录")
        
        # 模拟警报数据
        from core.realtime_coordinator import OpportunityAlert
        
        alert = OpportunityAlert(
            opportunity_id="test_001",
            strategy_name="配对交易",
            symbols=['000001.SZ', '000002.SZ'],
            confidence=0.85,
            expected_return=0.025,
            risk_level="MEDIUM",
            alert_time=datetime.now(),
            is_emergency=False
        )
        
        print(f"🚨 警报测试: {alert.strategy_name} - 置信度{alert.confidence:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")
        return False

def test_full_integration():
    """测试完整集成"""
    print("\n🔗 测试完整系统集成")
    print("=" * 50)
    
    try:
        from core.realtime_coordinator import RealTimeCoordinator
        
        # 创建完整系统
        coordinator = RealTimeCoordinator(initial_capital=200000)
        
        # 收集所有事件
        all_events = []
        
        def collect_ui_updates(data):
            all_events.append(('ui_update', data))
        
        def collect_signals(signal):
            all_events.append(('signal', signal))
        
        def collect_alerts(alert):
            all_events.append(('alert', alert))
        
        # 注册收集器
        coordinator.add_ui_callback(collect_ui_updates)
        coordinator.add_signal_callback(collect_signals)
        coordinator.add_alert_callback(collect_alerts)
        
        print("🚀 启动完整系统...")
        coordinator.start()
        
        # 运行10秒收集数据
        print("📊 收集系统运行数据...")
        time.sleep(10)
        
        # 执行强制扫描
        print("🔍 执行强制策略扫描...")
        opportunities = coordinator.force_strategy_scan()
        
        # 再等待5秒
        time.sleep(5)
        
        coordinator.stop()
        print("⏹️ 系统已停止")
        
        # 分析收集的事件
        ui_updates = [e for e in all_events if e[0] == 'ui_update']
        signals = [e for e in all_events if e[0] == 'signal']
        alerts = [e for e in all_events if e[0] == 'alert']
        
        print(f"\n📈 系统运行结果:")
        print(f"  UI更新事件: {len(ui_updates)} 次")
        print(f"  生成信号: {len(signals)} 个")
        print(f"  触发警报: {len(alerts)} 个")
        print(f"  强制扫描机会: {len(opportunities)} 个")
        
        # 获取最终系统状态
        final_status = coordinator.get_system_status()
        print(f"\n🎯 最终系统状态:")
        print(f"  监控股票: {final_status['monitored_symbols']} 只")
        print(f"  活跃机会: {final_status['active_opportunities']} 个")
        print(f"  系统指标: {final_status['system_metrics']}")
        
        # 成功条件：至少有UI更新和信号生成
        success = len(ui_updates) > 0 and (len(signals) > 0 or len(opportunities) > 0)
        
        if success:
            print("🎉 完整系统集成测试成功！")
        else:
            print("⚠️ 系统运行但事件较少，可能需要调整参数")
        
        return success
        
    except Exception as e:
        print(f"❌ 完整集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("⚡ 实时集成系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("实时协调器", test_realtime_coordinator),
        ("策略自动发现", test_strategy_auto_discovery),
        ("数据库集成", test_database_integration),
        ("UI集成", test_ui_integration),
        ("完整系统集成", test_full_integration)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            result = test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: 测试通过")
            else:
                print(f"❌ {test_name}: 测试失败")
                
        except Exception as e:
            print(f"💥 {test_name}: 测试异常 - {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n📋 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！实时集成系统运行正常！")
        print("🚀 可以启动 realtime_integrated_app.py 查看完整界面")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过，系统基本可用！")
        print("💡 建议检查失败的测试项并优化")
    else:
        print("⚠️ 多个测试失败，需要进一步调试")
        print("🔧 建议检查模块依赖和配置")

if __name__ == "__main__":
    main()
