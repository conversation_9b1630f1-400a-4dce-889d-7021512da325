"""
简化的综合套利模块测试
测试核心功能而不依赖复杂的外部库
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_strategy_combiner_basic():
    """测试策略组合器基本功能"""
    print("\n🔗 测试策略组合器基本功能")
    print("=" * 50)
    
    try:
        from strategies.strategy_combiner import StrategyCombiner, CombinationMethod
        
        # 创建策略组合器
        combiner = StrategyCombiner(CombinationMethod.EQUAL_WEIGHT)
        print("✅ 策略组合器创建成功")
        
        # 测试等权重分配
        combiner.strategy_names = ['策略A', '策略B', '策略C']
        weights = combiner._equal_weight()
        print(f"✅ 等权重分配: {weights}")
        
        # 测试权重约束
        test_weights = {'策略A': 0.6, '策略B': 0.3, '策略C': 0.1}
        constrained = combiner._apply_weight_constraints(test_weights)
        print(f"✅ 权重约束后: {constrained}")
        
        # 测试配置摘要
        summary = combiner.get_strategy_allocation_summary()
        print(f"✅ 配置摘要: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略组合器测试失败: {e}")
        return False


def test_advanced_strategies_basic():
    """测试高级策略基本功能"""
    print("\n🤖 测试高级策略基本功能")
    print("=" * 50)
    
    try:
        from strategies.advanced_arbitrage_strategies import MLEnhancedPairsStrategy, MultiFactorArbitrageStrategy
        
        # 测试ML增强策略
        ml_strategy = MLEnhancedPairsStrategy(lookback_period=30)
        print("✅ ML增强策略创建成功")
        
        # 测试RSI计算
        test_prices = [10, 10.5, 11, 10.8, 11.2, 10.9, 11.5, 11.1, 10.7, 11.3]
        rsi = ml_strategy._calculate_rsi(test_prices)
        print(f"✅ RSI计算: {rsi:.2f}")
        
        # 测试多因子策略
        factor_weights = {'value': 0.3, 'momentum': 0.3, 'quality': 0.4}
        multifactor_strategy = MultiFactorArbitrageStrategy(factor_weights)
        print("✅ 多因子策略创建成功")
        print(f"✅ 因子权重: {multifactor_strategy.factor_weights}")
        
        # 测试因子得分计算
        market_data = {
            '000001.SZ': {'price': 10.5, 'volume': 1000000, 'change': 0.02},
            '000002.SZ': {'price': 12.3, 'volume': 800000, 'change': -0.01}
        }
        
        factor_scores = multifactor_strategy._calculate_factor_scores(market_data)
        print(f"✅ 因子得分: {factor_scores}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级策略测试失败: {e}")
        return False


def test_risk_manager_basic():
    """测试风险管理器基本功能"""
    print("\n⚠️ 测试风险管理器基本功能")
    print("=" * 50)
    
    try:
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits, TradingMode
        
        # 创建风险管理器
        limits = DynamicPositionLimits(initial_capital=200000)
        risk_manager = DynamicRiskManager(limits)
        print("✅ 动态风险管理器创建成功")
        
        # 测试基本属性
        print(f"✅ 初始资金: ¥{risk_manager.limits.initial_capital:,.0f}")
        print(f"✅ 正常模式仓位限制: {risk_manager.limits.normal_position_ratio:.0%}")
        print(f"✅ 临时套利仓位限制: {risk_manager.limits.emergency_position_ratio:.0%}")
        
        # 测试当前仓位限制
        current_limit = risk_manager.get_current_position_limit()
        print(f"✅ 当前仓位限制: {current_limit:.0%}")
        
        # 测试市场状况检测
        market_data = {
            '000001.SZ': {'price': 10.5, 'change': -0.06, 'volume': 3000000},  # 爆跌6%
            '000002.SZ': {'price': 12.3, 'change': -0.04, 'volume': 2000000}
        }
        
        market_condition = risk_manager.detect_market_condition(market_data)
        print(f"✅ 市场状况: {market_condition.value}")
        
        # 测试临时套利触发
        should_trigger, signal = risk_manager.should_trigger_emergency_arbitrage(market_data)
        print(f"✅ 是否触发临时套利: {should_trigger}")
        
        if signal:
            print(f"✅ 临时套利信号: {signal.suggested_action}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险管理器测试失败: {e}")
        return False


def test_database_basic():
    """测试数据库基本功能"""
    print("\n📊 测试数据库基本功能")
    print("=" * 50)
    
    try:
        from database.signal_database import SignalDatabase, TradingSignal
        
        # 创建数据库
        db = SignalDatabase("test_simple.db")
        print("✅ 信号数据库创建成功")
        
        # 创建测试信号
        test_signal = TradingSignal(
            timestamp=datetime.now(),
            strategy_name="测试策略",
            symbol1="000001.SZ",
            symbol2="000002.SZ",
            signal_type="LONG_SHORT",
            confidence=0.85,
            expected_return=0.02,
            risk_level="MEDIUM",
            entry_price1=10.5,
            entry_price2=12.3,
            performance_score=0.75,
            is_historical=False
        )
        
        # 保存信号
        signal_id = db.save_signal(test_signal)
        print(f"✅ 保存信号成功，ID: {signal_id}")
        
        # 获取信号
        signals = db.get_realtime_signals(hours=1)
        print(f"✅ 获取实时信号: {len(signals)}个")
        
        # 获取数据库信息
        db_info = db.get_database_info()
        print(f"✅ 数据库信息: {db_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_integration_simple():
    """简化的系统集成测试"""
    print("\n🔗 简化系统集成测试")
    print("=" * 50)
    
    try:
        from strategies.strategy_combiner import StrategyCombiner, CombinationMethod
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
        
        # 创建核心组件
        limits = DynamicPositionLimits(initial_capital=200000)
        risk_manager = DynamicRiskManager(limits)
        combiner = StrategyCombiner(CombinationMethod.DYNAMIC_WEIGHT)
        
        print("✅ 核心组件创建成功")
        
        # 模拟市场数据
        market_data = {
            '000001.SZ': {'price': 10.5, 'change': 0.02, 'volume': 3000000},
            '000002.SZ': {'price': 12.3, 'change': -0.01, 'volume': 2500000}
        }
        
        # 1. 风险检查
        current_limit = risk_manager.get_current_position_limit()
        print(f"✅ 当前仓位限制: {current_limit:.0%}")
        
        # 2. 市场状况检测
        market_condition = risk_manager.detect_market_condition(market_data)
        print(f"✅ 市场状况: {market_condition.value}")
        
        # 3. 策略权重计算
        combiner.strategy_names = ['配对交易', '统计套利', 'ML增强']
        weights = combiner.calculate_strategy_weights()
        print(f"✅ 策略权重: {weights}")
        
        # 4. 系统状态摘要
        summary = {
            'total_capital': risk_manager.total_capital,
            'available_cash': risk_manager.available_cash,
            'current_mode': risk_manager.current_mode.value,
            'strategy_count': len(combiner.strategy_names)
        }
        print(f"✅ 系统状态: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 简化综合套利模块测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("策略组合器基本功能", test_strategy_combiner_basic),
        ("高级策略基本功能", test_advanced_strategies_basic),
        ("风险管理器基本功能", test_risk_manager_basic),
        ("数据库基本功能", test_database_basic),
        ("简化系统集成", test_integration_simple)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n📋 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！综合套利模块基本功能正常！")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过，系统基本可用！")
    else:
        print("⚠️ 多个测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
