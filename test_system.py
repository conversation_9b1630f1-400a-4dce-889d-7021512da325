#!/usr/bin/env python3
"""
系统测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("�� 测试模块导入...")
    
    try:
        from src.models.stochastic_models import BlackScholesModel, BSParameters
        print("✅ 随机微积分模型导入成功")
    except Exception as e:
        print(f"❌ 随机微积分模型导入失败: {e}")
        return False
    
    try:
        from src.trading.market_rules import ChinaStockMarketRules, StockType
        print("✅ 市场交易规则导入成功")
    except Exception as e:
        print(f"❌ 市场交易规则导入失败: {e}")
        return False
    
    try:
        from src.data.data_provider import AkshareProvider, MarketData
        print("✅ 数据提供者导入成功")
    except Exception as e:
        print(f"❌ 数据提供者导入失败: {e}")
        return False
    
    try:
        from src.arbitrage_engine import ArbitrageEngine, ArbitrageSignal
        print("✅ 套利引擎导入成功")
    except Exception as e:
        print(f"❌ 套利引擎导入失败: {e}")
        return False
    
    return True

def test_black_scholes():
    """测试Black-Scholes模型"""
    print("\n📊 测试Black-Scholes模型...")
    
    try:
        from src.models.stochastic_models import BlackScholesModel, BSParameters
        
        # 创建模型参数
        params = BSParameters(S0=100, mu=0.05, sigma=0.2, r=0.03, T=1.0)
        model = BlackScholesModel(params)
        
        # 测试期权定价
        call_price = model.option_price(105, "call")
        put_price = model.option_price(105, "put")
        
        print(f"  看涨期权价格: {call_price:.4f}")
        print(f"  看跌期权价格: {put_price:.4f}")
        
        # 测试希腊字母
        greeks = model.greeks(105, "call")
        print(f"  Delta: {greeks['delta']:.4f}")
        print(f"  Gamma: {greeks['gamma']:.4f}")
        
        # 测试路径模拟
        paths = model.simulate(n_paths=3, n_steps=5)
        print(f"  模拟路径形状: {paths.shape}")
        
        print("✅ Black-Scholes模型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Black-Scholes模型测试失败: {e}")
        return False

def test_market_rules():
    """测试市场交易规则"""
    print("\n📋 测试市场交易规则...")
    
    try:
        from src.trading.market_rules import ChinaStockMarketRules, StockType
        from datetime import datetime
        
        rules = ChinaStockMarketRules()
        
        # 测试涨跌停计算
        limits = rules.get_price_limits(10.0, StockType.MAIN_BOARD)
        print(f"  主板涨停价: {limits.up_limit}, 跌停价: {limits.down_limit}")
        
        # 测试价格验证
        is_valid, msg = rules.validate_order_price(10.5, 10.0, StockType.MAIN_BOARD)
        print(f"  价格验证: {is_valid}, 消息: {msg}")
        
        # 测试数量验证
        is_valid, msg = rules.validate_order_quantity(100)
        print(f"  数量验证: {is_valid}, 消息: {msg}")
        
        # 测试交易成本
        costs = rules.calculate_transaction_cost(10000, is_buy=True)
        print(f"  交易成本: {costs['total_cost']:.2f}元")
        
        print("✅ 市场交易规则测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 市场交易规则测试失败: {e}")
        return False

def test_arbitrage_engine():
    """测试套利引擎"""
    print("\n🤖 测试套利引擎...")
    
    try:
        from src.arbitrage_engine import ArbitrageEngine, PairTradingStrategy
        
        # 测试配对交易策略
        strategy = PairTradingStrategy()
        signal = strategy.generate_signal("000001.SZ", "000002.SZ", 10.0, 12.0)
        
        if signal:
            print(f"  生成信号: {signal.signal_type}")
            print(f"  置信度: {signal.confidence:.2f}")
        else:
            print("  未生成信号（正常情况）")
        
        # 测试套利引擎创建
        engine = ArbitrageEngine(initial_capital=1000000)
        print(f"  引擎初始资金: {engine.initial_capital:,}")
        
        print("✅ 套利引擎测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 套利引擎测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 量化套利系统测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_black_scholes,
        test_market_rules,
        test_arbitrage_engine
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
