"""
综合套利模块测试
测试策略组合器、高级套利策略、可视化仪表板等完善的功能
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from loguru import logger

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_strategy_combiner():
    """测试策略组合器"""
    print("\n🔗 测试策略组合器")
    print("=" * 50)
    
    try:
        from strategies.strategy_combiner import StrategyCombiner, CombinationMethod, StrategyWeight
        from strategies.strategy_optimizer import StrategyPerformance, StrategySignal
        
        # 创建策略组合器
        combiner = StrategyCombiner(CombinationMethod.DYNAMIC_WEIGHT)
        print("✅ 策略组合器创建成功")
        
        # 添加模拟策略性能数据
        strategies = ['配对交易', 'ML增强', '多因子', '统计套利']
        for i, strategy_name in enumerate(strategies):
            for j in range(10):  # 添加10个历史性能记录
                performance = StrategyPerformance(
                    strategy_name=strategy_name,
                    total_return=np.random.normal(0.02, 0.01),
                    sharpe_ratio=np.random.normal(1.2, 0.3),
                    max_drawdown=np.random.uniform(-0.1, -0.02),
                    win_rate=np.random.uniform(0.5, 0.8),
                    avg_return_per_trade=np.random.normal(0.001, 0.0005),
                    volatility=np.random.uniform(0.1, 0.3),
                    calmar_ratio=np.random.uniform(0.5, 2.0),
                    sortino_ratio=np.random.uniform(0.8, 2.5),
                    score=np.random.uniform(0.6, 0.9),
                    confidence=np.random.uniform(0.7, 0.95)
                )
                combiner.add_strategy_performance(strategy_name, performance)
        
        print(f"✅ 添加了{len(strategies)}个策略的历史性能数据")
        
        # 测试不同的权重计算方法
        methods = [
            CombinationMethod.EQUAL_WEIGHT,
            CombinationMethod.RISK_PARITY,
            CombinationMethod.PERFORMANCE_BASED,
            CombinationMethod.DYNAMIC_WEIGHT
        ]
        
        for method in methods:
            combiner.combination_method = method
            weights = combiner.calculate_strategy_weights()
            print(f"✅ {method.value}权重: {[(k, f'{v:.2%}') for k, v in weights.items()]}")
        
        # 测试信号组合
        mock_signals = []
        for strategy_name in strategies[:3]:
            signal = StrategySignal(
                timestamp=datetime.now(),
                strategy_name=strategy_name,
                symbol1=f"00000{np.random.randint(1,9)}.SZ",
                symbol2=f"60000{np.random.randint(1,9)}.SH",
                signal_type="LONG_SHORT",
                confidence=np.random.uniform(0.7, 0.95),
                expected_return=np.random.uniform(0.01, 0.03),
                risk_level="MEDIUM",
                entry_price1=np.random.uniform(10, 50),
                entry_price2=np.random.uniform(10, 50),
                performance_score=np.random.uniform(0.6, 0.9)
            )
            mock_signals.append(signal)
        
        # 组合信号
        combined_signal = combiner.combine_signals(mock_signals)
        if combined_signal:
            print(f"✅ 成功组合{len(mock_signals)}个信号")
            print(f"   组合置信度: {combined_signal.combined_confidence:.2f}")
            print(f"   组合预期收益: {combined_signal.combined_expected_return:.2%}")
            print(f"   分散化收益: {combined_signal.diversification_benefit:.2%}")
        
        # 获取配置摘要
        summary = combiner.get_strategy_allocation_summary()
        print(f"✅ 策略配置摘要: {summary['total_strategies']}个策略")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略组合器测试失败: {e}")
        return False


def test_advanced_strategies():
    """测试高级套利策略"""
    print("\n🤖 测试高级套利策略")
    print("=" * 50)
    
    try:
        from strategies.advanced_arbitrage_strategies import MLEnhancedPairsStrategy, MultiFactorArbitrageStrategy
        
        # 测试ML增强配对交易策略
        ml_strategy = MLEnhancedPairsStrategy(lookback_period=60)
        print("✅ ML增强配对交易策略创建成功")
        
        # 模拟市场数据
        market_data = {}
        symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
        
        for symbol in symbols:
            market_data[symbol] = {
                'price': np.random.uniform(10, 50),
                'volume': np.random.randint(1000000, 10000000),
                'change': np.random.normal(0, 0.02)
            }
        
        print(f"✅ 生成了{len(symbols)}只股票的模拟数据")
        
        # 扫描ML增强套利机会
        ml_opportunities = ml_strategy.scan_opportunities(market_data)
        print(f"✅ ML策略发现{len(ml_opportunities)}个套利机会")
        
        # 测试多因子套利策略
        factor_weights = {
            'value': 0.3,
            'momentum': 0.25,
            'quality': 0.2,
            'volatility': 0.15,
            'size': 0.1
        }
        
        multifactor_strategy = MultiFactorArbitrageStrategy(factor_weights)
        print("✅ 多因子套利策略创建成功")
        
        # 扫描多因子套利机会
        factor_opportunities = multifactor_strategy.scan_opportunities(market_data)
        print(f"✅ 多因子策略发现{len(factor_opportunities)}个套利机会")
        
        # 显示机会详情
        all_opportunities = ml_opportunities + factor_opportunities
        if all_opportunities:
            best_opportunity = max(all_opportunities, key=lambda x: x.confidence)
            print(f"✅ 最佳机会: {best_opportunity.strategy_type.value}")
            print(f"   交易对: {best_opportunity.symbols}")
            print(f"   置信度: {best_opportunity.confidence:.2f}")
            print(f"   预期收益: {best_opportunity.expected_return:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级套利策略测试失败: {e}")
        return False


def test_dashboard_components():
    """测试仪表板组件"""
    print("\n📊 测试仪表板组件")
    print("=" * 50)
    
    try:
        from visualization.dashboard import QuantitativeDashboard
        
        # 创建仪表板
        dashboard = QuantitativeDashboard()
        print("✅ 量化仪表板创建成功")
        
        # 检查组件初始化
        components = [
            ('风险管理器', dashboard.risk_manager),
            ('策略组合器', dashboard.strategy_combiner),
            ('策略优化器', dashboard.strategy_optimizer),
            ('数据库', dashboard.database),
            ('临时套利引擎', dashboard.emergency_engine)
        ]
        
        for name, component in components:
            if component is not None:
                print(f"✅ {name}初始化成功")
            else:
                print(f"⚠️ {name}初始化失败")
        
        print("✅ 仪表板组件测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 仪表板组件测试失败: {e}")
        return False


def test_integration():
    """测试系统集成"""
    print("\n🔗 测试系统集成")
    print("=" * 50)
    
    try:
        from strategies.strategy_combiner import StrategyCombiner, CombinationMethod
        from strategies.advanced_arbitrage_strategies import MLEnhancedPairsStrategy
        from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits
        from database.signal_database import SignalDatabase
        
        # 创建集成系统
        limits = DynamicPositionLimits(initial_capital=200000)
        risk_manager = DynamicRiskManager(limits)
        combiner = StrategyCombiner(CombinationMethod.DYNAMIC_WEIGHT)
        ml_strategy = MLEnhancedPairsStrategy()
        database = SignalDatabase("integration_test.db")
        
        print("✅ 所有组件创建成功")
        
        # 模拟完整的交易流程
        market_data = {
            '000001.SZ': {'price': 10.5, 'change': 0.02, 'volume': 3000000},
            '000002.SZ': {'price': 12.3, 'change': -0.01, 'volume': 2500000},
            '600000.SH': {'price': 8.9, 'change': 0.015, 'volume': 4000000}
        }
        
        # 1. 检测市场状况
        should_emergency, emergency_signal = risk_manager.should_trigger_emergency_arbitrage(market_data)
        print(f"✅ 市场状况检测: 是否触发临时套利 = {should_emergency}")
        
        # 2. 扫描套利机会
        opportunities = ml_strategy.scan_opportunities(market_data)
        print(f"✅ 发现{len(opportunities)}个套利机会")
        
        # 3. 风险检查
        risk_manager.update_total_capital()
        current_limit = risk_manager.get_current_position_limit()
        print(f"✅ 当前仓位限制: {current_limit:.0%}")
        
        # 4. 保存到数据库
        db_info = database.get_database_info()
        print(f"✅ 数据库状态: {db_info['total_tables']}个表")
        
        print("✅ 系统集成测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 综合套利模块完善测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("策略组合器", test_strategy_combiner),
        ("高级套利策略", test_advanced_strategies),
        ("仪表板组件", test_dashboard_components),
        ("系统集成", test_integration)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n📋 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！综合套利模块完善成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
