#!/usr/bin/env python3
"""
交易系统安全检查脚本
检查系统的资金安全和风险控制机制
"""

import sys
import os
from datetime import datetime

class TradingSafetyChecker:
    """交易安全检查器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.recommendations = []
    
    def add_issue(self, category: str, description: str, severity: str = "HIGH"):
        """添加安全问题"""
        self.issues.append({
            'category': category,
            'description': description,
            'severity': severity,
            'timestamp': datetime.now()
        })
    
    def check_arbitrage_engine_safety(self):
        """检查套利引擎安全性"""
        print("🔍 检查套利引擎安全性...")
        
        try:
            with open('src/arbitrage_engine.py', 'r') as f:
                content = f.read()
            
            # 检查资金管理
            if 'initial_capital' in content:
                print("  ✅ 发现初始资金设置")
            else:
                self.add_issue("资金管理", "缺少初始资金设置", "HIGH")
            
            # 检查止损机制
            if 'stop_loss' not in content.lower():
                self.add_issue("风险控制", "缺少止损机制", "CRITICAL")
            
            # 检查交易验证
            if 'validate' not in content.lower():
                self.add_issue("交易安全", "缺少交易验证机制", "HIGH")
            
        except FileNotFoundError:
            self.add_issue("文件完整性", "套利引擎文件不存在", "CRITICAL")
    
    def check_trading_execution_safety(self):
        """检查交易执行安全性"""
        print("🔍 检查交易执行安全性...")
        
        # 检查是否有实际的交易执行模块
        trading_files = [
            'src/trading/safe_trading_manager.py',
            'src/trading/order_manager.py',
        ]
        
        found_trading_module = False
        for file_path in trading_files:
            if os.path.exists(file_path):
                found_trading_module = True
                print(f"  ✅ 发现交易模块: {file_path}")
                break
        
        if not found_trading_module:
            self.add_issue("交易执行", "缺少安全的交易执行模块", "CRITICAL")
    
    def generate_safety_report(self):
        """生成安全报告"""
        print("\n" + "="*60)
        print("🛡️ 交易系统安全检查报告")
        print("="*60)
        
        # 统计
        critical_issues = len([i for i in self.issues if i['severity'] == 'CRITICAL'])
        high_issues = len([i for i in self.issues if i['severity'] == 'HIGH'])
        
        print(f"\n📊 安全状况概览:")
        print(f"  🔴 严重问题: {critical_issues}")
        print(f"  🟠 高风险问题: {high_issues}")
        
        # 详细问题
        if self.issues:
            print(f"\n🚨 发现的安全问题:")
            for i, issue in enumerate(self.issues, 1):
                severity_icon = {"CRITICAL": "��", "HIGH": "🟠"}.get(issue['severity'], "⚪")
                print(f"  {i}. {severity_icon} [{issue['category']}] {issue['description']}")
        
        # 安全评级
        total_score = 100
        total_score -= critical_issues * 30
        total_score -= high_issues * 15
        total_score = max(0, total_score)
        
        print(f"\n🎯 安全评分: {total_score}/100")
        
        if total_score >= 90:
            print("✅ 安全状况良好")
        elif total_score >= 70:
            print("⚠️ 安全状况一般，建议改进")
        else:
            print("🔴 安全风险极高，不建议进行真实交易")
        
        return total_score
    
    def run_full_safety_check(self):
        """运行完整安全检查"""
        print("🚀 开始交易系统安全检查...")
        print("="*60)
        
        self.check_arbitrage_engine_safety()
        self.check_trading_execution_safety()
        
        score = self.generate_safety_report()
        return score

def main():
    """主函数"""
    checker = TradingSafetyChecker()
    safety_score = checker.run_full_safety_check()
    
    print(f"\n{'='*60}")
    if safety_score < 70:
        print("⚠️ 警告: 当前系统安全性不足，不建议进行真实资金交易")
    else:
        print("✅ 系统基本安全要求已满足")
    
    return safety_score

if __name__ == "__main__":
    main()
