"""
统一量化套利系统界面
集成实时监控、风险控制、动态资金池等所有功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import time
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from core.realtime_coordinator import RealTimeCoordinator, OpportunityAlert
    from risk.enhanced_risk_manager import EnhancedRiskManager, PositionLimits
    from risk.dynamic_risk_manager import DynamicRiskManager, DynamicPositionLimits, TradingMode, MarketCondition
    from strategies.strategy_optimizer import StrategyOptimizer, TradingSignal as OptSignal
    from strategies.emergency_arbitrage import EmergencyArbitrageEngine, EmergencySignal
    from database.signal_database import SignalDatabase, TradingSignal
    from data.enhanced_data_manager import EnhancedDataManager
except ImportError as e:
    st.error(f"模块导入失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="统一量化套利系统",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin: 0.5rem 0;
    }
    .alert-card {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    .emergency-alert {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    .status-running {
        color: #28a745;
        font-weight: bold;
    }
    .status-stopped {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'coordinator' not in st.session_state:
    st.session_state.coordinator = RealTimeCoordinator(initial_capital=200000)
    st.session_state.enhanced_risk_manager = EnhancedRiskManager(initial_capital=200000)
    st.session_state.dynamic_risk_manager = DynamicRiskManager(initial_capital=200000)
    st.session_state.emergency_engine = EmergencyArbitrageEngine()
    st.session_state.running = False
    st.session_state.ui_data = {}
    st.session_state.alerts = []
    st.session_state.signals_today = []
    
    # 初始化数据管理器
    try:
        st.session_state.data_manager = EnhancedDataManager("config/data_sources.json")
    except Exception as e:
        st.warning(f"数据管理器初始化失败: {e}")
        st.session_state.data_manager = None

def create_header():
    """创建页面头部"""
    st.markdown("""
    <div class="main-header">
        <h1>🚀 统一量化套利系统 v5.0</h1>
        <p>实时监控 | 风险控制 | 动态资金池 | 数据源管理 | 一体化解决方案</p>
    </div>
    """, unsafe_allow_html=True)

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.title("🎛️ 系统控制")
        
        # 系统状态
        if st.session_state.running:
            st.markdown('<p class="status-running">🟢 系统运行中</p>', unsafe_allow_html=True)
            if st.button("⏹️ 停止系统", use_container_width=True):
                st.session_state.coordinator.stop()
                st.session_state.running = False
                st.success("系统已停止")
                st.rerun()
        else:
            st.markdown('<p class="status-stopped">🔴 系统已停止</p>', unsafe_allow_html=True)
            if st.button("🚀 启动系统", use_container_width=True):
                st.session_state.coordinator.start()
                st.session_state.running = True
                st.success("系统已启动")
                st.rerun()
        
        # 快速操作
        st.markdown("---")
        st.subheader("⚡ 快速操作")
        
        if st.button("🔍 强制扫描", use_container_width=True):
            if st.session_state.running:
                opportunities = st.session_state.coordinator.scan_opportunities()
                st.success(f"发现 {len(opportunities)} 个机会")
                st.session_state.alerts.extend(opportunities)
            else:
                st.warning("请先启动系统")
        
        if st.button("📊 生成报告", use_container_width=True):
            st.info("报告生成功能开发中...")
        
        # 系统模式选择
        st.markdown("---")
        st.subheader("🎯 系统模式")
        
        mode = st.selectbox(
            "选择运行模式",
            ["标准模式", "保守模式", "激进模式", "临时套利模式"],
            index=0
        )
        
        if mode == "临时套利模式":
            st.warning("⚠️ 临时套利模式风险较高")
            if st.button("🚨 确认进入临时模式"):
                st.session_state.dynamic_risk_manager.enter_emergency_mode()
                st.success("已进入临时套利模式")
        
        # 资金设置
        st.markdown("---")
        st.subheader("💰 资金设置")
        
        current_capital = st.number_input(
            "当前资金 (元)",
            min_value=10000,
            max_value=10000000,
            value=200000,
            step=10000
        )
        
        position_ratio = st.slider(
            "仓位比例",
            min_value=0.1,
            max_value=0.85,
            value=0.7,
            step=0.05
        )
        
        # 数据源状态
        st.markdown("---")
        st.subheader("📊 数据源状态")
        
        if st.session_state.data_manager:
            sources_status = st.session_state.data_manager.get_data_sources_status()
            for name, status in sources_status.items():
                status_icon = "✅" if status['connected'] else "❌"
                st.write(f"{status_icon} {name}")
        
        # 系统信息
        st.markdown("---")
        st.subheader("ℹ️ 系统信息")
        
        if st.session_state.running:
            status = st.session_state.coordinator.get_system_status()
            st.write(f"监控股票: {status['monitored_symbols']}")
            st.write(f"活跃机会: {status['active_opportunities']}")
            st.write(f"系统模式: {status['risk_manager_status']['mode']}")

def create_overview_dashboard():
    """创建概览仪表板"""
    st.subheader("📊 系统概览")
    
    # 关键指标
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric(
            "总资金",
            "¥200,000",
            delta="0%"
        )
    
    with col2:
        st.metric(
            "可用资金", 
            "¥140,000",
            delta="-30%"
        )
    
    with col3:
        st.metric(
            "今日收益",
            "¥2,350",
            delta="+1.18%"
        )
    
    with col4:
        st.metric(
            "活跃机会",
            "12",
            delta="+3"
        )
    
    with col5:
        current_mode = st.session_state.dynamic_risk_manager.current_mode.value
        st.metric(
            "系统模式",
            current_mode,
            delta="正常"
        )
    
    # 实时图表
    col1, col2 = st.columns(2)
    
    with col1:
        # 资金使用情况
        fig_capital = go.Figure(data=[
            go.Pie(
                labels=['已使用', '可用资金', '风险保证金'],
                values=[60000, 140000, 20000],
                hole=0.4,
                marker_colors=['#ff6b6b', '#4ecdc4', '#45b7d1']
            )
        ])
        fig_capital.update_layout(
            title="资金分配情况",
            height=300
        )
        st.plotly_chart(fig_capital, use_container_width=True)
    
    with col2:
        # 策略收益对比
        strategies = ['配对交易', '统计套利', 'ML增强', '波动率套利', '临时套利']
        returns = [0.023, 0.018, 0.032, 0.028, 0.045]
        
        fig_strategy = go.Figure(data=[
            go.Bar(
                x=strategies,
                y=returns,
                marker_color=['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
            )
        ])
        fig_strategy.update_layout(
            title="策略收益对比",
            yaxis_title="收益率",
            height=300
        )
        st.plotly_chart(fig_strategy, use_container_width=True)

def create_realtime_monitoring():
    """创建实时监控面板"""
    st.subheader("📈 实时市场监控")
    
    # 模拟实时数据
    symbols = ['000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '600000.SH', '600036.SH']
    
    # 生成模拟数据
    market_data = []
    for symbol in symbols:
        price = 10 + np.random.normal(0, 2)
        change = np.random.normal(0, 0.02)
        volume = np.random.randint(1000000, 10000000)
        
        market_data.append({
            '股票代码': symbol,
            '当前价格': f"¥{price:.2f}",
            '涨跌幅': f"{change:.2%}",
            '成交量': f"{volume:,}",
            '成交额': f"¥{price * volume / 10000:.0f}万",
            '状态': '正常' if abs(change) < 0.03 else '异常'
        })
    
    df = pd.DataFrame(market_data)
    
    # 添加颜色样式
    def highlight_status(val):
        if val == '异常':
            return 'background-color: #ffebee'
        return ''
    
    styled_df = df.style.applymap(highlight_status, subset=['状态'])
    st.dataframe(styled_df, use_container_width=True)
    
    # 价格走势图
    st.subheader("📊 价格走势")
    
    # 生成模拟价格数据
    dates = pd.date_range(start=datetime.now() - timedelta(hours=6), end=datetime.now(), freq='5min')
    
    fig = go.Figure()
    
    for symbol in symbols[:3]:  # 只显示前3个
        prices = 10 + np.cumsum(np.random.normal(0, 0.1, len(dates)))
        fig.add_trace(go.Scatter(
            x=dates,
            y=prices,
            mode='lines',
            name=symbol,
            line=dict(width=2)
        ))
    
    fig.update_layout(
        title="主要股票价格走势",
        xaxis_title="时间",
        yaxis_title="价格 (¥)",
        height=400,
        hovermode='x unified'
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_opportunity_alerts():
    """创建机会警报面板"""
    st.subheader("🚨 套利机会警报")
    
    # 生成模拟警报
    if not st.session_state.alerts:
        # 初始化一些示例警报
        sample_alerts = [
            OpportunityAlert(
                strategy_name="配对交易",
                symbols=["000001.SZ", "000002.SZ"],
                confidence=0.85,
                expected_return=0.025,
                risk_level="MEDIUM",
                alert_time=datetime.now() - timedelta(minutes=5),
                is_emergency=False
            ),
            OpportunityAlert(
                strategy_name="临时套利",
                symbols=["600519.SH"],
                confidence=0.92,
                expected_return=0.045,
                risk_level="HIGH",
                alert_time=datetime.now() - timedelta(minutes=2),
                is_emergency=True
            ),
            OpportunityAlert(
                strategy_name="统计套利",
                symbols=["600000.SH", "600036.SH"],
                confidence=0.78,
                expected_return=0.018,
                risk_level="LOW",
                alert_time=datetime.now() - timedelta(minutes=8),
                is_emergency=False
            )
        ]
        st.session_state.alerts = sample_alerts
    
    # 显示警报统计
    col1, col2, col3, col4 = st.columns(4)
    
    total_alerts = len(st.session_state.alerts)
    emergency_alerts = sum(1 for alert in st.session_state.alerts if alert.is_emergency)
    high_confidence = sum(1 for alert in st.session_state.alerts if alert.confidence > 0.8)
    avg_return = np.mean([alert.expected_return for alert in st.session_state.alerts]) if st.session_state.alerts else 0
    
    with col1:
        st.metric("总警报数", total_alerts)
    
    with col2:
        st.metric("紧急警报", emergency_alerts, delta=f"+{emergency_alerts}")
    
    with col3:
        st.metric("高置信度", high_confidence)
    
    with col4:
        st.metric("平均预期收益", f"{avg_return:.2%}")
    
    # 最新警报列表
    st.subheader("📋 最新警报")
    
    latest_alerts = st.session_state.alerts[-10:]
    
    for i, alert in enumerate(reversed(latest_alerts)):
        # 确定警报样式
        if alert.is_emergency:
            alert_type = "🚨 紧急套利"
            container_class = "emergency-alert"
        elif alert.confidence > 0.8:
            alert_type = "⭐ 高置信度"
            container_class = "alert-card"
        else:
            alert_type = "💡 一般机会"
            container_class = "alert-card"
        
        with st.container():
            st.markdown(f'<div class="{container_class}">', unsafe_allow_html=True)
            
            col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
            
            with col1:
                st.markdown(f"**{alert_type}**: {alert.strategy_name}")
                st.caption(f"交易对: {' / '.join(alert.symbols)}")
            
            with col2:
                st.markdown(f"置信度: **{alert.confidence:.1%}**")
                st.caption(f"预期收益: {alert.expected_return:.2%}")
            
            with col3:
                st.markdown(f"风险等级: **{alert.risk_level}**")
                time_diff = (datetime.now() - alert.alert_time).seconds
                st.caption(f"{time_diff}秒前")
            
            with col4:
                if st.button(f"执行", key=f"execute_{i}"):
                    st.success("信号已执行")
            
            st.markdown('</div>', unsafe_allow_html=True)

def create_risk_management():
    """创建风险管理面板"""
    st.subheader("🛡️ 风险管理")
    
    # 风险指标
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("### 📊 风险指标")
        
        # VaR计算
        var_95 = 0.05 * 200000  # 5% VaR
        st.metric("VaR (95%)", f"¥{var_95:,.0f}")
        
        # 最大回撤
        max_drawdown = 0.08
        st.metric("最大回撤", f"{max_drawdown:.1%}")
        
        # 夏普比率
        sharpe_ratio = 1.85
        st.metric("夏普比率", f"{sharpe_ratio:.2f}")
    
    with col2:
        st.markdown("### 📈 仓位控制")
        
        # 当前仓位
        current_position = 0.65
        st.metric("当前仓位", f"{current_position:.1%}")
        
        # 仓位限制
        position_limit = 0.70
        st.metric("仓位限制", f"{position_limit:.1%}")
        
        # 可用保证金
        available_margin = 0.35
        st.metric("可用保证金", f"{available_margin:.1%}")
    
    with col3:
        st.markdown("### ⚠️ 风险预警")
        
        # 风险等级
        risk_level = "中等"
        st.metric("风险等级", risk_level)
        
        # 预警状态
        if current_position > 0.8:
            st.error("🚨 仓位过高警告")
        elif current_position > 0.7:
            st.warning("⚠️ 仓位接近上限")
        else:
            st.success("✅ 仓位正常")
    
    # 风险分布图
    st.subheader("📊 风险分布")
    
    risk_categories = ['市场风险', '信用风险', '流动性风险', '操作风险', '模型风险']
    risk_values = [0.35, 0.15, 0.20, 0.10, 0.20]
    
    fig_risk = go.Figure(data=[
        go.Pie(
            labels=risk_categories,
            values=risk_values,
            hole=0.4,
            marker_colors=['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
        )
    ])
    
    fig_risk.update_layout(
        title="风险类型分布",
        height=400
    )
    
    st.plotly_chart(fig_risk, use_container_width=True)

def create_emergency_arbitrage():
    """创建临时套利面板"""
    st.subheader("🚨 临时套利监控")
    
    # 市场异常检测
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📊 市场异常检测")
        
        # 模拟市场数据
        market_conditions = {
            '000001.SZ': {'change': -0.06, 'volume_ratio': 2.5},
            '000002.SZ': {'change': 0.08, 'volume_ratio': 3.2},
            '600000.SH': {'change': -0.03, 'volume_ratio': 1.8},
            '600519.SH': {'change': 0.12, 'volume_ratio': 4.1}
        }
        
        for symbol, data in market_conditions.items():
            change = data['change']
            volume_ratio = data['volume_ratio']
            
            # 判断是否异常
            is_abnormal = abs(change) > 0.05 or volume_ratio > 3.0
            
            if is_abnormal:
                if change > 0.05:
                    st.error(f"🚀 {symbol}: 暴涨 {change:.1%} (成交量{volume_ratio:.1f}倍)")
                elif change < -0.05:
                    st.error(f"📉 {symbol}: 暴跌 {change:.1%} (成交量{volume_ratio:.1f}倍)")
                else:
                    st.warning(f"⚠️ {symbol}: 异常成交量 {volume_ratio:.1f}倍")
            else:
                st.success(f"✅ {symbol}: 正常 {change:.1%}")
    
    with col2:
        st.markdown("### 🎯 临时套利机会")
        
        # 检测到的临时套利机会
        emergency_opportunities = [
            {
                'symbol': '000002.SZ',
                'type': '暴涨回调',
                'confidence': 0.88,
                'expected_return': 0.045,
                'time_window': '15分钟'
            },
            {
                'symbol': '600519.SH',
                'type': '异常放量',
                'confidence': 0.92,
                'expected_return': 0.038,
                'time_window': '10分钟'
            }
        ]
        
        for opp in emergency_opportunities:
            st.markdown(f"""
            **{opp['symbol']}** - {opp['type']}
            - 置信度: {opp['confidence']:.1%}
            - 预期收益: {opp['expected_return']:.1%}
            - 时间窗口: {opp['time_window']}
            """)
            
            if st.button(f"🚨 执行临时套利", key=f"emergency_{opp['symbol']}"):
                st.success(f"已执行 {opp['symbol']} 临时套利")
    
    # 临时套利模式控制
    st.markdown("### 🎛️ 临时套利模式")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        current_mode = st.session_state.dynamic_risk_manager.current_mode
        if current_mode == TradingMode.EMERGENCY:
            st.error("🚨 当前模式: 临时套利")
            if st.button("🔄 退出临时模式"):
                st.session_state.dynamic_risk_manager.exit_emergency_mode()
                st.success("已退出临时套利模式")
                st.rerun()
        else:
            st.success("✅ 当前模式: 正常交易")
            if st.button("🚨 进入临时模式"):
                st.session_state.dynamic_risk_manager.enter_emergency_mode()
                st.success("已进入临时套利模式")
                st.rerun()
    
    with col2:
        # 仓位使用情况
        if current_mode == TradingMode.EMERGENCY:
            max_position = 0.85
            current_position = 0.78
        else:
            max_position = 0.70
            current_position = 0.65
        
        st.metric("最大仓位", f"{max_position:.0%}")
        st.metric("当前仓位", f"{current_position:.0%}")
    
    with col3:
        # 风险控制
        st.metric("止损线", "-3%")
        st.metric("止盈线", "+5%")

def create_data_source_management():
    """创建数据源管理面板"""
    st.subheader("📊 数据源管理")

    if not st.session_state.data_manager:
        st.error("数据管理器未初始化")
        return

    data_manager = st.session_state.data_manager

    # 数据源状态概览
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 🔌 数据源状态")
        sources_status = data_manager.get_data_sources_status()

        if sources_status:
            status_data = []
            for name, status in sources_status.items():
                status_data.append({
                    '数据源': name,
                    '类型': status['type'],
                    '状态': '✅ 已连接' if status['connected'] else '❌ 未连接',
                    '可用': '✅ 可用' if status['available'] else '❌ 不可用',
                    '优先级': status['priority']
                })

            df = pd.DataFrame(status_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无数据源")

    with col2:
        st.markdown("### 📊 统计信息")
        stats = data_manager.get_statistics()

        # 显示关键统计
        st.metric("总请求数", stats['total_requests'])
        st.metric("缓存命中率", stats['cache_hit_rate'])
        st.metric("API调用次数", stats['api_calls'])
        st.metric("错误次数", stats['errors'])

    # 数据质量报告
    st.markdown("### 📋 数据质量报告")
    quality_report = data_manager.get_quality_report()

    if 'message' not in quality_report:
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("平均质量", f"{quality_report['average_quality']:.2f}")

        with col2:
            st.metric("高质量数据", quality_report['high_quality_count'])

        with col3:
            st.metric("总股票数", quality_report['total_symbols'])
    else:
        st.info(quality_report['message'])

    # 数据源操作
    st.markdown("### 🔧 数据源操作")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🔄 刷新状态", use_container_width=True):
            st.rerun()

    with col2:
        if st.button("🗑️ 清空缓存", use_container_width=True):
            data_manager.clear_cache()
            st.success("缓存已清空")

    with col3:
        if st.button("📊 测试数据", use_container_width=True):
            try:
                test_symbols = ['000001.SZ', '000002.SZ']
                test_data = data_manager.get_realtime_data(test_symbols)
                st.success(f"成功获取 {len(test_data)} 只股票数据")
            except Exception as e:
                st.error(f"数据获取失败: {e}")

    # 实时数据流控制
    st.markdown("### 🌊 实时数据流")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("▶️ 启动数据流", use_container_width=True):
            try:
                symbols = ['000001.SZ', '000002.SZ', '600000.SH']
                data_manager.start_data_stream(symbols)
                st.success("数据流已启动")
            except Exception as e:
                st.error(f"启动数据流失败: {e}")

    with col2:
        if st.button("⏹️ 停止数据流", use_container_width=True):
            try:
                data_manager.stop_data_stream()
                st.success("数据流已停止")
            except Exception as e:
                st.error(f"停止数据流失败: {e}")

    # 显示数据流状态
    if stats['streaming_active']:
        st.success("🟢 数据流运行中")
    else:
        st.info("🔴 数据流已停止")

def main():
    """主函数"""
    create_header()
    create_sidebar()
    
    # 主要内容区域 - 使用更多标签页
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📊 系统概览", 
        "📈 实时监控", 
        "🚨 机会警报", 
        "🛡️ 风险管理", 
        "⚡ 临时套利",
        "📊 数据源管理"
    ])
    
    with tab1:
        create_overview_dashboard()
    
    with tab2:
        create_realtime_monitoring()
    
    with tab3:
        create_opportunity_alerts()
    
    with tab4:
        create_risk_management()
    
    with tab5:
        create_emergency_arbitrage()
    
    with tab6:
        create_data_source_management()
    
    # 自动刷新
    if st.session_state.running:
        time.sleep(3)
        st.rerun()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>🚀 统一量化套利系统 v5.0 | 集成所有功能 | 一体化解决方案</p>
        <p>📈 实时监控 | 🛡️ 风险控制 | ⚡ 临时套利 | 📊 数据管理</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
