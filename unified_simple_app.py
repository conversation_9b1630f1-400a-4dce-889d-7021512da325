"""
简化版统一量化套利系统界面
避免复杂导入，使用模拟数据演示功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time

# 页面配置
st.set_page_config(
    page_title="统一量化套利系统",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-running {
        color: #28a745;
        font-weight: bold;
    }
    .status-stopped {
        color: #dc3545;
        font-weight: bold;
    }
    .emergency-alert {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'running' not in st.session_state:
    st.session_state.running = False
    st.session_state.alerts = []
    st.session_state.emergency_mode = False
    st.session_state.total_capital = 200000  # 总资金
    st.session_state.position_ratio = 0.7    # 仓位比例
    st.session_state.daily_return = 2350     # 今日收益
    st.session_state.return_rate = 0.0118    # 收益率
    st.session_state.trading_mode = "标准模式"  # 交易模式
    st.session_state.risk_level = "中等"      # 风险等级

def create_header():
    """创建页面头部"""
    st.markdown("""
    <div class="main-header">
        <h1>🚀 统一量化套利系统 v5.0</h1>
        <p>实时监控 | 风险控制 | 动态资金池 | 数据源管理 | 一体化解决方案</p>
    </div>
    """, unsafe_allow_html=True)

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.title("🎛️ 系统控制")
        
        # 系统状态
        if st.session_state.running:
            st.markdown('<p class="status-running">🟢 系统运行中</p>', unsafe_allow_html=True)
            if st.button("⏹️ 停止系统", use_container_width=True):
                st.session_state.running = False
                st.success("系统已停止")
                st.rerun()
        else:
            st.markdown('<p class="status-stopped">🔴 系统已停止</p>', unsafe_allow_html=True)
            if st.button("🚀 启动系统", use_container_width=True):
                st.session_state.running = True
                st.success("系统已启动")
                st.rerun()
        
        # 快速操作
        st.markdown("---")
        st.subheader("⚡ 快速操作")
        
        if st.button("🔍 强制扫描", use_container_width=True):
            if st.session_state.running:
                st.success("发现 3 个新机会")
                # 添加模拟警报
                new_alert = {
                    'strategy': '配对交易',
                    'symbols': ['000001.SZ', '000002.SZ'],
                    'confidence': 0.85,
                    'return': 0.025,
                    'time': datetime.now(),
                    'emergency': False
                }
                st.session_state.alerts.append(new_alert)
            else:
                st.warning("请先启动系统")
        
        if st.button("📊 生成报告", use_container_width=True):
            st.info("报告生成功能开发中...")
        
        # 系统模式
        st.markdown("---")
        st.subheader("🎯 系统模式")

        # 获取当前模式的索引
        mode_options = ["标准模式", "保守模式", "激进模式", "临时套利模式"]
        current_index = mode_options.index(st.session_state.trading_mode) if st.session_state.trading_mode in mode_options else 0

        new_mode = st.selectbox(
            "选择运行模式",
            mode_options,
            index=current_index,
            key="mode_selector"
        )

        # 检查模式是否变化
        if new_mode != st.session_state.trading_mode:
            old_mode = st.session_state.trading_mode
            st.session_state.trading_mode = new_mode

            # 根据模式设置相关参数
            if new_mode == "保守模式":
                st.session_state.risk_level = "低"
                st.session_state.emergency_mode = False
                # 保守模式降低仓位限制
                if st.session_state.position_ratio > 0.5:
                    st.session_state.position_ratio = 0.5
            elif new_mode == "激进模式":
                st.session_state.risk_level = "高"
                st.session_state.emergency_mode = False
                # 激进模式可以提高仓位
                if st.session_state.position_ratio < 0.8:
                    st.session_state.position_ratio = 0.8
            elif new_mode == "临时套利模式":
                st.session_state.risk_level = "极高"
                st.session_state.emergency_mode = True
                # 临时套利模式最高仓位
                st.session_state.position_ratio = 0.85
            else:  # 标准模式
                st.session_state.risk_level = "中等"
                st.session_state.emergency_mode = False
                st.session_state.position_ratio = 0.7

            st.success(f"🎯 运行模式已更新: {old_mode} → {new_mode}")
            time.sleep(1)
            st.rerun()

        # 显示当前模式信息
        st.markdown("---")
        st.markdown("### 📊 当前模式信息")

        # 根据模式显示不同的信息和警告
        if st.session_state.trading_mode == "保守模式":
            st.info("🛡️ 保守模式: 低风险，稳健收益")
            st.write("• 最大仓位: 50%")
            st.write("• 风险等级: 低")
            st.write("• 适合: 稳健投资者")
        elif st.session_state.trading_mode == "激进模式":
            st.warning("⚡ 激进模式: 高风险，高收益")
            st.write("• 最大仓位: 80%")
            st.write("• 风险等级: 高")
            st.write("• 适合: 风险偏好投资者")
        elif st.session_state.trading_mode == "临时套利模式":
            st.error("🚨 临时套利模式: 极高风险")
            st.write("• 最大仓位: 85%")
            st.write("• 风险等级: 极高")
            st.write("• 适合: 专业投资者")
            st.write("• ⚠️ 请谨慎操作")
        else:  # 标准模式
            st.success("✅ 标准模式: 平衡风险收益")
            st.write("• 最大仓位: 70%")
            st.write("• 风险等级: 中等")
            st.write("• 适合: 一般投资者")
        
        # 资金设置
        st.markdown("---")
        st.subheader("💰 资金设置")

        # 使用会话状态保存资金设置
        new_capital = st.number_input(
            "当前资金 (元)",
            min_value=10000,
            max_value=10000000,
            value=st.session_state.total_capital,
            step=10000,
            key="capital_input"
        )

        new_position_ratio = st.slider(
            "仓位比例",
            min_value=0.1,
            max_value=0.85,
            value=st.session_state.position_ratio,
            step=0.05,
            key="position_input"
        )

        # 检查是否有变化并更新会话状态
        capital_changed = False
        position_changed = False

        if new_capital != st.session_state.total_capital:
            old_capital = st.session_state.total_capital
            st.session_state.total_capital = new_capital
            # 重新计算相关数值
            st.session_state.daily_return = int(new_capital * st.session_state.return_rate)
            capital_changed = True
            st.success(f"💰 总资金已更新: ¥{old_capital:,} → ¥{new_capital:,}")

        if new_position_ratio != st.session_state.position_ratio:
            old_ratio = st.session_state.position_ratio
            st.session_state.position_ratio = new_position_ratio
            position_changed = True
            st.success(f"📊 仓位比例已更新: {old_ratio:.1%} → {new_position_ratio:.1%}")

        # 如果有变化，延迟刷新以显示提示信息
        if capital_changed or position_changed:
            time.sleep(1)
            st.rerun()

        # 显示计算结果
        used_capital = int(st.session_state.total_capital * st.session_state.position_ratio)
        available_capital = st.session_state.total_capital - used_capital

        st.markdown("---")
        st.markdown("### 📊 资金分配")
        st.write(f"💰 总资金: ¥{st.session_state.total_capital:,}")
        st.write(f"📈 已使用: ¥{used_capital:,} ({st.session_state.position_ratio:.1%})")
        st.write(f"💵 可用资金: ¥{available_capital:,}")
        st.write(f"🎯 今日收益: ¥{st.session_state.daily_return:,}")

def create_overview_dashboard():
    """创建概览仪表板"""
    st.subheader("📊 系统概览")

    # 计算实时资金数据
    total_capital = st.session_state.total_capital
    used_capital = int(total_capital * st.session_state.position_ratio)
    available_capital = total_capital - used_capital
    daily_return = st.session_state.daily_return
    return_rate = st.session_state.return_rate

    # 关键指标
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.metric(
            "总资金",
            f"¥{total_capital:,}",
            delta="0%"
        )

    with col2:
        available_pct = (available_capital / total_capital) * 100
        st.metric(
            "可用资金",
            f"¥{available_capital:,}",
            delta=f"-{100-available_pct:.0f}%"
        )

    with col3:
        st.metric(
            "今日收益",
            f"¥{daily_return:,}",
            delta=f"+{return_rate:.2%}"
        )

    with col4:
        st.metric("活跃机会", str(len(st.session_state.alerts)), delta="+3")

    with col5:
        # 显示当前交易模式和风险等级
        mode_text = st.session_state.trading_mode
        risk_level = st.session_state.risk_level

        # 根据模式设置不同的delta颜色提示
        if st.session_state.trading_mode == "保守模式":
            delta_text = "🛡️ 低风险"
        elif st.session_state.trading_mode == "激进模式":
            delta_text = "⚡ 高风险"
        elif st.session_state.trading_mode == "临时套利模式":
            delta_text = "🚨 极高风险"
        else:
            delta_text = "✅ 平衡"

        st.metric("系统模式", mode_text, delta=delta_text)
    
    # 图表
    col1, col2 = st.columns(2)
    
    with col1:
        # 资金分配饼图 - 使用实时数据
        risk_margin = int(total_capital * 0.1)  # 10%风险保证金
        actual_available = available_capital - risk_margin

        fig_capital = go.Figure(data=[
            go.Pie(
                labels=['已使用资金', '可用资金', '风险保证金'],
                values=[used_capital, actual_available, risk_margin],
                hole=0.4,
                marker_colors=['#ff6b6b', '#4ecdc4', '#45b7d1'],
                textinfo='label+percent+value',
                texttemplate='%{label}<br>¥%{value:,}<br>(%{percent})'
            )
        ])
        fig_capital.update_layout(
            title=f"资金分配情况 (总计: ¥{total_capital:,})",
            height=300,
            showlegend=True
        )
        st.plotly_chart(fig_capital, use_container_width=True)
    
    with col2:
        # 策略收益对比 - 根据模式调整
        strategies = ['配对交易', '统计套利', 'ML增强', '波动率套利', '临时套利']

        # 根据交易模式调整策略收益和权重
        if st.session_state.trading_mode == "保守模式":
            returns = [0.015, 0.012, 0.018, 0.014, 0.008]  # 保守收益
            colors = ['#4ecdc4', '#45b7d1', '#96ceb4', '#6c5ce7', '#a29bfe']
            title_suffix = " (保守模式)"
        elif st.session_state.trading_mode == "激进模式":
            returns = [0.035, 0.028, 0.045, 0.038, 0.055]  # 激进收益
            colors = ['#ff6b6b', '#fd79a8', '#fdcb6e', '#e17055', '#d63031']
            title_suffix = " (激进模式)"
        elif st.session_state.trading_mode == "临时套利模式":
            returns = [0.025, 0.020, 0.040, 0.035, 0.065]  # 临时套利重点
            colors = ['#feca57', '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3']
            title_suffix = " (临时套利模式)"
        else:  # 标准模式
            returns = [0.023, 0.018, 0.032, 0.028, 0.045]  # 标准收益
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
            title_suffix = " (标准模式)"

        fig_strategy = go.Figure(data=[
            go.Bar(
                x=strategies,
                y=returns,
                marker_color=colors,
                text=[f"{r:.1%}" for r in returns],
                textposition='auto'
            )
        ])
        fig_strategy.update_layout(
            title=f"策略收益对比{title_suffix}",
            yaxis_title="收益率",
            height=300,
            showlegend=False
        )
        st.plotly_chart(fig_strategy, use_container_width=True)

    # 资金使用详情
    st.subheader("💰 资金使用详情")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("### 📊 仓位分析")
        st.metric("当前仓位", f"{st.session_state.position_ratio:.1%}")
        st.metric("已使用资金", f"¥{used_capital:,}")

        # 仓位安全性评估
        if st.session_state.position_ratio <= 0.5:
            st.success("🟢 仓位安全")
        elif st.session_state.position_ratio <= 0.7:
            st.warning("🟡 仓位适中")
        else:
            st.error("🔴 仓位较高")

    with col2:
        st.markdown("### 💵 可用资金")
        st.metric("可用资金", f"¥{available_capital:,}")
        st.metric("风险保证金", f"¥{risk_margin:,}")

        # 资金充足性评估
        available_ratio = available_capital / total_capital
        if available_ratio >= 0.3:
            st.success("🟢 资金充足")
        elif available_ratio >= 0.2:
            st.warning("🟡 资金适中")
        else:
            st.error("🔴 资金紧张")

    with col3:
        st.markdown("### 📈 收益分析")
        st.metric("今日收益", f"¥{daily_return:,}")
        st.metric("收益率", f"{return_rate:.2%}")

        # 收益表现评估
        if return_rate >= 0.02:
            st.success("🟢 收益优秀")
        elif return_rate >= 0.01:
            st.info("🔵 收益良好")
        elif return_rate >= 0:
            st.warning("🟡 收益一般")
        else:
            st.error("🔴 出现亏损")

    # 交易模式详情
    st.subheader("🎯 交易模式详情")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("### 📊 当前模式")
        st.metric("运行模式", st.session_state.trading_mode)
        st.metric("风险等级", st.session_state.risk_level)

        # 模式状态指示
        if st.session_state.trading_mode == "保守模式":
            st.success("🛡️ 稳健运行")
        elif st.session_state.trading_mode == "激进模式":
            st.warning("⚡ 高收益追求")
        elif st.session_state.trading_mode == "临时套利模式":
            st.error("🚨 极限操作")
        else:
            st.info("✅ 平衡运行")

    with col2:
        st.markdown("### ⚙️ 模式参数")

        # 根据模式显示不同的参数限制
        if st.session_state.trading_mode == "保守模式":
            st.write("🔒 最大仓位: 50%")
            st.write("🛡️ 风险控制: 严格")
            st.write("📈 预期收益: 5-10%")
            st.write("⏱️ 持仓周期: 长期")
        elif st.session_state.trading_mode == "激进模式":
            st.write("🚀 最大仓位: 80%")
            st.write("⚡ 风险控制: 宽松")
            st.write("📈 预期收益: 15-25%")
            st.write("⏱️ 持仓周期: 短期")
        elif st.session_state.trading_mode == "临时套利模式":
            st.write("🔥 最大仓位: 85%")
            st.write("🚨 风险控制: 最小")
            st.write("📈 预期收益: 20-40%")
            st.write("⏱️ 持仓周期: 极短")
        else:  # 标准模式
            st.write("⚖️ 最大仓位: 70%")
            st.write("🎯 风险控制: 适中")
            st.write("📈 预期收益: 10-15%")
            st.write("⏱️ 持仓周期: 中期")

    with col3:
        st.markdown("### 📋 模式建议")

        # 根据当前资金和仓位给出建议
        current_position = st.session_state.position_ratio

        if st.session_state.trading_mode == "保守模式":
            if current_position > 0.5:
                st.warning("⚠️ 建议降低仓位至50%以下")
            else:
                st.success("✅ 仓位符合保守模式要求")
            st.info("💡 建议关注蓝筹股和债券")
        elif st.session_state.trading_mode == "激进模式":
            if current_position < 0.6:
                st.info("💡 可以适当提高仓位")
            elif current_position > 0.8:
                st.warning("⚠️ 仓位已达上限，注意风险")
            else:
                st.success("✅ 仓位适合激进策略")
            st.info("💡 建议关注成长股和热点板块")
        elif st.session_state.trading_mode == "临时套利模式":
            st.error("🚨 极高风险模式，请密切监控")
            st.warning("⚠️ 建议设置严格止损")
            st.info("💡 适合短期套利机会")
        else:  # 标准模式
            if current_position > 0.75:
                st.warning("⚠️ 仓位偏高，建议适当降低")
            elif current_position < 0.5:
                st.info("💡 仓位偏低，可以适当提高")
            else:
                st.success("✅ 仓位配置合理")
            st.info("💡 建议均衡配置各类资产")

def create_realtime_monitoring():
    """创建实时监控面板"""
    st.subheader("📈 实时市场监控")
    
    # 模拟股票数据
    symbols = ['000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '600000.SH', '600036.SH']
    
    market_data = []
    for symbol in symbols:
        price = 10 + np.random.normal(0, 2)
        change = np.random.normal(0, 0.02)
        volume = np.random.randint(1000000, 10000000)
        
        market_data.append({
            '股票代码': symbol,
            '当前价格': f"¥{price:.2f}",
            '涨跌幅': f"{change:.2%}",
            '成交量': f"{volume:,}",
            '成交额': f"¥{price * volume / 10000:.0f}万",
            '状态': '正常' if abs(change) < 0.03 else '异常'
        })
    
    df = pd.DataFrame(market_data)
    st.dataframe(df, use_container_width=True)
    
    # 价格走势图
    st.subheader("📊 价格走势")
    
    dates = pd.date_range(start=datetime.now() - timedelta(hours=6), end=datetime.now(), freq='5min')
    
    fig = go.Figure()
    
    for symbol in symbols[:3]:
        prices = 10 + np.cumsum(np.random.normal(0, 0.1, len(dates)))
        fig.add_trace(go.Scatter(
            x=dates,
            y=prices,
            mode='lines',
            name=symbol,
            line=dict(width=2)
        ))
    
    fig.update_layout(
        title="主要股票价格走势",
        xaxis_title="时间",
        yaxis_title="价格 (¥)",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_opportunity_alerts():
    """创建机会警报面板"""
    st.subheader("🚨 套利机会警报")
    
    # 初始化示例警报
    if not st.session_state.alerts:
        sample_alerts = [
            {
                'strategy': '配对交易',
                'symbols': ['000001.SZ', '000002.SZ'],
                'confidence': 0.85,
                'return': 0.025,
                'time': datetime.now() - timedelta(minutes=5),
                'emergency': False
            },
            {
                'strategy': '临时套利',
                'symbols': ['600519.SH'],
                'confidence': 0.92,
                'return': 0.045,
                'time': datetime.now() - timedelta(minutes=2),
                'emergency': True
            }
        ]
        st.session_state.alerts = sample_alerts
    
    # 警报统计
    col1, col2, col3, col4 = st.columns(4)
    
    total_alerts = len(st.session_state.alerts)
    emergency_alerts = sum(1 for alert in st.session_state.alerts if alert['emergency'])
    high_confidence = sum(1 for alert in st.session_state.alerts if alert['confidence'] > 0.8)
    avg_return = np.mean([alert['return'] for alert in st.session_state.alerts]) if st.session_state.alerts else 0
    
    with col1:
        st.metric("总警报数", total_alerts)
    
    with col2:
        st.metric("紧急警报", emergency_alerts, delta=f"+{emergency_alerts}")
    
    with col3:
        st.metric("高置信度", high_confidence)
    
    with col4:
        st.metric("平均预期收益", f"{avg_return:.2%}")
    
    # 警报列表
    st.subheader("📋 最新警报")
    
    for i, alert in enumerate(reversed(st.session_state.alerts[-10:])):
        if alert['emergency']:
            alert_type = "🚨 紧急套利"
            container_class = "emergency-alert"
        elif alert['confidence'] > 0.8:
            alert_type = "⭐ 高置信度"
            container_class = "alert-card"
        else:
            alert_type = "💡 一般机会"
            container_class = "alert-card"
        
        with st.container():
            col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
            
            with col1:
                st.markdown(f"**{alert_type}**: {alert['strategy']}")
                st.caption(f"交易对: {' / '.join(alert['symbols'])}")
            
            with col2:
                st.markdown(f"置信度: **{alert['confidence']:.1%}**")
                st.caption(f"预期收益: {alert['return']:.2%}")
            
            with col3:
                time_diff = (datetime.now() - alert['time']).seconds
                st.caption(f"{time_diff}秒前")
            
            with col4:
                if st.button(f"执行", key=f"execute_{i}"):
                    st.success("信号已执行")

def main():
    """主函数"""
    create_header()
    create_sidebar()
    
    # 主要内容区域
    tab1, tab2, tab3 = st.tabs([
        "📊 系统概览", 
        "📈 实时监控", 
        "🚨 机会警报"
    ])
    
    with tab1:
        create_overview_dashboard()
    
    with tab2:
        create_realtime_monitoring()
    
    with tab3:
        create_opportunity_alerts()
    
    # 自动刷新
    if st.session_state.running:
        time.sleep(3)
        st.rerun()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>🚀 统一量化套利系统 v5.0 | 简化演示版本</p>
        <p>📈 实时监控 | 🛡️ 风险控制 | ⚡ 临时套利</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
