"""
简化版统一量化套利系统界面
避免复杂导入，使用模拟数据演示功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from data.realtime_data_reader import RealTimeDataReader
    DATA_READER_AVAILABLE = True
except ImportError:
    DATA_READER_AVAILABLE = False
    st.warning("实时数据读取模块不可用，将使用模拟数据")

# 页面配置
st.set_page_config(
    page_title="统一量化套利系统",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-running {
        color: #28a745;
        font-weight: bold;
    }
    .status-stopped {
        color: #dc3545;
        font-weight: bold;
    }
    .emergency-alert {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'running' not in st.session_state:
    st.session_state.running = False
    st.session_state.alerts = []
    st.session_state.emergency_mode = False
    st.session_state.trading_mode = "标准模式"  # 交易模式
    st.session_state.risk_level = "中等"      # 风险等级
    st.session_state.trading_signals = []    # 交易信号历史
    st.session_state.signal_counter = 0      # 信号计数器
    st.session_state.auto_execute = True     # 自动执行开关
    st.session_state.auto_threshold = 0.8    # 自动执行阈值
    st.session_state.executed_auto_signals = []  # 自动执行的信号记录

    # 初始化数据读取器
    if DATA_READER_AVAILABLE:
        st.session_state.data_reader = RealTimeDataReader()
    else:
        st.session_state.data_reader = None

    # 实时数据缓存
    st.session_state.market_data = pd.DataFrame()
    st.session_state.position_data = pd.DataFrame()
    st.session_state.capital_data = {}
    st.session_state.position_value_data = {}
    st.session_state.last_data_update = datetime.now()

def generate_trading_signal():
    """生成模拟交易信号"""
    import random

    # 股票池
    symbols = ['000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '600000.SH', '600036.SH',
               '600519.SH', '000858.SZ', '002594.SZ', '300750.SZ']

    # 策略类型
    strategies = ['配对交易', '统计套利', 'ML增强', '波动率套利', '临时套利', '趋势跟踪', '均值回归']

    # 信号类型
    signal_types = ['BUY', 'SELL', 'HOLD']

    # 根据交易模式调整信号参数
    if st.session_state.trading_mode == "保守模式":
        confidence_range = (0.6, 0.8)
        return_range = (0.005, 0.02)
        risk_levels = ['LOW', 'MEDIUM']
    elif st.session_state.trading_mode == "激进模式":
        confidence_range = (0.7, 0.95)
        return_range = (0.02, 0.06)
        risk_levels = ['MEDIUM', 'HIGH']
    elif st.session_state.trading_mode == "临时套利模式":
        confidence_range = (0.8, 0.98)
        return_range = (0.03, 0.08)
        risk_levels = ['HIGH', 'EXTREME']
    else:  # 标准模式
        confidence_range = (0.65, 0.9)
        return_range = (0.01, 0.04)
        risk_levels = ['LOW', 'MEDIUM', 'HIGH']

    # 生成信号
    st.session_state.signal_counter += 1
    signal = {
        'id': f"SIG_{st.session_state.signal_counter:04d}",
        'timestamp': datetime.now(),
        'strategy': random.choice(strategies),
        'symbol': random.choice(symbols),
        'signal_type': random.choice(signal_types),
        'confidence': round(random.uniform(*confidence_range), 3),
        'expected_return': round(random.uniform(*return_range), 4),
        'risk_level': random.choice(risk_levels),
        'entry_price': round(random.uniform(8, 50), 2),
        'target_price': None,
        'stop_loss': None,
        'position_size': round(random.uniform(0.02, 0.1), 3),
        'status': 'PENDING',
        'trading_mode': st.session_state.trading_mode,
        'notes': ''
    }

    # 计算目标价和止损价
    if signal['signal_type'] == 'BUY':
        signal['target_price'] = round(signal['entry_price'] * (1 + signal['expected_return']), 2)
        signal['stop_loss'] = round(signal['entry_price'] * 0.95, 2)
    elif signal['signal_type'] == 'SELL':
        signal['target_price'] = round(signal['entry_price'] * (1 - signal['expected_return']), 2)
        signal['stop_loss'] = round(signal['entry_price'] * 1.05, 2)

    # 检查是否需要自动执行
    if (st.session_state.auto_execute and
        signal['confidence'] >= st.session_state.auto_threshold and
        signal['risk_level'] != 'EXTREME'):  # 极高风险信号不自动执行

        signal['status'] = 'AUTO_EXECUTED'
        signal['execution_time'] = datetime.now()
        signal['execution_type'] = 'AUTOMATIC'
        signal['notes'] = f"自动执行 - 置信度{signal['confidence']:.1%}超过阈值{st.session_state.auto_threshold:.0%}"

        # 记录自动执行
        st.session_state.executed_auto_signals.append({
            'signal_id': signal['id'],
            'execution_time': signal['execution_time'],
            'confidence': signal['confidence'],
            'strategy': signal['strategy'],
            'symbol': signal['symbol'],
            'signal_type': signal['signal_type']
        })

    return signal

def update_realtime_data(force_refresh: bool = False):
    """更新实时数据"""
    if not st.session_state.data_reader:
        return

    try:
        # 检查是否需要更新
        now = datetime.now()
        time_diff = (now - st.session_state.last_data_update).seconds

        if not force_refresh and time_diff < 2:  # 2秒内不重复更新
            return

        # 更新行情数据
        market_data = st.session_state.data_reader.get_market_data(force_refresh)
        st.session_state.market_data = market_data

        # 更新持仓数据
        position_data = st.session_state.data_reader.get_position_data(force_refresh)
        st.session_state.position_data = position_data

        # 更新资金数据
        capital_data = st.session_state.data_reader.get_capital_data(force_refresh)
        st.session_state.capital_data = capital_data

        # 计算持仓市值
        position_value_data = st.session_state.data_reader.calculate_position_value(force_refresh)
        st.session_state.position_value_data = position_value_data

        # 更新时间戳
        st.session_state.last_data_update = now

    except Exception as e:
        st.error(f"数据更新失败: {e}")

def get_current_capital_info():
    """获取当前资金信息"""
    if st.session_state.capital_data:
        return {
            'total_capital': st.session_state.capital_data.get('total_capital', 500000),
            'available_capital': st.session_state.capital_data.get('available_capital', 300000),
            'frozen_capital': st.session_state.capital_data.get('frozen_capital', 0),
            'position_value': st.session_state.position_value_data.get('total_value', 200000)
        }
    else:
        # 使用模拟数据
        return {
            'total_capital': 500000,
            'available_capital': 300000,
            'frozen_capital': 0,
            'position_value': 200000
        }

def get_current_position_ratio():
    """获取当前仓位比例"""
    capital_info = get_current_capital_info()
    total_capital = capital_info['total_capital']
    position_value = capital_info['position_value']

    if total_capital > 0:
        return position_value / total_capital
    else:
        return 0.0

def create_header():
    """创建页面头部"""
    st.markdown("""
    <div class="main-header">
        <h1>🚀 统一量化套利系统 v5.0</h1>
        <p>实时监控 | 风险控制 | 动态资金池 | 数据源管理 | 一体化解决方案</p>
    </div>
    """, unsafe_allow_html=True)

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.title("🎛️ 系统控制")
        
        # 系统状态
        if st.session_state.running:
            st.markdown('<p class="status-running">🟢 系统运行中</p>', unsafe_allow_html=True)
            if st.button("⏹️ 停止系统", use_container_width=True):
                st.session_state.running = False
                st.success("系统已停止")
                st.rerun()
        else:
            st.markdown('<p class="status-stopped">🔴 系统已停止</p>', unsafe_allow_html=True)
            if st.button("🚀 启动系统", use_container_width=True):
                st.session_state.running = True
                st.success("系统已启动")
                st.rerun()
        
        # 快速操作
        st.markdown("---")
        st.subheader("⚡ 快速操作")
        
        if st.button("🔍 强制扫描", use_container_width=True):
            if st.session_state.running:
                # 生成多个交易信号
                num_signals = np.random.randint(2, 6)  # 生成2-5个信号
                new_signals = []
                auto_executed_count = 0

                for _ in range(num_signals):
                    signal = generate_trading_signal()
                    new_signals.append(signal)
                    st.session_state.trading_signals.append(signal)

                    # 统计自动执行的信号
                    if signal['status'] == 'AUTO_EXECUTED':
                        auto_executed_count += 1

                st.success(f"🎯 发现 {num_signals} 个新交易信号")

                # 显示自动执行统计
                if auto_executed_count > 0:
                    st.info(f"🤖 其中 {auto_executed_count} 个信号已自动执行 (置信度 ≥ {st.session_state.auto_threshold:.0%})")

                # 同时添加机会警报
                new_alert = {
                    'strategy': new_signals[0]['strategy'],
                    'symbols': [new_signals[0]['symbol']],
                    'confidence': new_signals[0]['confidence'],
                    'return': new_signals[0]['expected_return'],
                    'time': datetime.now(),
                    'emergency': new_signals[0]['risk_level'] in ['HIGH', 'EXTREME']
                }
                st.session_state.alerts.append(new_alert)

                # 显示最新信号预览
                with st.expander("📋 最新生成的信号", expanded=True):
                    for signal in new_signals[-3:]:  # 显示最新3个
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.write(f"**{signal['id']}**: {signal['strategy']}")
                        with col2:
                            st.write(f"{signal['signal_type']} {signal['symbol']}")
                        with col3:
                            st.write(f"置信度: {signal['confidence']:.1%}")
                        with col4:
                            if signal['status'] == 'AUTO_EXECUTED':
                                st.success("🤖 已自动执行")
                            else:
                                st.info("⏳ 待处理")
            else:
                st.warning("请先启动系统")
        
        if st.button("📊 生成报告", use_container_width=True):
            st.info("报告生成功能开发中...")

        if st.button("🎯 生成交易信号", use_container_width=True):
            if st.session_state.running:
                signal = generate_trading_signal()
                st.session_state.trading_signals.append(signal)
                st.success(f"✅ 已生成信号: {signal['id']}")
                st.info(f"📊 {signal['signal_type']} {signal['symbol']} (置信度: {signal['confidence']:.1%})")
            else:
                st.warning("请先启动系统")
        
        # 系统模式
        st.markdown("---")
        st.subheader("🎯 系统模式")

        # 获取当前模式的索引
        mode_options = ["标准模式", "保守模式", "激进模式", "临时套利模式"]
        current_index = mode_options.index(st.session_state.trading_mode) if st.session_state.trading_mode in mode_options else 0

        new_mode = st.selectbox(
            "选择运行模式",
            mode_options,
            index=current_index,
            key="mode_selector"
        )

        # 检查模式是否变化
        if new_mode != st.session_state.trading_mode:
            old_mode = st.session_state.trading_mode
            st.session_state.trading_mode = new_mode

            # 根据模式设置相关参数
            if new_mode == "保守模式":
                st.session_state.risk_level = "低"
                st.session_state.emergency_mode = False
                # 保守模式降低仓位限制
                if st.session_state.position_ratio > 0.5:
                    st.session_state.position_ratio = 0.5
            elif new_mode == "激进模式":
                st.session_state.risk_level = "高"
                st.session_state.emergency_mode = False
                # 激进模式可以提高仓位
                if st.session_state.position_ratio < 0.8:
                    st.session_state.position_ratio = 0.8
            elif new_mode == "临时套利模式":
                st.session_state.risk_level = "极高"
                st.session_state.emergency_mode = True
                # 临时套利模式最高仓位
                st.session_state.position_ratio = 0.85
            else:  # 标准模式
                st.session_state.risk_level = "中等"
                st.session_state.emergency_mode = False
                st.session_state.position_ratio = 0.7

            st.success(f"🎯 运行模式已更新: {old_mode} → {new_mode}")
            time.sleep(1)
            st.rerun()

        # 显示当前模式信息
        st.markdown("---")
        st.markdown("### 📊 当前模式信息")

        # 根据模式显示不同的信息和警告
        if st.session_state.trading_mode == "保守模式":
            st.info("🛡️ 保守模式: 低风险，稳健收益")
            st.write("• 最大仓位: 50%")
            st.write("• 风险等级: 低")
            st.write("• 适合: 稳健投资者")
        elif st.session_state.trading_mode == "激进模式":
            st.warning("⚡ 激进模式: 高风险，高收益")
            st.write("• 最大仓位: 80%")
            st.write("• 风险等级: 高")
            st.write("• 适合: 风险偏好投资者")
        elif st.session_state.trading_mode == "临时套利模式":
            st.error("🚨 临时套利模式: 极高风险")
            st.write("• 最大仓位: 85%")
            st.write("• 风险等级: 极高")
            st.write("• 适合: 专业投资者")
            st.write("• ⚠️ 请谨慎操作")
        else:  # 标准模式
            st.success("✅ 标准模式: 平衡风险收益")
            st.write("• 最大仓位: 70%")
            st.write("• 风险等级: 中等")
            st.write("• 适合: 一般投资者")
        
        # 数据刷新控制
        st.markdown("---")
        st.subheader("📊 数据管理")

        # 数据刷新按钮
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔄 刷新数据", use_container_width=True):
                update_realtime_data(force_refresh=True)
                st.success("数据已刷新")
                st.rerun()

        with col2:
            if st.button("📁 数据状态", use_container_width=True):
                if st.session_state.data_reader:
                    status = st.session_state.data_reader.get_data_status()
                    st.json(status)
                else:
                    st.info("数据读取器不可用")

        # 显示实时资金信息
        capital_info = get_current_capital_info()
        current_position_ratio = get_current_position_ratio()

        st.markdown("---")
        st.markdown("### 💰 实时资金状况")
        st.write(f"💰 总资金: ¥{capital_info['total_capital']:,.0f}")
        st.write(f"💵 可用资金: ¥{capital_info['available_capital']:,.0f}")
        st.write(f"📈 持仓市值: ¥{capital_info['position_value']:,.0f}")
        st.write(f"📊 仓位比例: {current_position_ratio:.1%}")

        # 数据更新时间
        if st.session_state.capital_data.get('update_time'):
            update_time = st.session_state.capital_data['update_time']
            st.caption(f"更新时间: {update_time.strftime('%H:%M:%S')}")

        # 持仓概览
        if not st.session_state.position_data.empty:
            st.markdown("### 📈 持仓概览")
            position_count = len(st.session_state.position_data)
            total_pnl = st.session_state.position_value_data.get('total_value', 0) - sum(
                st.session_state.position_data['quantity'] * st.session_state.position_data['cost_price']
            ) if not st.session_state.position_data.empty else 0

            st.write(f"📊 持仓品种: {position_count}")
            st.write(f"💹 总盈亏: ¥{total_pnl:,.0f}")
        else:
            st.info("暂无持仓数据")

        # 自动执行设置
        st.markdown("---")
        st.markdown("### 🤖 自动执行设置")

        # 自动执行开关
        auto_execute = st.checkbox(
            "启用自动执行",
            value=st.session_state.auto_execute,
            help="置信度超过阈值的信号将自动执行"
        )

        if auto_execute != st.session_state.auto_execute:
            st.session_state.auto_execute = auto_execute
            if auto_execute:
                st.success("✅ 自动执行已启用")
            else:
                st.info("ℹ️ 自动执行已禁用")
            st.rerun()

        # 自动执行阈值设置
        if st.session_state.auto_execute:
            auto_threshold = st.slider(
                "自动执行阈值",
                min_value=0.7,
                max_value=0.95,
                value=st.session_state.auto_threshold,
                step=0.05,
                help="置信度超过此阈值的信号将自动执行",
                format="%.0%%"
            )

            if auto_threshold != st.session_state.auto_threshold:
                old_threshold = st.session_state.auto_threshold
                st.session_state.auto_threshold = auto_threshold
                st.success(f"🎯 自动执行阈值已更新: {old_threshold:.0%} → {auto_threshold:.0%}")
                st.rerun()

            # 显示自动执行统计
            auto_count = len(st.session_state.executed_auto_signals)
            if auto_count > 0:
                st.write(f"🤖 已自动执行: {auto_count} 个信号")

                # 显示最近的自动执行信号
                if st.expander("📋 最近自动执行", expanded=False):
                    for auto_signal in st.session_state.executed_auto_signals[-3:]:
                        st.write(f"• {auto_signal['signal_id']}: {auto_signal['signal_type']} {auto_signal['symbol']} ({auto_signal['confidence']:.1%})")
            else:
                st.write("🤖 暂无自动执行记录")
        else:
            st.info("💡 启用自动执行可以自动处理高置信度信号")

def create_overview_dashboard():
    """创建概览仪表板"""
    st.subheader("📊 系统概览")

    # 更新实时数据
    update_realtime_data()

    # 获取实时资金数据
    capital_info = get_current_capital_info()
    total_capital = capital_info['total_capital']
    available_capital = capital_info['available_capital']
    position_value = capital_info['position_value']

    # 计算收益数据
    if not st.session_state.position_data.empty and 'cost_price' in st.session_state.position_data.columns:
        cost_value = sum(st.session_state.position_data['quantity'] * st.session_state.position_data['cost_price'])
        daily_return = position_value - cost_value
        return_rate = daily_return / cost_value if cost_value > 0 else 0
    else:
        daily_return = 2350  # 模拟数据
        return_rate = 0.0118

    # 关键指标
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.metric(
            "总资金",
            f"¥{total_capital:,}",
            delta="实时数据"
        )

    with col2:
        st.metric(
            "可用资金",
            f"¥{available_capital:,}",
            delta=f"{(available_capital/total_capital)*100:.1f}%"
        )

    with col3:
        st.metric(
            "持仓市值",
            f"¥{position_value:,}",
            delta=f"{(position_value/total_capital)*100:.1f}%"
        )

    with col4:
        st.metric(
            "今日盈亏",
            f"¥{daily_return:,}",
            delta=f"{return_rate:+.2%}"
        )

    with col5:
        # 显示当前交易模式和风险等级
        mode_text = st.session_state.trading_mode

        # 根据模式设置不同的delta颜色提示
        if st.session_state.trading_mode == "保守模式":
            delta_text = "🛡️ 低风险"
        elif st.session_state.trading_mode == "激进模式":
            delta_text = "⚡ 高风险"
        elif st.session_state.trading_mode == "临时套利模式":
            delta_text = "🚨 极高风险"
        else:
            delta_text = "✅ 平衡"

        st.metric("系统模式", mode_text, delta=delta_text)
    
    # 图表
    col1, col2 = st.columns(2)
    
    with col1:
        # 资金分配饼图 - 使用实时数据
        frozen_capital = capital_info.get('frozen_capital', 0)

        fig_capital = go.Figure(data=[
            go.Pie(
                labels=['持仓市值', '可用资金', '冻结资金'],
                values=[position_value, available_capital, frozen_capital],
                hole=0.4,
                marker_colors=['#ff6b6b', '#4ecdc4', '#45b7d1'],
                textinfo='label+percent+value',
                texttemplate='%{label}<br>¥%{value:,}<br>(%{percent})'
            )
        ])
        fig_capital.update_layout(
            title=f"资金分配情况 (总计: ¥{total_capital:,})",
            height=300,
            showlegend=True
        )
        st.plotly_chart(fig_capital, use_container_width=True)
    
    with col2:
        # 策略收益对比 - 根据模式调整
        strategies = ['配对交易', '统计套利', 'ML增强', '波动率套利', '临时套利']

        # 根据交易模式调整策略收益和权重
        if st.session_state.trading_mode == "保守模式":
            returns = [0.015, 0.012, 0.018, 0.014, 0.008]  # 保守收益
            colors = ['#4ecdc4', '#45b7d1', '#96ceb4', '#6c5ce7', '#a29bfe']
            title_suffix = " (保守模式)"
        elif st.session_state.trading_mode == "激进模式":
            returns = [0.035, 0.028, 0.045, 0.038, 0.055]  # 激进收益
            colors = ['#ff6b6b', '#fd79a8', '#fdcb6e', '#e17055', '#d63031']
            title_suffix = " (激进模式)"
        elif st.session_state.trading_mode == "临时套利模式":
            returns = [0.025, 0.020, 0.040, 0.035, 0.065]  # 临时套利重点
            colors = ['#feca57', '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3']
            title_suffix = " (临时套利模式)"
        else:  # 标准模式
            returns = [0.023, 0.018, 0.032, 0.028, 0.045]  # 标准收益
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
            title_suffix = " (标准模式)"

        fig_strategy = go.Figure(data=[
            go.Bar(
                x=strategies,
                y=returns,
                marker_color=colors,
                text=[f"{r:.1%}" for r in returns],
                textposition='auto'
            )
        ])
        fig_strategy.update_layout(
            title=f"策略收益对比{title_suffix}",
            yaxis_title="收益率",
            height=300,
            showlegend=False
        )
        st.plotly_chart(fig_strategy, use_container_width=True)

    # 资金使用详情
    st.subheader("💰 资金使用详情")

    col1, col2, col3 = st.columns(3)

    # 计算当前仓位比例
    current_position_ratio = get_current_position_ratio()

    with col1:
        st.markdown("### 📊 仓位分析")
        st.metric("当前仓位", f"{current_position_ratio:.1%}")
        st.metric("持仓市值", f"¥{position_value:,}")

        # 仓位安全性评估
        if current_position_ratio <= 0.5:
            st.success("🟢 仓位安全")
        elif current_position_ratio <= 0.7:
            st.warning("🟡 仓位适中")
        else:
            st.error("🔴 仓位较高")

    with col2:
        st.markdown("### 💵 可用资金")
        st.metric("可用资金", f"¥{available_capital:,}")
        frozen_capital = capital_info.get('frozen_capital', 0)
        st.metric("冻结资金", f"¥{frozen_capital:,}")

        # 资金充足性评估
        available_ratio = available_capital / total_capital
        if available_ratio >= 0.3:
            st.success("🟢 资金充足")
        elif available_ratio >= 0.2:
            st.warning("🟡 资金适中")
        else:
            st.error("🔴 资金紧张")

    with col3:
        st.markdown("### 📈 收益分析")
        st.metric("今日盈亏", f"¥{daily_return:,}")
        st.metric("收益率", f"{return_rate:.2%}")

        # 收益表现评估
        if return_rate >= 0.02:
            st.success("🟢 收益优秀")
        elif return_rate >= 0.01:
            st.info("🔵 收益良好")
        elif return_rate >= 0:
            st.warning("🟡 收益一般")
        else:
            st.error("🔴 出现亏损")

    # 交易模式详情
    st.subheader("🎯 交易模式详情")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("### 📊 当前模式")
        st.metric("运行模式", st.session_state.trading_mode)
        st.metric("风险等级", st.session_state.risk_level)

        # 模式状态指示
        if st.session_state.trading_mode == "保守模式":
            st.success("🛡️ 稳健运行")
        elif st.session_state.trading_mode == "激进模式":
            st.warning("⚡ 高收益追求")
        elif st.session_state.trading_mode == "临时套利模式":
            st.error("🚨 极限操作")
        else:
            st.info("✅ 平衡运行")

    with col2:
        st.markdown("### ⚙️ 模式参数")

        # 根据模式显示不同的参数限制
        if st.session_state.trading_mode == "保守模式":
            st.write("🔒 最大仓位: 50%")
            st.write("🛡️ 风险控制: 严格")
            st.write("📈 预期收益: 5-10%")
            st.write("⏱️ 持仓周期: 长期")
        elif st.session_state.trading_mode == "激进模式":
            st.write("🚀 最大仓位: 80%")
            st.write("⚡ 风险控制: 宽松")
            st.write("📈 预期收益: 15-25%")
            st.write("⏱️ 持仓周期: 短期")
        elif st.session_state.trading_mode == "临时套利模式":
            st.write("🔥 最大仓位: 85%")
            st.write("🚨 风险控制: 最小")
            st.write("📈 预期收益: 20-40%")
            st.write("⏱️ 持仓周期: 极短")
        else:  # 标准模式
            st.write("⚖️ 最大仓位: 70%")
            st.write("🎯 风险控制: 适中")
            st.write("📈 预期收益: 10-15%")
            st.write("⏱️ 持仓周期: 中期")

    with col3:
        st.markdown("### 📋 模式建议")

        # 根据当前资金和仓位给出建议
        current_position = get_current_position_ratio()

        if st.session_state.trading_mode == "保守模式":
            if current_position > 0.5:
                st.warning("⚠️ 建议降低仓位至50%以下")
            else:
                st.success("✅ 仓位符合保守模式要求")
            st.info("💡 建议关注蓝筹股和债券")
        elif st.session_state.trading_mode == "激进模式":
            if current_position < 0.6:
                st.info("💡 可以适当提高仓位")
            elif current_position > 0.8:
                st.warning("⚠️ 仓位已达上限，注意风险")
            else:
                st.success("✅ 仓位适合激进策略")
            st.info("💡 建议关注成长股和热点板块")
        elif st.session_state.trading_mode == "临时套利模式":
            st.error("🚨 极高风险模式，请密切监控")
            st.warning("⚠️ 建议设置严格止损")
            st.info("💡 适合短期套利机会")
        else:  # 标准模式
            if current_position > 0.75:
                st.warning("⚠️ 仓位偏高，建议适当降低")
            elif current_position < 0.5:
                st.info("💡 仓位偏低，可以适当提高")
            else:
                st.success("✅ 仓位配置合理")
            st.info("💡 建议均衡配置各类资产")

    # 自动执行统计
    if st.session_state.auto_execute:
        st.subheader("🤖 自动执行统计")

        col1, col2, col3 = st.columns(3)

        auto_count = len(st.session_state.executed_auto_signals)
        total_signals = len(st.session_state.trading_signals)
        auto_rate = (auto_count / max(total_signals, 1)) * 100

        with col1:
            st.metric("自动执行信号", auto_count)

        with col2:
            st.metric("自动执行率", f"{auto_rate:.1f}%")

        with col3:
            st.metric("执行阈值", f"{st.session_state.auto_threshold:.0%}")

        # 最近自动执行的信号
        if auto_count > 0:
            st.markdown("### 📋 最近自动执行")
            for auto_signal in st.session_state.executed_auto_signals[-5:]:
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.write(f"**{auto_signal['signal_id']}**")
                with col2:
                    st.write(f"{auto_signal['signal_type']} {auto_signal['symbol']}")
                with col3:
                    st.write(f"置信度: {auto_signal['confidence']:.1%}")
                with col4:
                    time_diff = (datetime.now() - auto_signal['execution_time']).seconds
                    st.write(f"{time_diff}秒前")
        else:
            st.info("🤖 暂无自动执行记录")

def create_realtime_monitoring():
    """创建实时监控面板"""
    st.subheader("📈 实时市场监控")

    # 更新实时数据
    update_realtime_data()

    # 使用实时行情数据
    if not st.session_state.market_data.empty:
        market_df = st.session_state.market_data.copy()

        # 格式化显示数据
        display_data = []
        for _, row in market_df.iterrows():
            change_pct = row.get('change_pct', 0)
            price = row.get('current_price', 0)
            volume = row.get('volume', 0)
            turnover = row.get('turnover', price * volume)

            display_data.append({
                '股票代码': row['symbol'],
                '当前价格': f"¥{price:.2f}",
                '涨跌幅': f"{change_pct:+.2%}",
                '成交量': f"{volume:,}",
                '成交额': f"¥{turnover/10000:.0f}万",
                '状态': '异常' if abs(change_pct) > 0.03 else '正常'
            })

        df = pd.DataFrame(display_data)

        # 添加颜色标识
        def highlight_status(val):
            if val == '异常':
                return 'background-color: #ffebee'
            return ''

        styled_df = df.style.applymap(highlight_status, subset=['状态'])
        st.dataframe(styled_df, use_container_width=True)

    else:
        st.warning("暂无实时行情数据")
    
    # 价格走势图
    st.subheader("📊 价格走势")

    dates = pd.date_range(start=datetime.now() - timedelta(hours=6), end=datetime.now(), freq='5min')

    fig = go.Figure()

    # 使用实时数据中的股票代码
    if not st.session_state.market_data.empty:
        symbols = st.session_state.market_data['symbol'].tolist()[:3]
    else:
        symbols = ['000001.SZ', '000002.SZ', '600000.SH']

    for symbol in symbols:
        prices = 10 + np.cumsum(np.random.normal(0, 0.1, len(dates)))
        fig.add_trace(go.Scatter(
            x=dates,
            y=prices,
            mode='lines',
            name=symbol,
            line=dict(width=2)
        ))

    fig.update_layout(
        title="主要股票价格走势",
        xaxis_title="时间",
        yaxis_title="价格 (¥)",
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

def create_opportunity_alerts():
    """创建机会警报面板"""
    st.subheader("🚨 套利机会警报")
    
    # 初始化示例警报
    if not st.session_state.alerts:
        sample_alerts = [
            {
                'strategy': '配对交易',
                'symbols': ['000001.SZ', '000002.SZ'],
                'confidence': 0.85,
                'return': 0.025,
                'time': datetime.now() - timedelta(minutes=5),
                'emergency': False
            },
            {
                'strategy': '临时套利',
                'symbols': ['600519.SH'],
                'confidence': 0.92,
                'return': 0.045,
                'time': datetime.now() - timedelta(minutes=2),
                'emergency': True
            }
        ]
        st.session_state.alerts = sample_alerts
    
    # 警报统计
    col1, col2, col3, col4 = st.columns(4)
    
    total_alerts = len(st.session_state.alerts)
    emergency_alerts = sum(1 for alert in st.session_state.alerts if alert['emergency'])
    high_confidence = sum(1 for alert in st.session_state.alerts if alert['confidence'] > 0.8)
    avg_return = np.mean([alert['return'] for alert in st.session_state.alerts]) if st.session_state.alerts else 0
    
    with col1:
        st.metric("总警报数", total_alerts)
    
    with col2:
        st.metric("紧急警报", emergency_alerts, delta=f"+{emergency_alerts}")
    
    with col3:
        st.metric("高置信度", high_confidence)
    
    with col4:
        st.metric("平均预期收益", f"{avg_return:.2%}")
    
    # 警报列表
    st.subheader("📋 最新警报")
    
    for i, alert in enumerate(reversed(st.session_state.alerts[-10:])):
        if alert['emergency']:
            alert_type = "🚨 紧急套利"
            container_class = "emergency-alert"
        elif alert['confidence'] > 0.8:
            alert_type = "⭐ 高置信度"
            container_class = "alert-card"
        else:
            alert_type = "💡 一般机会"
            container_class = "alert-card"
        
        with st.container():
            col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
            
            with col1:
                st.markdown(f"**{alert_type}**: {alert['strategy']}")
                st.caption(f"交易对: {' / '.join(alert['symbols'])}")
            
            with col2:
                st.markdown(f"置信度: **{alert['confidence']:.1%}**")
                st.caption(f"预期收益: {alert['return']:.2%}")
            
            with col3:
                time_diff = (datetime.now() - alert['time']).seconds
                st.caption(f"{time_diff}秒前")
            
            with col4:
                if st.button(f"执行", key=f"execute_{i}"):
                    st.success("信号已执行")

def create_position_monitoring():
    """创建持仓监控面板"""
    st.subheader("📊 实时持仓监控")

    # 更新实时数据
    update_realtime_data()

    # 显示持仓统计
    if not st.session_state.position_data.empty and st.session_state.position_value_data:
        position_data = st.session_state.position_value_data

        # 持仓统计
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("持仓品种", len(position_data['positions']))

        with col2:
            st.metric("总市值", f"¥{position_data['total_value']:,.0f}")

        with col3:
            total_cost = sum([pos['cost_price'] * pos['quantity'] for pos in position_data['positions']])
            total_pnl = position_data['total_value'] - total_cost
            st.metric("总盈亏", f"¥{total_pnl:,.0f}", delta=f"{total_pnl/total_cost:.2%}" if total_cost > 0 else "0%")

        with col4:
            capital_info = get_current_capital_info()
            position_ratio = position_data['total_value'] / capital_info['total_capital']
            st.metric("仓位比例", f"{position_ratio:.1%}")

        # 持仓详情表
        st.subheader("📋 持仓明细")

        position_list = []
        for pos in position_data['positions']:
            pnl_pct = (pos['current_price'] - pos['cost_price']) / pos['cost_price'] if pos['cost_price'] > 0 else 0

            position_list.append({
                '股票代码': pos['symbol'],
                '持仓数量': f"{pos['quantity']:,}",
                '成本价': f"¥{pos['cost_price']:.2f}",
                '现价': f"¥{pos['current_price']:.2f}",
                '市值': f"¥{pos['market_value']:,.0f}",
                '盈亏': f"¥{pos['pnl']:,.0f}",
                '盈亏比例': f"{pnl_pct:+.2%}"
            })

        df = pd.DataFrame(position_list)

        # 添加颜色标识
        def highlight_pnl(val):
            if '盈亏' in val:
                if '+' in val:
                    return 'color: green'
                elif '-' in val:
                    return 'color: red'
            return ''

        styled_df = df.style.applymap(highlight_pnl, subset=['盈亏', '盈亏比例'])
        st.dataframe(styled_df, use_container_width=True)

        # 持仓分布图
        st.subheader("📊 持仓分布")

        col1, col2 = st.columns(2)

        with col1:
            # 持仓市值分布饼图
            symbols = [pos['symbol'] for pos in position_data['positions']]
            values = [pos['market_value'] for pos in position_data['positions']]

            fig_position = go.Figure(data=[
                go.Pie(
                    labels=symbols,
                    values=values,
                    hole=0.4,
                    textinfo='label+percent',
                    texttemplate='%{label}<br>%{percent}'
                )
            ])
            fig_position.update_layout(title="持仓市值分布", height=300)
            st.plotly_chart(fig_position, use_container_width=True)

        with col2:
            # 盈亏分布柱状图
            symbols = [pos['symbol'] for pos in position_data['positions']]
            pnls = [pos['pnl'] for pos in position_data['positions']]
            colors = ['green' if pnl >= 0 else 'red' for pnl in pnls]

            fig_pnl = go.Figure(data=[
                go.Bar(
                    x=symbols,
                    y=pnls,
                    marker_color=colors,
                    text=[f"¥{pnl:,.0f}" for pnl in pnls],
                    textposition='auto'
                )
            ])
            fig_pnl.update_layout(title="持仓盈亏分布", height=300, yaxis_title="盈亏 (¥)")
            st.plotly_chart(fig_pnl, use_container_width=True)

        # 数据更新时间
        if position_data.get('update_time'):
            st.caption(f"数据更新时间: {position_data['update_time'].strftime('%Y-%m-%d %H:%M:%S')}")

    else:
        st.info("暂无持仓数据")

        # 显示数据文件状态
        if st.session_state.data_reader:
            st.subheader("📁 数据文件状态")
            status = st.session_state.data_reader.get_data_status()

            col1, col2 = st.columns(2)
            with col1:
                st.write("**行情数据**")
                st.write(f"文件: {status['market_data']['last_file'] or '未找到'}")
                st.write(f"缓存: {'是' if status['market_data']['cached'] else '否'}")

            with col2:
                st.write("**持仓数据**")
                st.write(f"文件: {status['position_data']['last_file'] or '未找到'}")
                st.write(f"缓存: {'是' if status['position_data']['cached'] else '否'}")

        # 提供示例数据文件格式
        with st.expander("📝 数据文件格式说明", expanded=False):
            st.markdown("""
            **行情数据文件格式 (market_*.csv):**
            ```
            symbol,current_price,change_pct,volume,turnover
            000001.SZ,12.45,0.0234,8500000,105825000
            000002.SZ,28.76,-0.0156,3200000,92032000
            ```

            **持仓数据文件格式 (positions_*.csv):**
            ```
            symbol,quantity,cost_price
            000001.SZ,5000,11.80
            000002.SZ,2000,29.50
            ```

            **资金数据文件格式 (capital_*.csv):**
            ```
            total_capital,available_capital,frozen_capital
            850000,365411,0
            ```
            """)

def create_trading_signals():
    """创建交易信号管理面板"""
    st.subheader("📊 交易信号管理")

    # 初始化示例信号
    if not st.session_state.trading_signals:
        # 生成一些示例信号
        for i in range(5):
            signal = generate_trading_signal()
            signal['timestamp'] = datetime.now() - timedelta(minutes=np.random.randint(5, 120))
            if i < 2:
                signal['status'] = 'EXECUTED'
            elif i == 2:
                signal['status'] = 'CANCELLED'
            st.session_state.trading_signals.append(signal)

    # 信号统计
    st.subheader("📈 信号统计概览")

    total_signals = len(st.session_state.trading_signals)
    pending_signals = len([s for s in st.session_state.trading_signals if s['status'] == 'PENDING'])
    executed_signals = len([s for s in st.session_state.trading_signals if s['status'] in ['EXECUTED', 'AUTO_EXECUTED']])
    auto_executed_signals = len([s for s in st.session_state.trading_signals if s['status'] == 'AUTO_EXECUTED'])
    cancelled_signals = len([s for s in st.session_state.trading_signals if s['status'] == 'CANCELLED'])

    col1, col2, col3, col4, col5, col6 = st.columns(6)

    with col1:
        st.metric("总信号数", total_signals)

    with col2:
        st.metric("待执行", pending_signals, delta=f"+{pending_signals}")

    with col3:
        st.metric("已执行", executed_signals)

    with col4:
        st.metric("🤖 自动执行", auto_executed_signals, delta=f"+{auto_executed_signals}")

    with col5:
        st.metric("已取消", cancelled_signals)

    with col6:
        success_rate = (executed_signals / max(total_signals, 1)) * 100
        st.metric("执行率", f"{success_rate:.1f}%")

    # 信号过滤和搜索
    st.subheader("🔍 信号筛选")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        status_filter = st.selectbox(
            "状态筛选",
            ["全部", "PENDING", "EXECUTED", "AUTO_EXECUTED", "CANCELLED"],
            key="status_filter"
        )

    with col2:
        strategy_filter = st.selectbox(
            "策略筛选",
            ["全部"] + list(set([s['strategy'] for s in st.session_state.trading_signals])),
            key="strategy_filter"
        )

    with col3:
        risk_filter = st.selectbox(
            "风险等级",
            ["全部", "LOW", "MEDIUM", "HIGH", "EXTREME"],
            key="risk_filter"
        )

    with col4:
        signal_type_filter = st.selectbox(
            "信号类型",
            ["全部", "BUY", "SELL", "HOLD"],
            key="signal_type_filter"
        )

    # 应用过滤器
    filtered_signals = st.session_state.trading_signals.copy()

    if status_filter != "全部":
        filtered_signals = [s for s in filtered_signals if s['status'] == status_filter]

    if strategy_filter != "全部":
        filtered_signals = [s for s in filtered_signals if s['strategy'] == strategy_filter]

    if risk_filter != "全部":
        filtered_signals = [s for s in filtered_signals if s['risk_level'] == risk_filter]

    if signal_type_filter != "全部":
        filtered_signals = [s for s in filtered_signals if s['signal_type'] == signal_type_filter]

    # 排序选项
    col1, col2 = st.columns(2)
    with col1:
        sort_by = st.selectbox(
            "排序方式",
            ["时间", "置信度", "预期收益", "风险等级"],
            key="sort_by"
        )

    with col2:
        sort_order = st.selectbox(
            "排序顺序",
            ["降序", "升序"],
            key="sort_order"
        )

    # 应用排序
    if sort_by == "时间":
        filtered_signals.sort(key=lambda x: x['timestamp'], reverse=(sort_order == "降序"))
    elif sort_by == "置信度":
        filtered_signals.sort(key=lambda x: x['confidence'], reverse=(sort_order == "降序"))
    elif sort_by == "预期收益":
        filtered_signals.sort(key=lambda x: x['expected_return'], reverse=(sort_order == "降序"))
    elif sort_by == "风险等级":
        risk_order = {"LOW": 1, "MEDIUM": 2, "HIGH": 3, "EXTREME": 4}
        filtered_signals.sort(key=lambda x: risk_order.get(x['risk_level'], 0), reverse=(sort_order == "降序"))

    # 显示过滤结果
    st.subheader(f"📋 信号列表 (共 {len(filtered_signals)} 条)")

    if not filtered_signals:
        st.info("没有符合条件的信号")
        return

    # 分页显示
    signals_per_page = 10
    total_pages = (len(filtered_signals) + signals_per_page - 1) // signals_per_page

    if total_pages > 1:
        page = st.selectbox(f"页面 (共 {total_pages} 页)", range(1, total_pages + 1)) - 1
        start_idx = page * signals_per_page
        end_idx = min(start_idx + signals_per_page, len(filtered_signals))
        page_signals = filtered_signals[start_idx:end_idx]
    else:
        page_signals = filtered_signals

    # 显示信号详情
    for i, signal in enumerate(page_signals):
        with st.expander(
            f"🎯 {signal['id']} - {signal['strategy']} - {signal['signal_type']} {signal['symbol']} "
            f"(置信度: {signal['confidence']:.1%})",
            expanded=False
        ):
            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown("### 📊 基本信息")
                st.write(f"**信号ID**: {signal['id']}")
                st.write(f"**生成时间**: {signal['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
                st.write(f"**策略**: {signal['strategy']}")
                st.write(f"**股票代码**: {signal['symbol']}")
                st.write(f"**信号类型**: {signal['signal_type']}")
                # 状态显示，特殊处理自动执行
                if signal['status'] == 'AUTO_EXECUTED':
                    st.write(f"**状态**: 🤖 自动执行")
                    if 'execution_time' in signal:
                        st.write(f"**执行时间**: {signal['execution_time'].strftime('%Y-%m-%d %H:%M:%S')}")
                else:
                    st.write(f"**状态**: {signal['status']}")

                st.write(f"**交易模式**: {signal['trading_mode']}")

            with col2:
                st.markdown("### 💰 交易参数")
                st.write(f"**入场价格**: ¥{signal['entry_price']}")
                if signal['target_price']:
                    st.write(f"**目标价格**: ¥{signal['target_price']}")
                if signal['stop_loss']:
                    st.write(f"**止损价格**: ¥{signal['stop_loss']}")
                st.write(f"**仓位大小**: {signal['position_size']:.1%}")
                st.write(f"**预期收益**: {signal['expected_return']:.2%}")
                st.write(f"**置信度**: {signal['confidence']:.1%}")

            with col3:
                st.markdown("### 🛡️ 风险管理")

                # 风险等级显示
                risk_color = {
                    'LOW': '🟢',
                    'MEDIUM': '🟡',
                    'HIGH': '🟠',
                    'EXTREME': '🔴'
                }
                st.write(f"**风险等级**: {risk_color.get(signal['risk_level'], '⚪')} {signal['risk_level']}")

                # 计算风险收益比
                if signal['signal_type'] in ['BUY', 'SELL'] and signal['stop_loss']:
                    if signal['signal_type'] == 'BUY':
                        risk = abs(signal['entry_price'] - signal['stop_loss']) / signal['entry_price']
                    else:
                        risk = abs(signal['stop_loss'] - signal['entry_price']) / signal['entry_price']

                    risk_reward_ratio = signal['expected_return'] / risk if risk > 0 else 0
                    st.write(f"**风险收益比**: {risk_reward_ratio:.2f}")

                # 操作按钮
                if signal['status'] == 'PENDING':
                    col_btn1, col_btn2 = st.columns(2)
                    with col_btn1:
                        if st.button(f"✅ 执行", key=f"exec_{signal['id']}"):
                            signal['status'] = 'EXECUTED'
                            signal['execution_time'] = datetime.now()
                            signal['execution_type'] = 'MANUAL'
                            st.success("信号已手动执行")
                            st.rerun()

                    with col_btn2:
                        if st.button(f"❌ 取消", key=f"cancel_{signal['id']}"):
                            signal['status'] = 'CANCELLED'
                            st.warning("信号已取消")
                            st.rerun()
                elif signal['status'] == 'AUTO_EXECUTED':
                    st.success("🤖 此信号已自动执行")
                    if 'notes' in signal and signal['notes']:
                        st.info(f"📝 {signal['notes']}")
                elif signal['status'] == 'EXECUTED':
                    st.success("✅ 此信号已手动执行")
                elif signal['status'] == 'CANCELLED':
                    st.warning("❌ 此信号已取消")

                # 备注
                if signal['notes']:
                    st.write(f"**备注**: {signal['notes']}")

    # 批量操作
    if len([s for s in filtered_signals if s['status'] == 'PENDING']) > 0:
        st.subheader("🔧 批量操作")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("✅ 执行所有待处理信号"):
                count = 0
                for signal in filtered_signals:
                    if signal['status'] == 'PENDING':
                        signal['status'] = 'EXECUTED'
                        count += 1
                if count > 0:
                    st.success(f"已执行 {count} 个信号")
                    st.rerun()

        with col2:
            if st.button("❌ 取消所有待处理信号"):
                count = 0
                for signal in filtered_signals:
                    if signal['status'] == 'PENDING':
                        signal['status'] = 'CANCELLED'
                        count += 1
                if count > 0:
                    st.warning(f"已取消 {count} 个信号")
                    st.rerun()

        with col3:
            if st.button("🗑️ 清空历史信号"):
                st.session_state.trading_signals = [s for s in st.session_state.trading_signals if s['status'] == 'PENDING']
                st.info("历史信号已清空")
                st.rerun()

    # 信号分析图表
    if len(st.session_state.trading_signals) > 0:
        st.subheader("📊 信号分析图表")

        col1, col2 = st.columns(2)

        with col1:
            # 信号状态分布饼图
            status_counts = {}
            for signal in st.session_state.trading_signals:
                status = signal['status']
                status_counts[status] = status_counts.get(status, 0) + 1

            fig_status = go.Figure(data=[
                go.Pie(
                    labels=list(status_counts.keys()),
                    values=list(status_counts.values()),
                    hole=0.4,
                    marker_colors=['#ff6b6b', '#4ecdc4', '#45b7d1']
                )
            ])
            fig_status.update_layout(title="信号状态分布", height=300)
            st.plotly_chart(fig_status, use_container_width=True)

        with col2:
            # 策略信号数量柱状图
            strategy_counts = {}
            for signal in st.session_state.trading_signals:
                strategy = signal['strategy']
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

            fig_strategy = go.Figure(data=[
                go.Bar(
                    x=list(strategy_counts.keys()),
                    y=list(strategy_counts.values()),
                    marker_color='#96ceb4'
                )
            ])
            fig_strategy.update_layout(title="策略信号分布", height=300)
            st.plotly_chart(fig_strategy, use_container_width=True)

def main():
    """主函数"""
    create_header()
    create_sidebar()
    
    # 主要内容区域
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📊 系统概览",
        "📈 实时监控",
        "💼 持仓监控",
        "🚨 机会警报",
        "🎯 交易信号"
    ])

    with tab1:
        create_overview_dashboard()

    with tab2:
        create_realtime_monitoring()

    with tab3:
        create_position_monitoring()

    with tab4:
        create_opportunity_alerts()

    with tab5:
        create_trading_signals()
    
    # 自动刷新 - 实时更新数据
    if st.session_state.running:
        # 更新实时数据
        update_realtime_data()
        time.sleep(3)
        st.rerun()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>🚀 统一量化套利系统 v5.0 | 简化演示版本</p>
        <p>📈 实时监控 | 🛡️ 风险控制 | ⚡ 临时套利</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
