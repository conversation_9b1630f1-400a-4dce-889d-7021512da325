"""
简化版统一量化套利系统界面
避免复杂导入，使用模拟数据演示功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time

# 页面配置
st.set_page_config(
    page_title="统一量化套利系统",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-running {
        color: #28a745;
        font-weight: bold;
    }
    .status-stopped {
        color: #dc3545;
        font-weight: bold;
    }
    .emergency-alert {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'running' not in st.session_state:
    st.session_state.running = False
    st.session_state.alerts = []
    st.session_state.emergency_mode = False
    st.session_state.total_capital = 200000  # 总资金
    st.session_state.position_ratio = 0.7    # 仓位比例
    st.session_state.daily_return = 2350     # 今日收益
    st.session_state.return_rate = 0.0118    # 收益率

def create_header():
    """创建页面头部"""
    st.markdown("""
    <div class="main-header">
        <h1>🚀 统一量化套利系统 v5.0</h1>
        <p>实时监控 | 风险控制 | 动态资金池 | 数据源管理 | 一体化解决方案</p>
    </div>
    """, unsafe_allow_html=True)

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.title("🎛️ 系统控制")
        
        # 系统状态
        if st.session_state.running:
            st.markdown('<p class="status-running">🟢 系统运行中</p>', unsafe_allow_html=True)
            if st.button("⏹️ 停止系统", use_container_width=True):
                st.session_state.running = False
                st.success("系统已停止")
                st.rerun()
        else:
            st.markdown('<p class="status-stopped">🔴 系统已停止</p>', unsafe_allow_html=True)
            if st.button("🚀 启动系统", use_container_width=True):
                st.session_state.running = True
                st.success("系统已启动")
                st.rerun()
        
        # 快速操作
        st.markdown("---")
        st.subheader("⚡ 快速操作")
        
        if st.button("🔍 强制扫描", use_container_width=True):
            if st.session_state.running:
                st.success("发现 3 个新机会")
                # 添加模拟警报
                new_alert = {
                    'strategy': '配对交易',
                    'symbols': ['000001.SZ', '000002.SZ'],
                    'confidence': 0.85,
                    'return': 0.025,
                    'time': datetime.now(),
                    'emergency': False
                }
                st.session_state.alerts.append(new_alert)
            else:
                st.warning("请先启动系统")
        
        if st.button("📊 生成报告", use_container_width=True):
            st.info("报告生成功能开发中...")
        
        # 系统模式
        st.markdown("---")
        st.subheader("🎯 系统模式")
        
        mode = st.selectbox(
            "选择运行模式",
            ["标准模式", "保守模式", "激进模式", "临时套利模式"],
            index=0
        )
        
        if mode == "临时套利模式":
            st.warning("⚠️ 临时套利模式风险较高")
            if st.button("🚨 确认进入临时模式"):
                st.session_state.emergency_mode = True
                st.success("已进入临时套利模式")
        
        # 资金设置
        st.markdown("---")
        st.subheader("💰 资金设置")

        # 使用会话状态保存资金设置
        new_capital = st.number_input(
            "当前资金 (元)",
            min_value=10000,
            max_value=10000000,
            value=st.session_state.total_capital,
            step=10000,
            key="capital_input"
        )

        new_position_ratio = st.slider(
            "仓位比例",
            min_value=0.1,
            max_value=0.85,
            value=st.session_state.position_ratio,
            step=0.05,
            key="position_input"
        )

        # 检查是否有变化并更新会话状态
        capital_changed = False
        position_changed = False

        if new_capital != st.session_state.total_capital:
            old_capital = st.session_state.total_capital
            st.session_state.total_capital = new_capital
            # 重新计算相关数值
            st.session_state.daily_return = int(new_capital * st.session_state.return_rate)
            capital_changed = True
            st.success(f"💰 总资金已更新: ¥{old_capital:,} → ¥{new_capital:,}")

        if new_position_ratio != st.session_state.position_ratio:
            old_ratio = st.session_state.position_ratio
            st.session_state.position_ratio = new_position_ratio
            position_changed = True
            st.success(f"📊 仓位比例已更新: {old_ratio:.1%} → {new_position_ratio:.1%}")

        # 如果有变化，延迟刷新以显示提示信息
        if capital_changed or position_changed:
            time.sleep(1)
            st.rerun()

        # 显示计算结果
        used_capital = int(st.session_state.total_capital * st.session_state.position_ratio)
        available_capital = st.session_state.total_capital - used_capital

        st.markdown("---")
        st.markdown("### 📊 资金分配")
        st.write(f"💰 总资金: ¥{st.session_state.total_capital:,}")
        st.write(f"📈 已使用: ¥{used_capital:,} ({st.session_state.position_ratio:.1%})")
        st.write(f"💵 可用资金: ¥{available_capital:,}")
        st.write(f"🎯 今日收益: ¥{st.session_state.daily_return:,}")

def create_overview_dashboard():
    """创建概览仪表板"""
    st.subheader("📊 系统概览")

    # 计算实时资金数据
    total_capital = st.session_state.total_capital
    used_capital = int(total_capital * st.session_state.position_ratio)
    available_capital = total_capital - used_capital
    daily_return = st.session_state.daily_return
    return_rate = st.session_state.return_rate

    # 关键指标
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.metric(
            "总资金",
            f"¥{total_capital:,}",
            delta="0%"
        )

    with col2:
        available_pct = (available_capital / total_capital) * 100
        st.metric(
            "可用资金",
            f"¥{available_capital:,}",
            delta=f"-{100-available_pct:.0f}%"
        )

    with col3:
        st.metric(
            "今日收益",
            f"¥{daily_return:,}",
            delta=f"+{return_rate:.2%}"
        )

    with col4:
        st.metric("活跃机会", str(len(st.session_state.alerts)), delta="+3")

    with col5:
        mode_text = "临时模式" if st.session_state.emergency_mode else "正常模式"
        st.metric("系统模式", mode_text, delta="正常")
    
    # 图表
    col1, col2 = st.columns(2)
    
    with col1:
        # 资金分配饼图 - 使用实时数据
        risk_margin = int(total_capital * 0.1)  # 10%风险保证金
        actual_available = available_capital - risk_margin

        fig_capital = go.Figure(data=[
            go.Pie(
                labels=['已使用资金', '可用资金', '风险保证金'],
                values=[used_capital, actual_available, risk_margin],
                hole=0.4,
                marker_colors=['#ff6b6b', '#4ecdc4', '#45b7d1'],
                textinfo='label+percent+value',
                texttemplate='%{label}<br>¥%{value:,}<br>(%{percent})'
            )
        ])
        fig_capital.update_layout(
            title=f"资金分配情况 (总计: ¥{total_capital:,})",
            height=300,
            showlegend=True
        )
        st.plotly_chart(fig_capital, use_container_width=True)
    
    with col2:
        # 策略收益对比
        strategies = ['配对交易', '统计套利', 'ML增强', '波动率套利', '临时套利']
        returns = [0.023, 0.018, 0.032, 0.028, 0.045]
        
        fig_strategy = go.Figure(data=[
            go.Bar(
                x=strategies,
                y=returns,
                marker_color=['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
            )
        ])
        fig_strategy.update_layout(title="策略收益对比", yaxis_title="收益率", height=300)
        st.plotly_chart(fig_strategy, use_container_width=True)

    # 资金使用详情
    st.subheader("💰 资金使用详情")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("### 📊 仓位分析")
        st.metric("当前仓位", f"{st.session_state.position_ratio:.1%}")
        st.metric("已使用资金", f"¥{used_capital:,}")

        # 仓位安全性评估
        if st.session_state.position_ratio <= 0.5:
            st.success("🟢 仓位安全")
        elif st.session_state.position_ratio <= 0.7:
            st.warning("🟡 仓位适中")
        else:
            st.error("🔴 仓位较高")

    with col2:
        st.markdown("### 💵 可用资金")
        st.metric("可用资金", f"¥{available_capital:,}")
        st.metric("风险保证金", f"¥{risk_margin:,}")

        # 资金充足性评估
        available_ratio = available_capital / total_capital
        if available_ratio >= 0.3:
            st.success("🟢 资金充足")
        elif available_ratio >= 0.2:
            st.warning("🟡 资金适中")
        else:
            st.error("🔴 资金紧张")

    with col3:
        st.markdown("### 📈 收益分析")
        st.metric("今日收益", f"¥{daily_return:,}")
        st.metric("收益率", f"{return_rate:.2%}")

        # 收益表现评估
        if return_rate >= 0.02:
            st.success("🟢 收益优秀")
        elif return_rate >= 0.01:
            st.info("🔵 收益良好")
        elif return_rate >= 0:
            st.warning("🟡 收益一般")
        else:
            st.error("🔴 出现亏损")

def create_realtime_monitoring():
    """创建实时监控面板"""
    st.subheader("📈 实时市场监控")
    
    # 模拟股票数据
    symbols = ['000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '600000.SH', '600036.SH']
    
    market_data = []
    for symbol in symbols:
        price = 10 + np.random.normal(0, 2)
        change = np.random.normal(0, 0.02)
        volume = np.random.randint(1000000, 10000000)
        
        market_data.append({
            '股票代码': symbol,
            '当前价格': f"¥{price:.2f}",
            '涨跌幅': f"{change:.2%}",
            '成交量': f"{volume:,}",
            '成交额': f"¥{price * volume / 10000:.0f}万",
            '状态': '正常' if abs(change) < 0.03 else '异常'
        })
    
    df = pd.DataFrame(market_data)
    st.dataframe(df, use_container_width=True)
    
    # 价格走势图
    st.subheader("📊 价格走势")
    
    dates = pd.date_range(start=datetime.now() - timedelta(hours=6), end=datetime.now(), freq='5min')
    
    fig = go.Figure()
    
    for symbol in symbols[:3]:
        prices = 10 + np.cumsum(np.random.normal(0, 0.1, len(dates)))
        fig.add_trace(go.Scatter(
            x=dates,
            y=prices,
            mode='lines',
            name=symbol,
            line=dict(width=2)
        ))
    
    fig.update_layout(
        title="主要股票价格走势",
        xaxis_title="时间",
        yaxis_title="价格 (¥)",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_opportunity_alerts():
    """创建机会警报面板"""
    st.subheader("🚨 套利机会警报")
    
    # 初始化示例警报
    if not st.session_state.alerts:
        sample_alerts = [
            {
                'strategy': '配对交易',
                'symbols': ['000001.SZ', '000002.SZ'],
                'confidence': 0.85,
                'return': 0.025,
                'time': datetime.now() - timedelta(minutes=5),
                'emergency': False
            },
            {
                'strategy': '临时套利',
                'symbols': ['600519.SH'],
                'confidence': 0.92,
                'return': 0.045,
                'time': datetime.now() - timedelta(minutes=2),
                'emergency': True
            }
        ]
        st.session_state.alerts = sample_alerts
    
    # 警报统计
    col1, col2, col3, col4 = st.columns(4)
    
    total_alerts = len(st.session_state.alerts)
    emergency_alerts = sum(1 for alert in st.session_state.alerts if alert['emergency'])
    high_confidence = sum(1 for alert in st.session_state.alerts if alert['confidence'] > 0.8)
    avg_return = np.mean([alert['return'] for alert in st.session_state.alerts]) if st.session_state.alerts else 0
    
    with col1:
        st.metric("总警报数", total_alerts)
    
    with col2:
        st.metric("紧急警报", emergency_alerts, delta=f"+{emergency_alerts}")
    
    with col3:
        st.metric("高置信度", high_confidence)
    
    with col4:
        st.metric("平均预期收益", f"{avg_return:.2%}")
    
    # 警报列表
    st.subheader("📋 最新警报")
    
    for i, alert in enumerate(reversed(st.session_state.alerts[-10:])):
        if alert['emergency']:
            alert_type = "🚨 紧急套利"
            container_class = "emergency-alert"
        elif alert['confidence'] > 0.8:
            alert_type = "⭐ 高置信度"
            container_class = "alert-card"
        else:
            alert_type = "💡 一般机会"
            container_class = "alert-card"
        
        with st.container():
            col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
            
            with col1:
                st.markdown(f"**{alert_type}**: {alert['strategy']}")
                st.caption(f"交易对: {' / '.join(alert['symbols'])}")
            
            with col2:
                st.markdown(f"置信度: **{alert['confidence']:.1%}**")
                st.caption(f"预期收益: {alert['return']:.2%}")
            
            with col3:
                time_diff = (datetime.now() - alert['time']).seconds
                st.caption(f"{time_diff}秒前")
            
            with col4:
                if st.button(f"执行", key=f"execute_{i}"):
                    st.success("信号已执行")

def main():
    """主函数"""
    create_header()
    create_sidebar()
    
    # 主要内容区域
    tab1, tab2, tab3 = st.tabs([
        "📊 系统概览", 
        "📈 实时监控", 
        "🚨 机会警报"
    ])
    
    with tab1:
        create_overview_dashboard()
    
    with tab2:
        create_realtime_monitoring()
    
    with tab3:
        create_opportunity_alerts()
    
    # 自动刷新
    if st.session_state.running:
        time.sleep(3)
        st.rerun()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>🚀 统一量化套利系统 v5.0 | 简化演示版本</p>
        <p>📈 实时监控 | 🛡️ 风险控制 | ⚡ 临时套利</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
