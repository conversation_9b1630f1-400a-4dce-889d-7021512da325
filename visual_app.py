"""
量化套利系统可视化应用
集成系统概览、实时过程监控和交易信号展示
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import time
import threading

# 页面配置
st.set_page_config(
    page_title="量化套利系统",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
    }
    .signal-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #28a745;
    }
    .stTabs [data-baseweb="tab-list"] {
        gap: 2px;
    }
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding-left: 20px;
        padding-right: 20px;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'signals' not in st.session_state:
    st.session_state.signals = []
if 'running' not in st.session_state:
    st.session_state.running = False
if 'metrics' not in st.session_state:
    st.session_state.metrics = {
        'total_signals': 0,
        'active_positions': 0,
        'total_pnl': 0.0,
        'daily_pnl': 0.0,
        'win_rate': 0.0
    }

def create_main_header():
    """创建主标题"""
    st.markdown("""
    <div class="main-header">
        <h1>🚀 量化套利系统仪表板</h1>
        <p>基于随机微积分方程的沪深股市实时交易监控系统</p>
    </div>
    """, unsafe_allow_html=True)

def create_system_overview():
    """系统概览页面"""
    st.header("📊 系统概览")
    
    # 关键指标
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric(
            "总信号数", 
            st.session_state.metrics['total_signals'],
            delta=f"+{np.random.randint(0, 3)}"
        )
    
    with col2:
        st.metric(
            "活跃持仓", 
            st.session_state.metrics['active_positions'],
            delta=f"+{np.random.randint(-1, 2)}"
        )
    
    with col3:
        st.metric(
            "总盈亏", 
            f"¥{st.session_state.metrics['total_pnl']:,.0f}",
            delta=f"{st.session_state.metrics['daily_pnl']:+,.0f}"
        )
    
    with col4:
        st.metric(
            "胜率", 
            f"{st.session_state.metrics['win_rate']:.1%}",
            delta=f"{np.random.uniform(-0.02, 0.02):+.1%}"
        )
    
    with col5:
        current_time = datetime.now().strftime("%H:%M:%S")
        status = "🟢 运行中" if st.session_state.running else "🔴 已停止"
        st.metric("系统状态", status)
        st.caption(f"更新时间: {current_time}")
    
    # 图表区域
    col1, col2 = st.columns(2)
    
    with col1:
        create_pnl_chart()
    
    with col2:
        create_performance_radar()

def create_pnl_chart():
    """创建盈亏图表"""
    st.subheader("📈 盈亏曲线")
    
    # 生成模拟数据
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
    
    # 模拟累计盈亏（带趋势）
    base_return = 0.001  # 日均收益率
    volatility = 0.02    # 波动率
    
    daily_returns = np.random.normal(base_return, volatility, len(dates))
    cumulative_pnl = np.cumsum(daily_returns) * 1000000  # 假设100万本金
    daily_pnl = daily_returns * 1000000
    
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('累计盈亏曲线', '日盈亏分布'),
        vertical_spacing=0.15,
        row_heights=[0.7, 0.3]
    )
    
    # 累计盈亏线
    fig.add_trace(
        go.Scatter(
            x=dates, 
            y=cumulative_pnl,
            mode='lines',
            name='累计盈亏',
            line=dict(color='#1f77b4', width=3),
            fill='tonexty',
            fillcolor='rgba(31, 119, 180, 0.1)'
        ),
        row=1, col=1
    )
    
    # 添加零线
    fig.add_hline(y=0, line_dash="dash", line_color="gray", row=1, col=1)
    
    # 日盈亏柱状图
    colors = ['#d62728' if x < 0 else '#2ca02c' for x in daily_pnl]
    fig.add_trace(
        go.Bar(
            x=dates, 
            y=daily_pnl,
            name='日盈亏',
            marker_color=colors,
            opacity=0.7
        ),
        row=2, col=1
    )
    
    fig.update_layout(
        height=500,
        showlegend=False,
        title_text="盈亏分析",
        title_x=0.5
    )
    
    fig.update_xaxes(title_text="日期", row=2, col=1)
    fig.update_yaxes(title_text="累计盈亏 (¥)", row=1, col=1)
    fig.update_yaxes(title_text="日盈亏 (¥)", row=2, col=1)
    
    st.plotly_chart(fig, use_container_width=True)

def create_performance_radar():
    """创建性能雷达图"""
    st.subheader("🎯 性能指标")
    
    # 模拟性能指标
    categories = ['收益率', '夏普比率', '最大回撤', '胜率', '波动率', '信息比率']
    
    # 当前表现 (标准化到0-1)
    current_values = [
        0.75,  # 收益率
        0.68,  # 夏普比率
        0.85,  # 最大回撤 (越小越好，这里是1-回撤率)
        0.72,  # 胜率
        0.60,  # 波动率 (适中为好)
        0.78   # 信息比率
    ]
    
    # 基准表现
    benchmark_values = [0.5] * len(categories)
    
    fig = go.Figure()
    
    # 当前表现
    fig.add_trace(go.Scatterpolar(
        r=current_values,
        theta=categories,
        fill='toself',
        name='当前表现',
        line_color='#1f77b4',
        fillcolor='rgba(31, 119, 180, 0.2)'
    ))
    
    # 基准线
    fig.add_trace(go.Scatterpolar(
        r=benchmark_values,
        theta=categories,
        fill='toself',
        name='基准水平',
        line_color='#ff7f0e',
        fillcolor='rgba(255, 127, 14, 0.1)'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1],
                tickvals=[0.2, 0.4, 0.6, 0.8, 1.0],
                ticktext=['20%', '40%', '60%', '80%', '100%']
            )
        ),
        showlegend=True,
        height=400,
        title="性能雷达图"
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 详细指标
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("年化收益率", "15.3%", "↑2.1%")
    with col2:
        st.metric("夏普比率", "1.68", "↑0.12")
    with col3:
        st.metric("最大回撤", "-3.2%", "↓0.8%")

def create_market_overview():
    """市场概览页面"""
    st.header("🏭 市场概览")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        create_industry_heatmap()
    
    with col2:
        create_market_sentiment()

def create_industry_heatmap():
    """行业热力图"""
    st.subheader("🔥 行业表现热力图")
    
    # 行业数据
    industries = [
        '银行', '证券', '保险', '地产', '科技',
        '医药', '消费', '制造', '能源', '通信',
        '军工', '农业', '环保', '传媒', '汽车'
    ]
    
    # 生成随机涨跌幅数据
    np.random.seed(42)  # 固定种子以保持一致性
    changes = np.random.normal(0, 2.5, 15)
    
    # 创建5x3网格
    z_data = changes.reshape(5, 3)
    
    # 创建热力图
    fig = go.Figure(data=go.Heatmap(
        z=z_data,
        x=['板块A', '板块B', '板块C'],
        y=industries,
        colorscale='RdYlGn',
        zmid=0,
        text=[[f'{z_data[i][j]:.1f}%' for j in range(3)] for i in range(5)],
        texttemplate="%{text}",
        textfont={"size": 12},
        hoverongaps=False,
        colorbar=dict(title="涨跌幅 (%)")
    ))
    
    fig.update_layout(
        title="实时行业涨跌幅",
        height=400,
        xaxis_title="板块分类",
        yaxis_title="行业"
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_market_sentiment():
    """市场情绪"""
    st.subheader("📊 市场情绪")
    
    # 模拟市场数据
    total_stocks = 4500
    rising = np.random.randint(1800, 2200)
    falling = np.random.randint(1600, 2000)
    unchanged = total_stocks - rising - falling
    
    # 饼图
    fig = go.Figure(data=[go.Pie(
        labels=['上涨', '下跌', '平盘'],
        values=[rising, falling, unchanged],
        hole=.4,
        marker_colors=['#2ca02c', '#d62728', '#808080'],
        textinfo='label+percent',
        textfont_size=12
    )])
    
    fig.update_layout(
        title="股票涨跌分布",
        height=300,
        showlegend=True,
        annotations=[dict(text=f'总计<br>{total_stocks}只', x=0.5, y=0.5, font_size=16, showarrow=False)]
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 市场指标
    st.markdown("### 📈 市场指标")
    
    col1, col2 = st.columns(2)
    with col1:
        st.metric("总成交额", f"¥{np.random.randint(8000, 12000):,}亿")
        st.metric("平均涨幅", f"{np.random.normal(0, 1):.2f}%")
    
    with col2:
        st.metric("换手率", f"{np.random.uniform(1.5, 3.0):.1f}%")
        st.metric("市场活跃度", f"{np.random.uniform(0.6, 0.9):.1%}")

def create_trading_signals():
    """交易信号页面"""
    st.header("🎯 实时交易信号")
    
    # 控制面板
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        signal_filter = st.selectbox(
            "策略筛选",
            ["全部", "配对交易", "统计套利", "波动率套利", "趋势跟踪"]
        )
    
    with col2:
        confidence_filter = st.slider("置信度阈值", 0.0, 1.0, 0.6, 0.05)
    
    with col3:
        risk_filter = st.selectbox("风险等级", ["全部", "低", "中", "高"])
    
    with col4:
        auto_refresh = st.checkbox("自动刷新", value=True)
    
    # 生成模拟信号
    generate_mock_signals()
    
    # 过滤信号
    filtered_signals = filter_signals(signal_filter, confidence_filter, risk_filter)
    
    if filtered_signals:
        # 信号统计
        create_signal_stats(filtered_signals)
        
        # 信号列表
        create_signal_list(filtered_signals)
        
        # 信号图表
        create_signal_charts(filtered_signals)
    else:
        st.info("🔍 暂无符合条件的交易信号")
    
    # 自动刷新
    if auto_refresh and st.session_state.running:
        time.sleep(2)
        st.rerun()

def generate_mock_signals():
    """生成模拟交易信号"""
    if len(st.session_state.signals) < 100:
        strategies = ["配对交易", "统计套利", "波动率套利", "趋势跟踪"]
        signal_types = ["LONG_SHORT", "SHORT_LONG", "CLOSE"]
        risk_levels = ["低", "中", "高"]
        
        # 30%概率生成新信号
        if np.random.random() < 0.3:
            signal = {
                'timestamp': datetime.now(),
                'strategy': np.random.choice(strategies),
                'symbol1': f"{np.random.randint(100, 999):03d}{np.random.randint(100, 999):03d}.{'SZ' if np.random.random() < 0.5 else 'SH'}",
                'symbol2': f"{np.random.randint(100, 999):03d}{np.random.randint(100, 999):03d}.{'SZ' if np.random.random() < 0.5 else 'SH'}",
                'signal_type': np.random.choice(signal_types),
                'confidence': np.random.uniform(0.5, 1.0),
                'expected_return': np.random.uniform(0.005, 0.03),
                'risk_level': np.random.choice(risk_levels),
                'z_score': np.random.normal(0, 2),
                'price1': np.random.uniform(8, 50),
                'price2': np.random.uniform(8, 50)
            }
            st.session_state.signals.append(signal)
            st.session_state.metrics['total_signals'] += 1
    
    # 保持最近100个信号
    if len(st.session_state.signals) > 100:
        st.session_state.signals = st.session_state.signals[-100:]

def filter_signals(strategy_filter, confidence_filter, risk_filter):
    """过滤信号"""
    filtered = st.session_state.signals.copy()
    
    # 策略过滤
    if strategy_filter != "全部":
        filtered = [s for s in filtered if s['strategy'] == strategy_filter]
    
    # 置信度过滤
    filtered = [s for s in filtered if s['confidence'] >= confidence_filter]
    
    # 风险等级过滤
    if risk_filter != "全部":
        filtered = [s for s in filtered if s['risk_level'] == risk_filter]
    
    return filtered

def create_signal_stats(signals):
    """信号统计"""
    st.subheader("📊 信号统计")
    
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("信号总数", len(signals))
    
    with col2:
        long_count = len([s for s in signals if s['signal_type'] == 'LONG_SHORT'])
        st.metric("做多信号", long_count)
    
    with col3:
        short_count = len([s for s in signals if s['signal_type'] == 'SHORT_LONG'])
        st.metric("做空信号", short_count)
    
    with col4:
        avg_confidence = np.mean([s['confidence'] for s in signals]) if signals else 0
        st.metric("平均置信度", f"{avg_confidence:.2f}")
    
    with col5:
        avg_return = np.mean([s['expected_return'] for s in signals]) if signals else 0
        st.metric("平均预期收益", f"{avg_return:.2%}")

def create_signal_list(signals):
    """信号列表"""
    st.subheader("📋 信号详情")
    
    # 转换为DataFrame
    df_data = []
    for signal in signals[-20:]:  # 显示最近20个
        df_data.append({
            '时间': signal['timestamp'].strftime('%H:%M:%S'),
            '策略': signal['strategy'],
            '交易对': f"{signal['symbol1']} / {signal['symbol2']}",
            '信号': signal['signal_type'],
            '置信度': f"{signal['confidence']:.2f}",
            '预期收益': f"{signal['expected_return']:.2%}",
            '风险等级': signal['risk_level'],
            'Z-Score': f"{signal['z_score']:.2f}",
            '价格1': f"{signal['price1']:.2f}",
            '价格2': f"{signal['price2']:.2f}"
        })
    
    if df_data:
        df = pd.DataFrame(df_data)
        
        # 样式化表格
        def highlight_signal_type(val):
            if val == 'LONG_SHORT':
                return 'background-color: #d4edda; color: #155724'
            elif val == 'SHORT_LONG':
                return 'background-color: #f8d7da; color: #721c24'
            else:
                return 'background-color: #fff3cd; color: #856404'
        
        styled_df = df.style.applymap(highlight_signal_type, subset=['信号'])
        st.dataframe(styled_df, use_container_width=True, height=400)

def create_signal_charts(signals):
    """信号图表"""
    col1, col2 = st.columns(2)
    
    with col1:
        # 策略分布
        strategies = [s['strategy'] for s in signals]
        strategy_counts = pd.Series(strategies).value_counts()
        
        fig = px.pie(
            values=strategy_counts.values,
            names=strategy_counts.index,
            title="策略分布",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        fig.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 置信度分布
        confidences = [s['confidence'] for s in signals]
        
        fig = go.Figure(data=[go.Histogram(
            x=confidences,
            nbinsx=20,
            marker_color='skyblue',
            opacity=0.7
        )])
        
        fig.update_layout(
            title="置信度分布",
            xaxis_title="置信度",
            yaxis_title="频次",
            bargap=0.1
        )
        
        st.plotly_chart(fig, use_container_width=True)

def create_sidebar():
    """侧边栏"""
    with st.sidebar:
        st.header("🎛️ 控制中心")
        
        # 系统控制
        st.subheader("系统控制")
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🚀 启动", type="primary", use_container_width=True):
                st.session_state.running = True
                st.success("系统已启动")
        
        with col2:
            if st.button("⏹️ 停止", use_container_width=True):
                st.session_state.running = False
                st.success("系统已停止")
        
        st.markdown("---")
        
        # 系统参数
        st.subheader("⚙️ 系统参数")
        
        initial_capital = st.number_input(
            "初始资金 (万元)", 
            min_value=10, 
            max_value=1000, 
            value=100, 
            step=10
        )
        
        max_position = st.slider(
            "最大单股仓位", 
            0.05, 0.20, 0.10, 0.01,
            format="%.2f"
        )
        
        risk_tolerance = st.selectbox(
            "风险偏好", 
            ["保守", "平衡", "激进"]
        )
        
        st.markdown("---")
        
        # 实时状态
        st.subheader("📊 实时状态")
        
        if st.session_state.running:
            st.success("🟢 系统运行中")
            
            # 模拟实时更新指标
            st.session_state.metrics.update({
                'total_pnl': np.random.normal(15000, 2000),
                'daily_pnl': np.random.normal(500, 200),
                'win_rate': np.random.uniform(0.65, 0.75),
                'active_positions': np.random.randint(5, 15)
            })
        else:
            st.error("🔴 系统已停止")
        
        # 显示关键指标
        st.metric("总盈亏", f"¥{st.session_state.metrics['total_pnl']:,.0f}")
        st.metric("今日盈亏", f"¥{st.session_state.metrics['daily_pnl']:,.0f}")
        st.metric("胜率", f"{st.session_state.metrics['win_rate']:.1%}")
        
        st.markdown("---")
        
        # 快速操作
        st.subheader("⚡ 快速操作")
        
        if st.button("🔄 重置系统", use_container_width=True):
            st.session_state.signals = []
            st.session_state.metrics = {
                'total_signals': 0,
                'active_positions': 0,
                'total_pnl': 0.0,
                'daily_pnl': 0.0,
                'win_rate': 0.0
            }
            st.success("系统已重置")
        
        if st.button("📊 导出报告", use_container_width=True):
            st.info("报告导出功能开发中...")
        
        if st.button("⚙️ 高级设置", use_container_width=True):
            st.info("高级设置功能开发中...")

def main():
    """主函数"""
    # 创建侧边栏
    create_sidebar()
    
    # 主标题
    create_main_header()
    
    # 创建标签页
    tab1, tab2, tab3 = st.tabs([
        "📊 系统概览", 
        "�� 市场概览", 
        "🎯 交易信号"
    ])
    
    with tab1:
        create_system_overview()
    
    with tab2:
        create_market_overview()
    
    with tab3:
        create_trading_signals()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 1rem;'>
        <p>🚀 量化套利系统 v1.0 | 基于随机微积分方程 | 实时交易信号监控</p>
        <p>⚠️ 本系统仅供学习研究使用，投资有风险，入市需谨慎</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
